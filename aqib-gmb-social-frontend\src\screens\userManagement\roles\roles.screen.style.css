/* Role Cards Styling */
.roleCard {
    height: 100%;
    border-radius: 12px !important;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    border: 1px solid #f0f0f0;
}

.roleCard:hover {
    box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px);
}

.roleCardHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.roleInfo {
    flex: 1;
}

.roleTitle {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: var(--titleColor) !important;
    display: flex;
    align-items: center;
    margin-bottom: 8px !important;
}

.roleIndicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: var(--grayColor);
}

.roleIndicator.admin {
    background-color: var(--primaryColor);
}

.roleIndicator.manager {
    background-color: var(--positive);
}

.roleIndicator.user {
    background-color: var(--secondaryColor);
}

.roleStatusChip {
    margin-top: 4px !important;
}

.roleActions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.roleEditBtn {
    min-width: 80px !important;
    height: 32px !important;
    font-size: 12px !important;
    border-color: var(--primaryColor) !important;
    color: var(--primaryColor) !important;
}

.roleEditBtn:hover {
    background-color: var(--primaryColorAlpha) !important;
}

.roleCardDivider {
    margin: 16px 0 !important;
}

.rolePermissions {
    margin-top: 16px;
}

.permissionsLabel {
    font-size: 12px !important;
    font-weight: 600 !important;
    color: var(--grayColor) !important;
    margin-bottom: 8px !important;
}

.permissionsText {
    font-size: 13px !important;
    color: var(--secondaryTextColor) !important;
    line-height: 1.4 !important;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Permission Modal Styling */
.rolePermissionsModal .modal-modal-description {
    padding: 10px !important;
    /* max-height: calc(100vh - 200px); */
    overflow-y: auto;
}

.permissionCard {
    height: 100%;
    border-radius: 8px !important;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.permissionCard:hover {
    border-color: var(--primaryColor);
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
}

.permissionHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--primaryColorAlpha);
}

.permissionHeaderLeft {
    display: flex;
    align-items: center;
    flex: 1;
}

.permissionIcon {
    color: var(--tableHeader) !important;
    margin-right: 12px !important;
    font-size: 18px !important;
}

.permissionTitle {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--titleColor) !important;
}

.permissionToggle {
    margin: 0 !important;
}

.permissionToggle .MuiSwitch-root {
    margin: 0 !important;
}

.permissionContent {
    padding: 0;
    margin-top: 16px;
}

.permissionCheckboxGrid {
    margin: 0 !important;
    width: 100% !important;
}

.permissionCheckboxGrid .MuiGrid-item {
    padding: 4px 8px !important;
}

.permissionCheckbox {
    margin: 0 !important;
    width: 100%;
}

.permissionCheckbox .MuiFormControlLabel-label {
    font-size: 14px !important;
    color: var(--secondaryTextColor) !important;
    font-weight: 400 !important;
}

.permissionCheckbox.Mui-disabled .MuiFormControlLabel-label {
    color: var(--grayColor) !important;
}

.permissionCheckbox .MuiCheckbox-root {
    padding: 4px 8px 4px 0 !important;
}

/* Legacy styles for backward compatibility */
.permissionSwitch {
    margin-bottom: 16px !important;
}

.permissionSwitch .MuiFormControlLabel-label {
    font-weight: 500 !important;
    color: var(--titleColor) !important;
}

.permissionDivider {
    margin: 16px 0 !important;
}

.permissionCheckboxes {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.permissionCheckboxes .MuiFormControlLabel-label {
    font-size: 14px !important;
    color: var(--secondaryTextColor) !important;
}

.permissionCheckboxes .MuiFormControlLabel-root.Mui-disabled .MuiFormControlLabel-label {
    color: var(--grayColor) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .roleCardHeader {
        flex-direction: column;
        align-items: flex-start;
    }

    .roleActions {
        flex-direction: row;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        margin-top: 12px;
    }

    .rolePermissionsModal .modal-modal-description {
        padding: 16px !important;
    }

    .permissionCard {
        margin-bottom: 16px;
    }
}

/* Legacy styles for backward compatibility */
.commonRoleInput {
    margin: 0px 4px 28px;
}

.commonRoleInput .MuiFormGroup-root {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}