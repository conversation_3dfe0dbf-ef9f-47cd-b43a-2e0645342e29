import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
} from "@mui/material";
import { Warning as WarningIcon } from "@mui/icons-material";
import DeleteForeverOutlinedIcon from "@mui/icons-material/DeleteForeverOutlined";

interface ConfirmDeleteComponentProps {
  open: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmDeleteComponent: React.FC<ConfirmDeleteComponentProps> = ({
  open,
  title,
  message,
  onConfirm,
  onCancel,
}) => {
  return (
    <Dialog open={open} onClose={onCancel} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          {/* <WarningIcon color="warning" /> */}
          <Typography variant="h6" component="span">
            {title}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Typography variant="body1">{message}</Typography>
      </DialogContent>

      <DialogActions sx={{ justifyContent: "space-between", padding: 2 }}>
        <Button
          onClick={onCancel}
          variant="outlined"
          color="primary"
          sx={{
            borderRadius: "5px !important",
            textTransform: "none",
            "&.MuiButton-root": {
              borderRadius: "5px",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          color="error"
          startIcon={<DeleteForeverOutlinedIcon />}
          sx={{
            borderRadius: "5px !important",
            textTransform: "none",
            "&.MuiButton-root": {
              borderRadius: "5px",
            },
          }}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDeleteComponent;
