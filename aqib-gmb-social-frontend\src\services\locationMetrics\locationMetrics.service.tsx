import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  PERFORMANCE_LOCATIONMETRICS,
  PERFORMANCE_SEARCHKEYWORDS,
  PERFORMANCE_MULTI_LOCATION_ANALYTICS,
} from "../../constants/endPoints.constant";
import { Action } from "redux";
import { ILocationMetricsRequestModel } from "../../interfaces/request/ILocationMetricsRequestModel";

class LocationMetricsService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  getLocationMetrics = async (
    requestBody: ILocationMetricsRequestModel,
    headerObject: any
  ) => {
    return await this._httpHelperService.post(
      PERFORMANCE_LOCATIONMETRICS,
      requestBody,
      headerObject
    );
  };

  getSearchkeywords = async (
    requestBody: ILocationMetricsRequestModel,
    headerObject: any
  ) => {
    return await this._httpHelperService.post(
      PERFORMANCE_SEARCHKEYWORDS,
      requestBody,
      headerObject
    );
  };

  getMultiLocationAnalytics = async (requestBody: {
    locationIds: string[];
    startDate: string;
    endDate: string;
  }) => {
    return await this._httpHelperService.post(
      PERFORMANCE_MULTI_LOCATION_ANALYTICS,
      requestBody
    );
  };
}

export default LocationMetricsService;
