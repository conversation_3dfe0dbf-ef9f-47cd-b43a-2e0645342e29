import React, { useContext, useState } from "react";
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Stack,
  Rating,
  Card,
  CardContent,
  Divider,
} from "@mui/material";
import { Formik, Form } from "formik";
import * as yup from "yup";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../../../context/loading.context";
import { ToastContext } from "../../../../context/toast.context";
import { ToastSeverity } from "../../../../constants/toastSeverity.constant";
import ReviewSettingsService, {
  IReplyTemplate,
  ICreateReplyTemplateRequest,
  IUpdateReplyTemplateRequest,
} from "../../../../services/reviewSettings/reviewSettings.service";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import { display } from "html2canvas/dist/types/css/property-descriptors/display";

interface ICreateEditTemplateProps {
  template: IReplyTemplate | null;
  businessId: number | null;
  accountId?: string | null;
  locationId?: string | null;
  onClose: () => void;
}

const validationSchema = yup.object({
  templateName: yup
    .string()
    .required("Template name is required")
    .min(3, "Template name must be at least 3 characters")
    .max(255, "Template name must be less than 255 characters"),
  templateContent: yup
    .string()
    .required("Template content is required")
    .min(10, "Template content must be at least 10 characters")
    .max(1000, "Template content must be less than 1000 characters"),
  starRating: yup
    .number()
    .required("Star rating is required")
    .min(1, "Star rating must be between 1 and 5")
    .max(5, "Star rating must be between 1 and 5"),
  isDefault: yup.boolean(),
});

const CreateEditTemplateComponent: React.FunctionComponent<
  ICreateEditTemplateProps
> = ({ template, businessId, accountId, locationId, onClose }) => {
  const dispatch = useDispatch();
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);

  const _reviewSettingsService = new ReviewSettingsService(dispatch);

  const initialValues = {
    templateName: template?.template_name || "",
    templateContent: template?.template_content || "",
    starRating: template?.star_rating || 5,
    isDefault: template?.is_default || false,
  };

  const handleSubmit = async (values: any) => {
    if (!userInfo?.id) {
      setToastConfig(ToastSeverity.Error, "User not authenticated", true);
      return;
    }

    try {
      setLoading(true);

      if (template?.id) {
        // Update existing template
        const updateData: IUpdateReplyTemplateRequest = {
          starRating: values.starRating,
          templateName: values.templateName,
          templateContent: values.templateContent,
          isDefault: values.isDefault,
        };

        await _reviewSettingsService.updateReplyTemplate(
          userInfo.id,
          template.id,
          updateData
        );

        setToastConfig(
          ToastSeverity.Success,
          "Template updated successfully",
          true
        );
      } else {
        // Create new template
        const createData: ICreateReplyTemplateRequest = {
          starRating: values.starRating,
          templateName: values.templateName,
          templateContent: values.templateContent,
          isDefault: values.isDefault,
          businessId: businessId || undefined,
        };

        console.log("=== CREATE TEMPLATE DEBUG ===");
        console.log("Business ID:", businessId);
        console.log("Account ID:", accountId);
        console.log("Location ID:", locationId);
        console.log("Create Data:", createData);

        const createResponse = await _reviewSettingsService.createReplyTemplate(
          userInfo.id,
          createData,
          accountId || undefined,
          locationId || undefined
        );

        console.log("Template creation response:", createResponse);

        setToastConfig(
          ToastSeverity.Success,
          "Template created successfully",
          true
        );
      }

      onClose();
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        template?.id
          ? "Failed to update template"
          : "Failed to create template",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateAIText = async (
    starRating: number,
    setFieldValue: any
  ) => {
    try {
      setIsGeneratingAI(true);
      const response = await _reviewSettingsService.generateAIText(starRating);

      if (
        response.data?.generatedText &&
        typeof response.data.generatedText === "string"
      ) {
        setFieldValue("templateContent", response.data.generatedText);
        setToastConfig(
          ToastSeverity.Success,
          "AI text generated successfully!",
          true
        );
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Generated text is invalid. Please try again.",
          true
        );
      }
    } catch (error) {
      console.error("AI generation error:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to generate AI text. Please try again.",
        true
      );
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const getPreviewText = (content: string, rating: number) => {
    // Ensure content is a string and handle null/undefined cases
    if (!content || typeof content !== "string") {
      return "";
    }

    // Replace common placeholders with sample data
    return content
      .replace(/\{customerName\}/g, "John Doe")
      .replace(/\{businessName\}/g, "Your Business")
      .replace(/\{rating\}/g, rating.toString());
  };

  return (
    <Box className="height100" sx={{ p: 3}}>
      <Typography variant="h5" component="h2" sx={{ mb: 4 }}>
        {template?.id ? "Edit" : "Create"} Reply Template
      </Typography>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          setFieldValue,
        }) => (
          <Form id="template-form">
            <Stack spacing={3}>
              <TextField
                fullWidth
                name="templateName"
                label="Template Name"
                value={values.templateName}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.templateName && Boolean(errors.templateName)}
                helperText={
                  touched.templateName &&
                  typeof errors.templateName === "string"
                    ? errors.templateName
                    : undefined
                }
                placeholder="e.g., Thank You - 5 Star"
              />

              {template?.id ? (
                // Read-only star rating display for editing
                <Box>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 1 }}
                  >
                    Star Rating
                  </Typography>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      p: 2,
                      border: "1px solid",
                      borderColor: "divider",
                      borderRadius: 1,
                      backgroundColor: "grey.50",
                    }}
                  >
                    <Rating
                      value={values.starRating}
                      readOnly
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body1">
                      {values.starRating} Star
                      {values.starRating !== 1 ? "s" : ""}
                    </Typography>
                  </Box>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ mt: 0.5 }}
                  >
                    Star rating cannot be changed when editing a template
                  </Typography>
                </Box>
              ) : (
                // Editable star rating selection for creating new template
                <FormControl fullWidth>
                  <InputLabel>Star Rating</InputLabel>
                  <Select
                    name="starRating"
                    value={values.starRating}
                    label="Star Rating"
                    onChange={(e) =>
                      setFieldValue("starRating", e.target.value)
                    }
                    error={touched.starRating && Boolean(errors.starRating)}
                  >
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <MenuItem key={rating} value={rating}>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Rating
                            value={rating}
                            readOnly
                            size="small"
                            sx={{ mr: 1 }}
                          />
                          {rating} Star{rating !== 1 ? "s" : ""}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              <Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ flexGrow: 1 }}
                  >
                    Template Content
                  </Typography>
                  <Button
                    className="GenerateAITextBtn"
                    variant="outlined"
                    size="small"
                    onClick={() =>
                      handleGenerateAIText(values.starRating, setFieldValue)
                    }
                    disabled={isGeneratingAI}
                    startIcon={<AutoAwesomeIcon  style={{display:"block", marginRight:"-8px"}}/>}
                    sx={{ minHeight: "36px" }}
                  >
                    {isGeneratingAI ? "Generating..." : "Generate AI Text"}
                  </Button>
                </Box>
                <TextField
                  fullWidth
                  multiline
                  rows={6}
                  name="templateContent"
                  value={values.templateContent}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={
                    touched.templateContent && Boolean(errors.templateContent)
                  }
                  helperText={
                    touched.templateContent &&
                    typeof errors.templateContent === "string"
                      ? errors.templateContent
                      : "You can use placeholders like {customerName}, {businessName}, {rating}"
                  }
                  placeholder="Thank you for your review! We appreciate your feedback..."
                />
              </Box>

              <FormControlLabel
                control={
                  <Switch
                    checked={values.isDefault}
                    onChange={(e) =>
                      setFieldValue("isDefault", e.target.checked)
                    }
                    name="isDefault"
                  />
                }
                label="Set as default template for this star rating"
              />

              {values.templateContent && (
                <>
                  <Divider />
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Preview
                      </Typography>
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 2 }}
                      >
                        <Rating
                          value={values.starRating}
                          readOnly
                          size="small"
                        />
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {values.starRating} Star Review Response
                        </Typography>
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{ fontStyle: "italic", color: "text.secondary" }}
                      >
                        {getPreviewText(
                          values.templateContent,
                          values.starRating
                        )}
                      </Typography>
                    </CardContent>
                  </Card>
                </>
              )}
            </Stack>
          </Form>
        )}
      </Formik>

      <Box className="commonFooter" sx ={{mt:3,pb:2, display: "flex", justifyContent: "space-between", alignItems: "center"}}>
          <Button
            variant="outlined"
            className="CreateTemplateCancelBtn"
            onClick={onClose}
            sx={{
              minHeight: "46px",
            }}
            startIcon={<CancelOutlinedIcon />}
          >
            <span className="responsiveHide">Cancel</span>
          </Button>
          <Button
            type="submit"
            variant="contained"
            className="CreateTemplateBtn"
            form="template-form"
            sx={{
              minHeight: "46px",
            }}
            startIcon={
              template?.id ? <EditOutlinedIcon /> : <AddOutlinedIcon style={{display:"block", marginRight:"-8px"}}/>
            }
          >
            <span style={{display:"block", margin:"0px"}} className="responsiveHide">
              {template?.id ? "Update" : "Create"} Template
            </span>
          </Button>
        
      </Box>
    </Box>
  );
};

export default CreateEditTemplateComponent;
