const ReportsModel = require("../models/reports.models");
const ExcelJS = require("exceljs");

// Get Google Analytics data with filtering
const getGoogleAnalyticsData = async (req, res) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = req.user.userId;
    const { businessId, accountId, locationId, locationIds, fromDate, toDate } =
      req.body;

    // Handle both locationId (singular) and locationIds (plural) for backward compatibility
    const finalLocationIds = locationIds || (locationId ? [locationId] : []);

    // Validate required fields
    if (!businessId || !fromDate || !toDate) {
      return res.status(400).json({
        success: false,
        message: "Business ID, from date, and to date are required",
      });
    }

    // Validate date format and range
    const startDate = new Date(fromDate);
    const endDate = new Date(toDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({
        success: false,
        message: "Invalid date format. Please use YYYY-MM-DD format",
      });
    }

    if (startDate > endDate) {
      return res.status(400).json({
        success: false,
        message: "From date cannot be later than to date",
      });
    }

    if (endDate > new Date()) {
      return res.status(400).json({
        success: false,
        message: "To date cannot be in the future",
      });
    }

    const filters = {
      businessId,
      accountId,
      locationIds: finalLocationIds,
      fromDate,
      toDate,
    };

    const data = await ReportsModel.fetchGoogleAnalyticsData(userId, filters);

    res.json({
      success: true,
      data,
      filters,
      totalRecords: data.length,
    });
  } catch (error) {
    console.error("Error fetching Google Analytics data:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch Google Analytics data",
      error: error.message,
    });
  }
};

// Get Google Analytics charts data
const getGoogleAnalyticsCharts = async (req, res) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = req.user.userId;
    const { businessId, accountId, locationId, locationIds, fromDate, toDate } =
      req.body;

    // Handle both locationId (singular) and locationIds (plural) for backward compatibility
    const finalLocationIds = locationIds || (locationId ? [locationId] : []);

    // Validate required fields
    if (!businessId || !fromDate || !toDate) {
      return res.status(400).json({
        success: false,
        message: "Business ID, from date, and to date are required",
      });
    }

    const filters = {
      businessId,
      accountId,
      locationIds: finalLocationIds,
      fromDate,
      toDate,
    };

    console.log(
      "Google Analytics Controller - Received request body:",
      req.body
    );
    console.log(
      "Google Analytics Controller - Final locationIds:",
      finalLocationIds
    );
    console.log(
      "Google Analytics Controller - Filters being sent to model:",
      filters
    );

    const data = await ReportsModel.fetchGoogleAnalyticsData(userId, filters);
    const chartData = ReportsModel.generateAnalyticsCharts(
      data,
      fromDate,
      toDate
    );

    res.json({
      success: true,
      chartData,
      filters,
    });
  } catch (error) {
    console.error("Error generating Google Analytics charts:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate Google Analytics charts",
      error: error.message,
    });
  }
};

// Get Google Analytics summary statistics
const getGoogleAnalyticsSummary = async (req, res) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = req.user.userId;
    const { businessId, accountId, locationId, locationIds, fromDate, toDate } =
      req.body;

    // Handle both locationId (singular) and locationIds (plural) for backward compatibility
    const finalLocationIds = locationIds || (locationId ? [locationId] : []);

    // Validate required fields
    if (!businessId || !fromDate || !toDate) {
      return res.status(400).json({
        success: false,
        message: "Business ID, from date, and to date are required",
      });
    }

    const filters = {
      businessId,
      accountId,
      locationIds: finalLocationIds,
      fromDate,
      toDate,
    };

    const summary = await ReportsModel.getGoogleAnalyticsSummary(
      userId,
      filters
    );

    res.json({
      success: true,
      summary,
      filters,
    });
  } catch (error) {
    console.error("Error fetching Google Analytics summary:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch Google Analytics summary",
      error: error.message,
    });
  }
};

// Export Google Analytics report to Excel
const exportGoogleAnalyticsReport = async (req, res) => {
  try {
    if (!req.user || !req.user.userId) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated",
      });
    }

    const userId = req.user.userId;
    const { businessId, accountId, locationId, locationIds, fromDate, toDate } =
      req.body;

    // Handle both locationId (singular) and locationIds (plural) for backward compatibility
    const finalLocationIds = locationIds || (locationId ? [locationId] : []);

    // Validate required fields
    if (!businessId || !fromDate || !toDate) {
      return res.status(400).json({
        success: false,
        message: "Business ID, from date, and to date are required",
      });
    }

    const filters = {
      businessId,
      accountId,
      locationIds: finalLocationIds,
      fromDate,
      toDate,
    };

    const data = await ReportsModel.fetchGoogleAnalyticsForExport(
      userId,
      filters
    );

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Google Analytics Report");

    // Add headers
    const headers = [
      "Date",
      "Location",
      "Account",
      "Business",
      "Metric Type",
      "Metric Value",
    ];

    worksheet.addRow(headers);

    // Style headers
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Add data rows
    data.forEach((item) => {
      worksheet.addRow([
        item.Date,
        item.Location,
        item.Account,
        item.Business,
        item.MetricType,
        item.MetricValue,
      ]);
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      column.width = 15;
    });

    // Set response headers for file download
    const filename = `google-analytics-report-${fromDate}-to-${toDate}.xlsx`;
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    console.error("Error exporting Google Analytics report:", error);
    res.status(500).json({
      success: false,
      message: "Failed to export Google Analytics report",
      error: error.message,
    });
  }
};

module.exports = {
  getGoogleAnalyticsData,
  getGoogleAnalyticsCharts,
  getGoogleAnalyticsSummary,
  exportGoogleAnalyticsReport,
};
