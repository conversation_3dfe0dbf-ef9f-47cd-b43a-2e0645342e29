import React, { useContext } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import { useDispatch } from "react-redux";
import LinkedInService from "../../services/linkedin/linkedin.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";

interface LinkedInLoginProps {
  userId: number;
  onLoginSuccess?: () => void;
}

const LinkedInLogin: React.FC<LinkedInLoginProps> = ({
  userId,
  onLoginSuccess,
}) => {
  const dispatch = useDispatch();
  const { setToastConfig } = useContext(ToastContext);
  const linkedinService = new LinkedInService(dispatch);

  const handleLinkedInLogin = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent parent tab click
    try {
      console.log("LinkedIn Login - userId:", userId);

      if (!userId) {
        setToastConfig(
          ToastSeverity.Error,
          "User ID is required for LinkedIn authentication",
          true
        );
        return;
      }

      const response = await linkedinService.authenticate(userId);

      if (response?.success && response.authUrl) {
        // Redirect to LinkedIn OAuth
        window.location.href = response.authUrl;
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to initiate LinkedIn authentication",
          true
        );
      }
    } catch (error: any) {
      console.error("LinkedIn authentication error:", error);
      setToastConfig(ToastSeverity.Error, "Error connecting to LinkedIn", true);
    }
  };

  return (
    <Button
      variant="contained"
      startIcon={<LinkedInIcon />}
      onClick={handleLinkedInLogin}
      sx={{
        backgroundColor: "#0077B5", // LinkedIn blue color
        color: "white",
        minHeight: "50px",
        minWidth: "120px",
        textTransform: "none",
        fontWeight: "bold",
        borderRadius: "4px",
        "&:hover": {
          backgroundColor: "#005885", // Darker LinkedIn blue
        },
        "&:active": {
          backgroundColor: "#004471", // Even darker LinkedIn blue
        },
        "&:focus": {
          outline: "2px solid #0077B5",
          outlineOffset: "2px",
        },
      }}
      className="tableActionBtn"
    >
      <span className="responsiveHide">Connect</span>
    </Button>
  );
};

export default LinkedInLogin;
