import React, {
  FunctionComponent,
  ReactNode,
  useEffect,
  useState,
} from "react";
import { styled, useTheme, Theme, CSSObject } from "@mui/material/styles";
import Box from "@mui/material/Box";
import MuiDrawer from "@mui/material/Drawer";
import MuiAppBar, { AppBarProps as MuiAppBarProps } from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import List from "@mui/material/List";
import CssBaseline from "@mui/material/CssBaseline";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import IconButton from "@mui/material/IconButton";
import MenuIcon from "@mui/icons-material/Menu";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { NestedMenuItems } from "../../interfaces/nestedMenuItems";
import MenuListItemNestedComponent from "../menuListItemNested/menuListItemNested.component";
import DashboardCustomizeRoundedIcon from "@mui/icons-material/DashboardCustomizeRounded";
import useMediaQuery from "@mui/material/useMediaQuery";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import ManageAccountsOutlinedIcon from "@mui/icons-material/ManageAccountsOutlined";
import PersonAddOutlinedIcon from "@mui/icons-material/PersonAddOutlined";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { Collapse, Tooltip } from "@mui/material";

//Icons
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import ArrowForwardIosRoundedIcon from "@mui/icons-material/ArrowForwardIosRounded";
import { ListAltSharp } from "@mui/icons-material";
import { logOut } from "../../actions/auth.actions";
import { useDispatch, useSelector } from "react-redux";
import HeaderComponent from "../header/header.component";
import LogoutOutlinedIcon from "@mui/icons-material/LogoutOutlined";
import AdsClickOutlinedIcon from "@mui/icons-material/AdsClickOutlined";
import BorderColorOutlinedIcon from "@mui/icons-material/BorderColorOutlined";
import ChatOutlinedIcon from "@mui/icons-material/ChatOutlined";
import MapsUgcRoundedIcon from "@mui/icons-material/MapsUgcRounded";
import QuestionAnswerRoundedIcon from "@mui/icons-material/QuestionAnswerRounded";
import RateReviewIcon from "@mui/icons-material/RateReview";
import GroupIcon from "@mui/icons-material/Group";
import AdminPanelSettingsIcon from "@mui/icons-material/AdminPanelSettings";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import StoreIcon from "@mui/icons-material/Store";
import PublishIcon from "@mui/icons-material/Publish";
import PostAddIcon from "@mui/icons-material/PostAdd";
import AnalyticsIcon from "@mui/icons-material/Analytics";

import SettingsIcon from "@mui/icons-material/Settings";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import { openMenu, toggleMenu } from "../../actions/userPreferences.actions";
import ReviewService from "../../services/review/review.service";
import AddchartOutlinedIcon from "@mui/icons-material/AddchartOutlined";
import EqualizerOutlinedIcon from "@mui/icons-material/EqualizerOutlined";

// Image imports
import logoImage from "../../assets/common/Logo.png";
import logoIconImage from "../../assets/common/LogoIcon.png";

const drawerWidth = 340;

const openedMixin = (theme: Theme): CSSObject => ({
  width: drawerWidth,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: "hidden",
});

const closedMixin = (theme: Theme): CSSObject => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: "hidden",
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up("sm")]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}));

interface AppBarProps extends MuiAppBarProps {
  open?: boolean;
}

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})<AppBarProps>(({ theme }) => ({
  // zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        marginLeft: drawerWidth,
        width: `calc(100% - ${drawerWidth}px)`,
        transition: theme.transitions.create(["width", "margin"], {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.enteringScreen,
        }),
      },
    },
  ],
}));

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: (prop) => prop !== "open",
})(({ theme }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",
  variants: [
    {
      props: ({ open }) => open,
      style: {
        ...openedMixin(theme),
        "& .MuiDrawer-paper": openedMixin(theme),
      },
    },
    {
      props: ({ open }) => !open,
      style: {
        ...closedMixin(theme),
        "& .MuiDrawer-paper": closedMixin(theme),
      },
    },
  ],
}));

interface Props {
  children?: ReactNode;
}

const LeftMenuComponent: FunctionComponent<Props> = ({ children }) => {
  const dispatch = useDispatch();
  const logoutUser = () => dispatch<any>(logOut());
  const openLeftMenu = (show: boolean) => dispatch<any>(openMenu(show));
  const setOpenMenuId = (toggleMenuId: string | null) =>
    dispatch<any>(toggleMenu(toggleMenuId));
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const { menuOpened, toggledMenuId } = useSelector(
    (state: any) => state.userPreferencesReducer
  );

  const [unansweredReviewsCount, setUnansweredReviewsCount] =
    useState<number>(0);
  const isMobile = useMediaQuery("(max-width:600px)");
  const theme = useTheme();

  const handleToggle = (id: string) => {
    const itemData = toggledMenuId === id ? null : id;
    setOpenMenuId(itemData);
  };

  const fetchUnansweredReviewsCount = async () => {
    if (userInfo?.id && rbAccess?.ReviewsManagement) {
      try {
        const reviewService = new ReviewService(dispatch);
        const response = await reviewService.getUnansweredReviewsCount(
          userInfo.id
        );

        if (response?.count !== undefined) {
          setUnansweredReviewsCount(response.count);
        } else {
          setUnansweredReviewsCount(0);
        }
      } catch (error) {
        console.error("Failed to fetch unanswered reviews count:", error);
        setUnansweredReviewsCount(0);
      }
    }
  };

  // Expose the refresh function globally so other components can call it
  React.useEffect(() => {
    (window as any).refreshReviewNotificationCount =
      fetchUnansweredReviewsCount;
    return () => {
      delete (window as any).refreshReviewNotificationCount;
    };
  }, [userInfo, rbAccess]);

  useEffect(() => {
    console.log("userInfo: ", userInfo);
    console.log("Roles Based Access: ", rbAccess);
    if (isMobile && menuOpened) {
      openLeftMenu(false);
    }

    // Fetch notification count when component mounts and user has access
    fetchUnansweredReviewsCount();

    // Set up periodic refresh every 5 minutes
    const interval = setInterval(() => {
      fetchUnansweredReviewsCount();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [userInfo, rbAccess]);

  const handleMenuItemClick = (isOpen: boolean) => {};

  const FONTSIZE = "small";

  const DashboardRoutes: NestedMenuItems = {
    id: "home",
    title: "Dashboard",
    navigateTo: "/home",
    icon: <DashboardCustomizeRoundedIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: true,
  };

  const UserManagementRoutes: NestedMenuItems = {
    id: "user-management",
    title: "User Management",
    navigateTo: "",
    icon: <PersonAddOutlinedIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.UserManagement),
    nested: [
      {
        id: "",
        title: "List Users",
        navigateTo: "/user-management/users",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.UserCreate),
        icon: <GroupIcon fontSize={FONTSIZE} />,
      },
    ],
  };

  const RoleManagementRoutes: NestedMenuItems = {
    id: "roles-management",
    title: "Role Management",
    navigateTo: "",
    icon: <ManageAccountsOutlinedIcon />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.RoleManagement),
    nested: [
      {
        id: "",
        title: "Manage Roles",
        navigateTo: "/roles-management/roles",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.RoleManagement),
        icon: <AdminPanelSettingsIcon />,
      },
    ],
  };

  const BusinessManagementRoutes: NestedMenuItems = {
    id: "business-management",
    title: "Business Management",
    navigateTo: "",
    icon: <AdsClickOutlinedIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),
    nested: [
      {
        id: "",
        title: "Manage Business",
        navigateTo: "/business-management/manage-business",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),
        icon: <BusinessCenterIcon fontSize={FONTSIZE} />,
      },
      {
        id: "",
        title: "Local Business",
        navigateTo: "/business-management/local-business",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),
        icon: <StoreIcon fontSize={FONTSIZE} />,
      },
      // {
      //   title: "Business Summary",
      //   navigateTo: "/business-management/business-summary",
      //   open: open,
      //   isAccessible: Boolean(rbAccess && rbAccess.BusinessManagement),
      // },
    ],
  };

  const ReviewManagementRoutes: NestedMenuItems = {
    id: "review-management",
    title: "Review Management",
    navigateTo: "",
    icon: <BorderColorOutlinedIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.ReviewsManagement),
    notificationCount: unansweredReviewsCount,
    nested: [
      {
        id: "",
        title: "List of reviews for business",
        navigateTo: "/review-management/manage-reviews",
        open: menuOpened,
        isAccessible: true,
        icon: <RateReviewIcon fontSize={FONTSIZE} />,
        notificationCount: unansweredReviewsCount,
      },
      {
        id: "",
        title: "Review Settings",
        navigateTo: "/review-management/review-settings",
        open: menuOpened,
        isAccessible: true,
        icon: <SettingsIcon fontSize={FONTSIZE} />,
      },
    ],
  };

  const QandAManagementRoutes: NestedMenuItems = {
    id: "q-and-a",
    title: "Q&A",
    navigateTo: "/q-and-a",
    icon: <QuestionAnswerRoundedIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.QandAManagement),
  };

  const PostsManagementRoutes: NestedMenuItems = {
    id: "post-management",
    title: "Posts Management",
    navigateTo: "",
    icon: <ChatOutlinedIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.PostsManagement),
    nested: [
      {
        id: "",
        title: "Bulk Post Updates",
        navigateTo: "/post-management/posts",
        open: menuOpened,
        isAccessible: true,
        icon: <PublishIcon fontSize={FONTSIZE} />,
      },
      // {
      //   title: "Event Updates",
      //   navigateTo: "",
      //   open: open,
      //   isAccessible: true,
      // },
      {
        id: "",
        title: "Create Posts",
        navigateTo: "/post-management/create-social-post",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.PostsCreate),
        icon: <PostAddIcon fontSize={FONTSIZE} />,
      },
      // {
      //   id: "",
      //   title: "Page Not Found",
      //   navigateTo: "/page-not-found",
      //   open: open,
      //   isAccessible: true,
      // },
    ],
  };

  const AnalyticsRoutes: NestedMenuItems = {
    id: "analytics-and-reports",
    title: "Analytics & Reports",
    navigateTo: "",
    icon: <AnalyticsIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.AnalyticsManagement),
    nested: [
      {
        id: "",
        title: "Analytics",
        navigateTo: "/analytics-reports/analytics",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.Analytics),
        icon: <AddchartOutlinedIcon fontSize={FONTSIZE} />,
      },
      {
        id: "",
        title: "Reports",
        navigateTo: "/analytics-reports/reports",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.Reports),
        icon: <EqualizerOutlinedIcon fontSize={FONTSIZE} />,
      },
      {
        id: "",
        title: "Analytics Trend",
        navigateTo: "/analytics-reports/google-analytics-trend",
        open: menuOpened,
        isAccessible: Boolean(rbAccess && rbAccess.Analytics),
        icon: <AnalyticsIcon fontSize={FONTSIZE} />,
      },
    ],
  };

  const LocalFalconRoutes: NestedMenuItems = {
    id: "geo-grid",
    title: "Geo Grid",
    navigateTo: "/geo-grid",
    icon: <TrendingUpIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.GeoGridManagement),
  };

  const ManageAssetsRoutes: NestedMenuItems = {
    id: "manage-assets",
    title: "Manage Assets",
    navigateTo: "/manage-assets",
    icon: <CloudUploadIcon fontSize={FONTSIZE} />,
    open: menuOpened,
    isAccessible: Boolean(rbAccess && rbAccess.AssetManagement),
  };

  return (
    <div>
      <Box sx={{ display: "flex" }}>
        <CssBaseline />
        {/* <AppBar
          position="fixed"
          elevation={0}
          sx={{
            zIndex: (theme) => theme.zIndex.drawer + 1,
            backgroundColor: "transparent",
            boxShadow: "none",
            backdropFilter: "blur(0px)", // optional: nice blur effect behind
          }}
          open={open}
        > */}
        {/* <Toolbar className="headerMenu"> */}
        {/* <IconButton
              color="inherit"
              aria-label="open drawer"
              onClick={handleDrawerOpen}
              edge="start"
              sx={[
                {
                  marginRight: 5,
                },
                open && { display: "none" },
              ]}
            >
              <ArrowForwardIosRoundedIcon />
            </IconButton> */}
        <HeaderComponent />
        {/* </Toolbar> */}
        {/* </AppBar> */}

        {rbAccess && (
          <Drawer
            variant="permanent"
            open={menuOpened}
            style={
              isMobile && menuOpened
                ? { position: "absolute" }
                : { position: "unset" }
            }
            // onMouseEnter={!isMobile ? () => setOpen(true) : undefined}
            // onMouseLeave={!isMobile ? () => setOpen(false) : undefined}
          >
            <DrawerHeader className="navLogoPart">
              {menuOpened ? (
                <Box sx={{ textAlign: "center", my: 2, py: 1 }}>
                  <img
                    alt="MyLocoBiz - Login"
                    className="widthLogo navFullLogo"
                    src={logoImage}
                  />
                </Box>
              ) : (
                <></>
                // <Box className="navIcon">
                //   <img
                //     alt="MyLocoBiz - Login"
                //     className=" "
                //     src={logoIconImage}
                //   />
                // </Box>
              )}
              {isMobile ? (
                <IconButton onClick={() => openLeftMenu(!menuOpened)}>
                  {menuOpened ? <ChevronLeftIcon /> : <ChevronRightIcon />}
                </IconButton>
              ) : (
                <IconButton onClick={() => openLeftMenu(!menuOpened)}>
                  {menuOpened ? <ChevronLeftIcon /> : <ChevronRightIcon />}
                </IconButton>
              )}
            </DrawerHeader>

            {/* <Divider /> */}

            <List
              sx={{
                width: "100%",
                height: "100%",
                maxWidth: 360,
                fontWeight: "600",
              }}
              component="nav"
              aria-labelledby="nested-list-subheader"
            >
              <MenuListItemNestedComponent
                key={"home"}
                props={{
                  ...DashboardRoutes,
                  isToggled: toggledMenuId === DashboardRoutes.id,
                  onToggle: () => handleToggle(DashboardRoutes.id),
                }}
              />

              {Boolean(rbAccess.UserManagement) && (
                <MenuListItemNestedComponent
                  key={"User-Management-Routes"}
                  props={{
                    ...UserManagementRoutes,
                    isToggled: toggledMenuId === UserManagementRoutes.id,
                    onToggle: () => handleToggle(UserManagementRoutes.id),
                  }}
                />
              )}

              {Boolean(rbAccess.RoleManagement) && (
                <MenuListItemNestedComponent
                  key={"Role-Management-Routes"}
                  props={{
                    ...RoleManagementRoutes,
                    isToggled: toggledMenuId === RoleManagementRoutes.id,
                    onToggle: () => handleToggle(RoleManagementRoutes.id),
                  }}
                />
              )}

              {Boolean(rbAccess.BusinessManagement) && (
                <MenuListItemNestedComponent
                  key={"Business-Management-Routes"}
                  props={{
                    ...BusinessManagementRoutes,
                    isToggled: toggledMenuId === BusinessManagementRoutes.id,
                    onToggle: () => handleToggle(BusinessManagementRoutes.id),
                  }}
                />
              )}

              {Boolean(rbAccess?.ReviewsManagement) && (
                <MenuListItemNestedComponent
                  key={"Review-Management-Routes"}
                  props={{
                    ...ReviewManagementRoutes,
                    isToggled: toggledMenuId === ReviewManagementRoutes.id,
                    onToggle: () => handleToggle(ReviewManagementRoutes.id),
                  }}
                />
              )}

              {Boolean(rbAccess.QandAManagement) && (
                <MenuListItemNestedComponent
                  key={"QandA-Management-Routes"}
                  props={{
                    ...QandAManagementRoutes,
                    isToggled: toggledMenuId === QandAManagementRoutes.id,
                    onToggle: () => handleToggle(QandAManagementRoutes.id),
                  }}
                />
              )}

              {Boolean(rbAccess.PostsManagement) && (
                <MenuListItemNestedComponent
                  key={"Posts-Management-Routes"}
                  props={{
                    ...PostsManagementRoutes,
                    isToggled: toggledMenuId === PostsManagementRoutes.id,
                    onToggle: () => handleToggle(PostsManagementRoutes.id),
                  }}
                />
              )}

              {Boolean(rbAccess.AnalyticsManagement) && (
                <MenuListItemNestedComponent
                  key={"analytics-reports"}
                  props={{
                    ...AnalyticsRoutes,
                    isToggled: toggledMenuId === AnalyticsRoutes.id,
                    onToggle: () => handleToggle(AnalyticsRoutes.id),
                  }}
                />
              )}

              {/* Local Falcon - Available to users with LocationManagement permission */}
              {Boolean(rbAccess.LocationManagement) && (
                <MenuListItemNestedComponent
                  key={"local-falcon"}
                  props={{
                    ...LocalFalconRoutes,
                    isToggled: toggledMenuId === LocalFalconRoutes.id,
                    onToggle: () => handleToggle(LocalFalconRoutes.id),
                  }}
                />
              )}

              {/* Manage Assets - Available to users with BusinessManagement permission */}
              {Boolean(rbAccess.AssetManagement) && (
                <MenuListItemNestedComponent
                  key={"manage-assets"}
                  props={{
                    ...ManageAssetsRoutes,
                    isToggled: toggledMenuId === ManageAssetsRoutes.id,
                    onToggle: () => handleToggle(ManageAssetsRoutes.id),
                  }}
                />
              )}
            </List>
            <List>
              <ListItemButton
                sx={[
                  {
                    minHeight: 48,
                    px: 2.5,
                  },
                  menuOpened
                    ? {
                        justifyContent: "initial",
                      }
                    : {
                        justifyContent: "center",
                      },
                ]}
                onClick={() => {
                  openLeftMenu(false);
                  setOpenMenuId("");
                  setTimeout(() => {
                    logoutUser();
                  }, 1000);
                }}
              >
                <Tooltip title={!menuOpened ? "Logout" : ""} placement="right">
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      justifyContent: "center",
                      mr: menuOpened ? 3 : "auto",
                    }}
                  >
                    <LogoutOutlinedIcon
                      color={menuOpened ? "error" : "inherit"}
                    />
                  </ListItemIcon>
                </Tooltip>

                <ListItemText
                  slotProps={{
                    primary: {
                      sx: { fontWeight: 600 },
                    },
                  }}
                  sx={{
                    opacity: menuOpened ? 1 : 0,
                    transition: "opacity 0.3s",
                  }}
                  primary={"Logout"}
                />
                <></>
              </ListItemButton>
            </List>
          </Drawer>
        )}

        <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
          <DrawerHeader />
          {children}
        </Box>
      </Box>
    </div>
  );
};

export default LeftMenuComponent;
