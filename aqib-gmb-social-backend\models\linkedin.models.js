const pool = require("../config/db");
const logger = require("../utils/logger");

module.exports = class LinkedIn {
  /**
   * Save LinkedIn OAuth tokens
   * @param {Object} tokenData - Token data
   * @returns {Promise<Object>} Save result
   */
  static async saveOAuthTokens(tokenData) {
    try {
      const query = `
        INSERT INTO linkedin_oauth_tokens
        (user_id, linkedin_user_id, linkedin_user_name, linkedin_user_email, linkedin_user_picture, access_token, refresh_token, expires_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        linkedin_user_name = VALUES(linkedin_user_name),
        linkedin_user_email = VALUES(linkedin_user_email),
        linkedin_user_picture = VALUES(linkedin_user_picture),
        access_token = VALUES(access_token),
        refresh_token = VALUES(refresh_token),
        expires_at = VALUES(expires_at),
        updated_at = CURRENT_TIMESTAMP
      `;

      const values = [
        tokenData.userId,
        tokenData.linkedinUserId,
        tokenData.linkedinUserName || null,
        tokenData.linkedinUserEmail || null,
        tokenData.linkedinUserPicture || null,
        tokenData.accessToken,
        tokenData.refreshToken || null,
        tokenData.expiresAt || null,
      ];

      const result = await pool.query(query, values);

      logger.info("LinkedIn OAuth tokens saved successfully", {
        userId: tokenData.userId,
        linkedinUserId: tokenData.linkedinUserId,
        linkedinUserEmail: tokenData.linkedinUserEmail,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving LinkedIn OAuth tokens:", {
        error: error.message,
        userId: tokenData.userId,
        linkedinUserId: tokenData.linkedinUserId,
      });
      throw error;
    }
  }

  /**
   * Get LinkedIn accounts for user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} LinkedIn accounts result
   */
  static async getLinkedInAccounts(userId) {
    try {
      const query = `
        SELECT id, user_id, linkedin_user_id, linkedin_user_name, linkedin_user_email,
               linkedin_user_picture, access_token, created_at, updated_at
        FROM linkedin_oauth_tokens
        WHERE user_id = ? AND status_id = 1
        ORDER BY created_at DESC
      `;

      const results = await pool.query(query, [userId]);

      logger.info("LinkedIn accounts retrieved", {
        userId,
        accountsCount: results.length,
      });

      return { success: true, accounts: results };
    } catch (error) {
      logger.error("Error getting LinkedIn accounts:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Save LinkedIn post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Save result
   */
  static async savePost(postData) {
    try {
      const query = `
        INSERT INTO linkedin_posts
        (user_id, profile_id, post_id, text, media, published, scheduled_publish_time, linkedin_response)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        postData.userId,
        postData.profileId,
        postData.postId,
        postData.text,
        postData.media,
        postData.published,
        postData.scheduledPublishTime,
        postData.linkedinResponse,
      ];

      const result = await pool.query(query, values);

      logger.info("LinkedIn post saved successfully", {
        userId: postData.userId,
        profileId: postData.profileId,
        postId: postData.postId,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving LinkedIn post:", {
        error: error.message,
        userId: postData.userId,
        profileId: postData.profileId,
      });
      throw error;
    }
  }

  /**
   * Get LinkedIn posts for user
   * @param {number} userId - User ID
   * @param {string} profileId - Profile ID (optional)
   * @param {number} page - Page number
   * @param {number} limit - Limit per page
   * @returns {Promise<Object>} Posts result
   */
  static async getPosts(userId, profileId = null, page = 1, limit = 10) {
    try {
      let query = `
        SELECT lp.*, lot.linkedin_user_name, lot.linkedin_user_email
        FROM linkedin_posts lp
        LEFT JOIN linkedin_oauth_tokens lot ON lp.profile_id = lot.linkedin_user_id
        WHERE lp.user_id = ?
      `;
      let params = [userId];

      if (profileId) {
        query += ` AND lp.profile_id = ?`;
        params.push(profileId);
      }

      query += ` ORDER BY lp.created_at DESC`;

      if (limit > 0) {
        const offset = (page - 1) * limit;
        query += ` LIMIT ? OFFSET ?`;
        params.push(limit, offset);
      }

      const results = await pool.query(query, params);

      // Get total count for pagination
      let countQuery = `
        SELECT COUNT(*) as total
        FROM linkedin_posts lp
        WHERE lp.user_id = ?
      `;
      let countParams = [userId];

      if (profileId) {
        countQuery += ` AND lp.profile_id = ?`;
        countParams.push(profileId);
      }

      const countResult = await pool.query(countQuery, countParams);
      const total = countResult[0].total;

      logger.info("LinkedIn posts retrieved", {
        userId,
        profileId,
        postsCount: results.length,
        total,
        page,
        limit,
      });

      return {
        success: true,
        posts: results,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Error getting LinkedIn posts:", {
        error: error.message,
        userId,
        profileId,
      });
      throw error;
    }
  }

  /**
   * Get LinkedIn post by ID
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Post result
   */
  static async getPostById(postId, userId) {
    try {
      const query = `
        SELECT lp.*, lot.linkedin_user_name, lot.linkedin_user_email
        FROM linkedin_posts lp
        LEFT JOIN linkedin_oauth_tokens lot ON lp.profile_id = lot.linkedin_user_id
        WHERE lp.id = ? AND lp.user_id = ?
      `;

      const results = await pool.query(query, [postId, userId]);

      if (results.length === 0) {
        return { success: false, message: "Post not found" };
      }

      logger.info("LinkedIn post retrieved by ID", {
        postId,
        userId,
      });

      return { success: true, post: results[0] };
    } catch (error) {
      logger.error("Error getting LinkedIn post by ID:", {
        error: error.message,
        postId,
        userId,
      });
      throw error;
    }
  }

  /**
   * Update LinkedIn post status
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Update result
   */
  static async updatePostStatus(postId, userId, status) {
    try {
      const query = `
        UPDATE linkedin_posts
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      const result = await pool.query(query, [status, postId, userId]);

      if (result.affectedRows === 0) {
        return { success: false, message: "Post not found or not updated" };
      }

      logger.info("LinkedIn post status updated", {
        postId,
        userId,
        status,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error updating LinkedIn post status:", {
        error: error.message,
        postId,
        userId,
        status,
      });
      throw error;
    }
  }
};
