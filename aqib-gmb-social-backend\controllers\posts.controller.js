const GMB_ACTIONS = require("../constants/gmb-actions");
const logger = require("../utils/logger");
const { reqGMBApi } = require("../services/gmb.service");
const fs = require("fs");
const path = require("path");
const { OAuth2Client } = require("google-auth-library");
const keys = require("../config/OAuthKey.json");
const { google } = require("googleapis");
const gmbToken = require("../models/gmb.models");
const axios = require("axios");
const Location = require("../models/location.models");
const Auth = require("../models/auth.models");
const os = require("os");
const { v4: uuidv4 } = require("uuid"); // Import UUID
const Posts = require("../models/post.models");

const welcome = async (req, res) => {
  try {
    logger.logControllerAction("posts", "welcome", req.requestId);
    logger.info("Posts welcome endpoint accessed", {
      requestId: req.requestId,
    });
    res.send({ message: "Posts Home Page" });
  } catch (error) {
    logger.error("Error in posts welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

const uploadImageLocal = async (req, res) => {
  const files = req.files;

  if (!files || files.length === 0) {
    return res.status(400).json({ message: "Missing file" });
  }

  const uploadDir = path.join(__dirname, "../uploads"); // Store in ~/uploads/

  // Ensure upload directory exists
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  let uploadedFiles = [];

  for (let index = 0; index < files.length; index++) {
    const file = files[index];
    const fileExtension = path.extname(file.originalname); // Keep the original file extension
    const uniqueFileName = `${uuidv4()}${fileExtension}`;
    const filePath = path.join(uploadDir, uniqueFileName);

    // Save file to local storage
    fs.writeFileSync(filePath, file.buffer);

    uploadedFiles.push({
      originalName: file.originalname,
      fileName: uniqueFileName,
      fileUrl: `${req.protocol}://${req.get("host")}/uploads/${uniqueFileName}`, // URL to access file
      size: file.size,
    });
  }

  return res.status(200).json({
    message: "Files uploaded successfully",
    files: uploadedFiles,
  });
};

/**
 * Upload post images to S3 temp folder
 */
const uploadPostImagesToS3 = async (req, res) => {
  try {
    const files = req.files;
    const userId = req.params.userId;

    if (!files || files.length === 0) {
      return res.status(400).json({
        message: "No files uploaded",
        isSuccess: false,
      });
    }

    if (!userId) {
      return res.status(400).json({
        message: "User ID is required",
        isSuccess: false,
      });
    }

    const s3Service = require("../services/s3.service");
    const uploadedFiles = [];
    const errors = [];

    for (const file of files) {
      try {
        // Validate file type
        const allowedTypes = [
          "image/jpeg",
          "image/jpg",
          "image/png",
          "image/gif",
          "image/webp",
        ];
        if (!allowedTypes.includes(file.mimetype)) {
          errors.push({
            fileName: file.originalname,
            error: `Unsupported file type: ${
              file.mimetype
            }. Allowed types: ${allowedTypes.join(", ")}`,
          });
          continue;
        }

        // Validate file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
          errors.push({
            fileName: file.originalname,
            error: `File size too large: ${(file.size / 1024 / 1024).toFixed(
              2
            )}MB. Maximum allowed: 10MB`,
          });
          continue;
        }

        const uploadResult = await s3Service.uploadPostImageToS3(
          file.buffer,
          parseInt(userId),
          file.originalname,
          file.mimetype
        );

        if (uploadResult.success) {
          uploadedFiles.push({
            originalName: file.originalname,
            fileName: uploadResult.data.fileName,
            s3Key: uploadResult.data.s3Key,
            s3Url: uploadResult.data.s3Url,
            signedUrl: uploadResult.data.signedUrl, // Include signed URL
            size: file.size,
            mimeType: file.mimetype,
            etag: uploadResult.data.etag,
          });
        } else {
          errors.push({
            fileName: file.originalname,
            error: uploadResult.error,
          });
        }
      } catch (fileError) {
        errors.push({
          fileName: file.originalname,
          error: fileError.message,
        });
      }
    }

    // Return response based on results
    if (uploadedFiles.length === 0 && errors.length > 0) {
      return res.status(400).json({
        message: "All file uploads failed",
        errors: errors,
        isSuccess: false,
      });
    }

    res.status(200).json({
      message:
        uploadedFiles.length === files.length
          ? "All files uploaded successfully to S3"
          : "Some files uploaded successfully to S3",
      data: uploadedFiles,
      errors: errors.length > 0 ? errors : undefined,
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error uploading post images to S3:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error.message,
      isSuccess: false,
    });
  }
};

const startUpload = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.start_upload,
      reqBodyData: { accountId, locationId },
    });
    if (result.success) {
      res.status(200).json({ message: "Media uploaded", data: result });
    } else {
      res
        .status(result.status)
        .json({ message: "Failed to upload data", data: result.data });
    }
    // console.log(req)
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const uploadMedia = async (req, res) => {
  const resourceName = req.body.resourceName;
  const files = req.files;
  const userId = req.body.userId;
  const businessGroupId = req.body.businessGroupId;
  const businessId = req.body.businessId;
  const locationId = req.body.locationId;

  if (!files) {
    return res.status(400).json({ message: "Missing file" });
  }

  const oAuthTokens = await Location.getTokensToRetrievePostsOnLocation(
    userId,
    businessGroupId,
    businessId,
    locationId
  );

  if (oAuthTokens && oAuthTokens.length > 0) {
    var responseData = await Auth.authenticateGoogle(oAuthTokens[0]);
    req["user"]["gmbToken"] = responseData.accessToken;
    for (let index = 0; index < files.length; index++) {
      const element = files[index];

      const fileData = element.buffer;

      try {
        const result = await reqGMBApi({
          req,
          action: GMB_ACTIONS.upload_media,
          reqBodyData: { resourceName, fileData },
        });
        if (result.success) {
          res
            .status(200)
            .json({ message: "Media uploaded", data: result.data });
        } else {
          res
            .status(result.status)
            .json({ message: "Failed to upload data", data: result.data });
        }
      } catch (error) {
        res
          .status(500)
          .json({ message: "Internal Server Error", error: error.message });
      }
    }
  } else {
  }
};

const getGooglePostsOnLocationId = async (req, res) => {
  try {
    let postsData = [];
    const userId = req.params.userId;
    const businessGroupId = req.query.businessGroupId;
    const businessId = req.query.businessId;
    const locationId = req.query.locationId;
    const oAuthTokens = await Location.getTokensToRetrievePostsOnLocation(
      userId,
      businessGroupId,
      businessId,
      locationId
    );

    if (oAuthTokens && oAuthTokens.length > 0) {
      const uniqueAccountIds = [
        ...new Set(oAuthTokens.map((item) => item.gmbAccountId)),
      ];

      for (let index = 0; index < uniqueAccountIds.length; index++) {
        const element = uniqueAccountIds[index];
        const filteredLocations = oAuthTokens.filter(
          (x) => x.gmbAccountId === element
        );
        var responseData = await Auth.authenticateGoogle(filteredLocations[0]);

        for (let index = 0; index < filteredLocations.length; index++) {
          const location = filteredLocations[index];
          const accountId = location.gmbAccountId;
          const locationId = location.gmbLocationId;
          req["user"]["gmbToken"] = responseData.accessToken;
          try {
            const result = await reqGMBApi({
              req,
              action: GMB_ACTIONS.retrive_posts,
              reqBodyData: { accountId, locationId },
            });

            if (
              result &&
              result.data &&
              result.data.localPosts &&
              result.data.localPosts.length > 0
            ) {
              postsData = [...postsData, ...result.data.localPosts];
            }
          } catch (error) {}
        }
      }

      res.status(200).json({
        message: "Posts Retrieved",
        data: postsData,
        isSuccess: true,
      });
    } else {
      return res
        .status(400)
        .json({ error: "Google Re-authentication required!" });
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const getGooglePosts = async (req, res) => {
  try {
    let posts = [];
    const oAuthTokens = await Location.getTokensToRetrievePosts(
      req.params.userId
    );

    if (oAuthTokens) {
      const uniqueAccountIds = [
        ...new Set(oAuthTokens.map((item) => item.gmbAccountId)),
      ];

      for (let index = 0; index < uniqueAccountIds.length; index++) {
        const element = uniqueAccountIds[index];
        const filteredLocations = oAuthTokens.filter(
          (x) => x.gmbAccountId === element
        );
        var responseData = await Auth.authenticateGoogle(filteredLocations[0]);

        for (let index = 0; index < filteredLocations.length; index++) {
          const location = filteredLocations[index];
          const accountId = location.gmbAccountId;
          const locationId = location.gmbLocationId;
          req["user"]["gmbToken"] = responseData.accessToken;
          try {
            const result = await reqGMBApi({
              req,
              action: GMB_ACTIONS.retrive_posts,
              reqBodyData: { accountId, locationId },
            });

            if (
              result &&
              result.data &&
              result.data.localPosts &&
              result.data.localPosts.length > 0
            ) {
              posts = [...posts, ...result.data.localPosts];
            }
          } catch (error) {}
        }
      }

      res.status(200).json({
        message: "Posts Retrieved",
        data: posts,
        isSuccess: true,
      });
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const deleteGooglePost = async (req, res) => {
  try {
    const deleteRoute = req.query.id;
    const accountId = deleteRoute.split("/")[1];
    const locationId = deleteRoute.split("/")[3];
    const oAuthTokens = await Location.getTokensToRetrievePosts(
      req.params.userId,
      accountId
    );
    if (oAuthTokens) {
      var selectedRecord = oAuthTokens.filter(
        (x) => x.gmbAccountId === accountId && x.gmbLocationId === locationId
      );
      if (selectedRecord) {
        var responseData = await Auth.authenticateGoogle(selectedRecord[0]);
        req["user"]["gmbToken"] = responseData.accessToken;
        try {
          const result = await reqGMBApi({
            req,
            action: GMB_ACTIONS.delete_post,
            reqBodyData: { route: req.query.id },
          });
          res.status(200).json({
            message: "Posts Deleted",
            data: result,
            isSuccess: true,
          });
        } catch (error) {}
      }
    }
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

const createPost = async (req, res) => {
  try {
    // Ensure posts table exists (only if not already initialized)

    const locationInfo = req.body.locationInfo;
    const userId = req.params.userId;
    const businessGroupId = locationInfo.accountId;
    const businessId = locationInfo.businessId;
    const locationId = locationInfo.locationId;
    const createGooglePost = req.body.createGooglePost;
    const isBulkPost = req.body.isBulkPost || false;
    const bulkPostId =
      req.body.bulkPostId || (isBulkPost ? Posts.generateBulkPostId() : null);

    const oAuthTokens = await Location.getTokensToRetrievePostsOnLocation(
      userId,
      businessGroupId,
      businessId,
      locationId
    );

    if (oAuthTokens) {
      const uniqueAccountIds = [
        ...new Set(oAuthTokens.map((item) => item.gmbAccountId)),
      ];
      const element = uniqueAccountIds[0];

      const filteredLocations = oAuthTokens.filter(
        (x) => x.gmbAccountId === element
      );

      const location = filteredLocations[0];
      const gmbAccountId = location.gmbAccountId;
      const gmbLocationId = location.gmbLocationId;
      var responseData = await Auth.authenticateGoogle(filteredLocations[0]);
      req["user"]["gmbToken"] = responseData.accessToken;
      if (
        createGooglePost.topicType === "EVENT" ||
        createGooglePost.topicType === "OFFER"
      ) {
        if (createGooglePost.event && createGooglePost.event.schedule) {
          const startDate = new Date(createGooglePost.event.schedule.startTime);
          const endDate = new Date(createGooglePost.event.schedule.endTime);

          createGooglePost.event.schedule = {
            startDate: {
              year: startDate.getUTCFullYear(),
              month: startDate.getUTCMonth() + 1,
              day: startDate.getUTCDate(),
            },
            startTime: {
              hours: startDate.getUTCHours(),
              minutes: startDate.getUTCMinutes(),
              seconds: startDate.getUTCSeconds(),
              nanos: 0,
            },
            endDate: {
              year: endDate.getUTCFullYear(),
              month: endDate.getUTCMonth() + 1,
              day: endDate.getUTCDate(),
            },
            endTime: {
              hours: endDate.getUTCHours(),
              minutes: endDate.getUTCMinutes(),
              seconds: endDate.getUTCSeconds(),
              nanos: 0,
            },
          };
        }
      }

      const createPostResponse = await reqGMBApi({
        req,
        action: GMB_ACTIONS.create_post,
        reqBodyData: {
          accountId: gmbAccountId,
          locationId: gmbLocationId,
          requestObj: createGooglePost,
        },
      });

      if (createPostResponse.status === 400) {
        res.status(200).json({
          message: "Creation of the post was unsuccessful.",
          data: createPostResponse.data,
          isSuccess: false,
        });
      } else {
        // Save post to database
        try {
          const postData = {
            userId: parseInt(userId),
            businessId: businessId,
            locationId: gmbLocationId,
            accountId: gmbAccountId,
            googlePostName: createPostResponse.data.name,
            bulkPostId: bulkPostId,
            isBulkPost: isBulkPost,
            postContent: createGooglePost,
            postResponse: createPostResponse.data,
            summary: createPostResponse.data.summary,
            topicType: createPostResponse.data.topicType,
            languageCode: createPostResponse.data.languageCode,
            state: createPostResponse.data.state,
            searchUrl: createPostResponse.data.searchUrl,
          };

          const saveResult = await Posts.savePost(postData);

          // Log the creation
          if (saveResult.success) {
            const ipAddress =
              req.ip ||
              req.connection.remoteAddress ||
              req.headers["x-forwarded-for"];
            const userAgent = req.headers["user-agent"];

            await Posts.logPostAction({
              postId: saveResult.insertId,
              googlePostName: createPostResponse.data.name,
              userId: parseInt(userId),
              businessId: businessId,
              locationId: gmbLocationId,
              accountId: gmbAccountId,
              actionType: isBulkPost ? "BULK_CREATE" : "CREATE",
              oldContent: null,
              newContent: JSON.stringify(createGooglePost),
              oldSummary: null,
              newSummary: createPostResponse.data.summary,
              oldTopicType: null,
              newTopicType: createPostResponse.data.topicType,
              oldState: null,
              newState: createPostResponse.data.state,
              oldBulkPostId: null,
              newBulkPostId: bulkPostId,
              oldIsBulkPost: null,
              newIsBulkPost: isBulkPost,
              changesMade: postData,
              ipAddress: ipAddress,
              userAgent: userAgent,
              notes: `Post created via ${
                isBulkPost ? "bulk" : "single"
              } creation`,
            });
          }
        } catch (dbError) {
          console.error("Error saving post to database:", dbError);
          // Continue with response even if DB save fails
        }

        res.status(200).json({
          message: "Post Created",
          data: createPostResponse.data,
          isSuccess: true,
          bulkPostId: bulkPostId, // Include bulk post ID in response
        });
      }
    }
  } catch (error) {
    console.error("Error creating Google Post:", error);
    res.status(500).json({
      message: "Failed to create post",
      error: error.message,
      isSuccess: false,
      bulkPostId: req.body.bulkPostId || null,
    });
  }
};

const saveSchedule = async (req, res) => {
  try {
    const locationInfo = req.body.locationInfo;
    const createGooglePost = req.body.createGooglePost;
    result = await Posts.saveSchedules(req.body);
    return res
      .status(201)
      .json({ message: "Schedule Saved Successfully", data: result });
  } catch (error) {
    console.error("Error fetching Google Posts:", error);
    return [];
  }
};

/**
 * Check if a post is part of a bulk post
 */
const checkBulkPostStatus = async (req, res) => {
  try {
    const { googlePostName } = req.params;

    if (!googlePostName) {
      return res.status(400).json({
        message: "Google post name is required",
        isSuccess: false,
      });
    }

    const bulkStatus = await Posts.checkBulkPostStatus(googlePostName);

    if (!bulkStatus) {
      return res.status(404).json({
        message: "Post not found",
        isSuccess: false,
      });
    }

    res.status(200).json({
      message: "Bulk post status retrieved",
      data: bulkStatus,
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error checking bulk post status:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error.message,
      isSuccess: false,
    });
  }
};

/**
 * Get all posts in a bulk post group
 */
const getBulkPostDetails = async (req, res) => {
  try {
    const { bulkPostId } = req.params;

    if (!bulkPostId) {
      return res.status(400).json({
        message: "Bulk post ID is required",
        isSuccess: false,
      });
    }

    const posts = await Posts.getPostsByBulkId(bulkPostId);

    res.status(200).json({
      message: "Bulk post details retrieved",
      data: {
        posts: posts,
        totalPosts: posts.length,
      },
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error getting bulk post details:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error.message,
      isSuccess: false,
    });
  }
};

/**
 * Update Google post
 */
const updatePost = async (req, res) => {
  try {
    const { userId } = req.params;
    const { googlePostName, updateData, isSingleEdit = false } = req.body;

    if (!googlePostName) {
      return res.status(400).json({
        message: "Google post name is required",
        isSuccess: false,
      });
    }

    if (!updateData) {
      return res.status(400).json({
        message: "Update data is required",
        isSuccess: false,
      });
    }

    // Get client IP and user agent for logging
    const ipAddress =
      req.ip || req.connection.remoteAddress || req.headers["x-forwarded-for"];
    const userAgent = req.headers["user-agent"];

    // If it's a single edit from bulk post, remove bulk association
    if (isSingleEdit) {
      updateData.bulkPostId = null;
      updateData.isBulkPost = false;
    }

    // Update the post in database
    const updateResult = await Posts.updatePost(
      googlePostName,
      updateData,
      parseInt(userId),
      ipAddress,
      userAgent
    );

    if (!updateResult.success) {
      return res.status(500).json({
        message: "Failed to update post in database",
        isSuccess: false,
      });
    }

    // TODO: Update the post on Google My Business API
    // This would involve calling Google's API to update the actual post
    // For now, we'll just update our local database

    res.status(200).json({
      message: isSingleEdit
        ? "Post updated successfully and removed from bulk group"
        : "Post updated successfully",
      data: {
        googlePostName,
        affectedRows: updateResult.affectedRows,
        isSingleEdit,
      },
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error updating post:", error);
    res.status(500).json({
      message: "Failed to update post",
      error: error.message,
      isSuccess: false,
    });
  }
};

/**
 * Get post by Google post name
 */
const getPostByGoogleName = async (req, res) => {
  try {
    const { googlePostName } = req.params;

    if (!googlePostName) {
      return res.status(400).json({
        message: "Google post name is required",
        isSuccess: false,
      });
    }

    const post = await Posts.getPostByGoogleName(googlePostName);

    if (!post) {
      return res.status(404).json({
        message: "Post not found",
        isSuccess: false,
      });
    }

    res.status(200).json({
      message: "Post retrieved",
      data: post,
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error getting post by Google name:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error.message,
      isSuccess: false,
    });
  }
};

/**
 * Update bulk posts
 */
const updateBulkPosts = async (req, res) => {
  try {
    const { userId } = req.params;
    const { bulkPostId, updateData } = req.body;

    if (!bulkPostId) {
      return res.status(400).json({
        message: "Bulk post ID is required",
        isSuccess: false,
      });
    }

    if (!updateData) {
      return res.status(400).json({
        message: "Update data is required",
        isSuccess: false,
      });
    }

    // Get client IP and user agent for logging
    const ipAddress =
      req.ip || req.connection.remoteAddress || req.headers["x-forwarded-for"];
    const userAgent = req.headers["user-agent"];

    // Update all posts in the bulk group
    const updateResult = await Posts.updateBulkPosts(
      bulkPostId,
      updateData,
      parseInt(userId),
      ipAddress,
      userAgent
    );

    if (!updateResult.success) {
      return res.status(500).json({
        message: "Failed to update bulk posts",
        isSuccess: false,
      });
    }

    // TODO: Update posts on Google My Business API
    // This would involve calling Google's API to update the actual posts
    // For now, we'll just update our local database

    res.status(200).json({
      message: `Successfully updated ${updateResult.successfulUpdates} of ${updateResult.totalPosts} posts`,
      data: {
        bulkPostId,
        totalPosts: updateResult.totalPosts,
        successfulUpdates: updateResult.successfulUpdates,
        failedUpdates: updateResult.failedUpdates,
        results: updateResult.results,
        errors: updateResult.errors,
      },
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error updating bulk posts:", error);
    res.status(500).json({
      message: "Failed to update bulk posts",
      error: error.message,
      isSuccess: false,
    });
  }
};

/**
 * Get post logs for audit trail
 */
const getPostLogs = async (req, res) => {
  try {
    const { identifier } = req.params;
    const { type = "google_post_name", limit = 50 } = req.query;

    if (!identifier) {
      return res.status(400).json({
        message: "Post identifier is required",
        isSuccess: false,
      });
    }

    const logs = await Posts.getPostLogs(identifier, type, parseInt(limit));

    res.status(200).json({
      message: "Post logs retrieved successfully",
      data: logs,
      count: logs.length,
      isSuccess: true,
    });
  } catch (error) {
    console.error("Error getting post logs:", error);
    res.status(500).json({
      message: "Failed to retrieve post logs",
      error: error.message,
      isSuccess: false,
    });
  }
};

module.exports = {
  welcome,
  uploadMedia,
  startUpload,
  getGooglePosts,
  deleteGooglePost,
  getGooglePostsOnLocationId,
  uploadImageLocal,
  uploadPostImagesToS3,
  createPost,
  saveSchedule,
  checkBulkPostStatus,
  getBulkPostDetails,
  getPostByGoogleName,
  updatePost,
  updateBulkPosts,
  getPostLogs,
};
