import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Box,
  LinearProgress,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  IconButton,
  Typography,
} from "@mui/material";
import {
  CheckCircleOutline as CheckCircleOutlineIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import {
  GooglePostStatusDialogProps,
  ISelectionLocationWithPost,
} from "../interfaces/postStatusInterfaces";
import BlinkingText from "./shared/BlinkingText";

const GooglePostStatusDialog: React.FC<GooglePostStatusDialogProps> = ({
  open,
  onClose,
  selectedLocations,
  postCreationProgress,
  allApiCallsCompleted,
  getProgressColor,
}) => {
  const navigate = useNavigate();

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={() => console.log("On Close")}
    >
      <DialogTitle>Google Post Upload Status</DialogTitle>
      <Box sx={{ position: "relative", width: "100%" }}>
        <LinearProgress
          variant="determinate"
          value={postCreationProgress.percent}
          color="secondary"
          sx={{
            height: "20px",
            backgroundColor: "#d3d3d3",
            "& .MuiLinearProgress-bar": {
              backgroundColor: getProgressColor(),
            },
          }}
        />
        <BlinkingText
          variant="body2"
          sx={{
            position: "absolute",
            top: 0,
            left: "13%",
            transform: "translateX(-50%)",
            fontWeight: "bold",
            color: "#ffffff",
          }}
        >
          {postCreationProgress.status}...
        </BlinkingText>
      </Box>
      <DialogContent>
        <DialogContentText>
          <Box
            noValidate
            component="form"
            sx={{
              display: "flex",
              flexDirection: "column",
              m: "auto",
            }}
          >
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Business</b>
                    </TableCell>
                    <TableCell>
                      <b>Account</b>
                    </TableCell>
                    <TableCell>
                      <b>Location</b>
                    </TableCell>
                    <TableCell>
                      <b>Status</b>
                    </TableCell>
                    <TableCell>
                      <b>Actions</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedLocations &&
                    selectedLocations.map(
                      (x: ISelectionLocationWithPost, index: number) => (
                        <TableRow key={index}>
                          <TableCell>{x.locationInfo.businessName}</TableCell>
                          <TableCell>{x.locationInfo.accountName}</TableCell>
                          <TableCell>{x.locationInfo.locationName}</TableCell>
                          <TableCell>
                            {x.locationInfo.status == null ||
                            x.locationInfo.status === undefined ? (
                              <CircularProgress color="secondary" size="30px" />
                            ) : x.locationInfo.status ? (
                              <CheckCircleOutlineIcon color="success" />
                            ) : (
                              <CancelIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell>
                            {x.locationInfo.viewUrl ? (
                              <IconButton
                                onClick={() =>
                                  window.open(x.locationInfo.viewUrl, "_blank")
                                }
                                color="primary"
                                size="small"
                              >
                                <VisibilityIcon />
                              </IconButton>
                            ) : (
                              <BlockIcon color="error" />
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          disabled={!allApiCallsCompleted}
          onClick={() => navigate("/post-management/posts")}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GooglePostStatusDialog;
