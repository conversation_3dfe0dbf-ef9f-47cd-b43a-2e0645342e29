export interface IInstagramCreatePost {
  accountId: string;
  accountIds?: string[]; // For multiple account selection
  caption: string;
  mediaUrl: string;
  mediaType: "image" | "video";
  published: boolean;
  scheduledPublishTime?: string;
}

// Interface for bulk Instagram post creation
export interface IInstagramBulkCreatePost {
  accounts: IInstagramSelectedAccount[];
  caption: string;
  mediaUrl: string;
  mediaType: "image" | "video";
  published: boolean;
  scheduledPublishTime?: string;
}

// Interface for selected Instagram account with post data
export interface IInstagramSelectedAccount {
  accountId: string;
  accountName: string;
  accountUsername?: string;
  accountPictureUrl?: string;
  accountType?: string;
  status?: boolean;
  instagramUrl?: string;
  error?: string;
}

export interface IInstagramMedia {
  type: "image" | "video";
  url: string;
  caption?: string;
  sourceUrl?: string;
  mediaFormat?: string;
}
