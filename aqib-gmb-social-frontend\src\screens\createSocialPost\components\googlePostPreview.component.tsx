import React, { useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardMedia,
  Button,
  IconButton,
} from "@mui/material";
import {
  ImageOutlined as ImageOutlinedIcon,
  ArrowBackIos,
  ArrowForwardIos,
  Call as CallIcon,
  CalendarMonth as CalendarMonthIcon,
  ShoppingCart as ShoppingCartIcon,
  PersonAdd as PersonAddIcon,
  School as SchoolIcon,
} from "@mui/icons-material";
import { IGoogleCreatePost } from "../../../interfaces/request/IGoogleCreatePost";
import { EVENT_TYPE_CONFIGS, TOPIC_TYPES } from "../../../constants/application.constant";

interface GooglePostPreviewProps {
  values: IGoogleCreatePost;
  uploadedImages: any[];
  createPostInitials: IGoogleCreatePost;
}

const GooglePostPreview: React.FC<GooglePostPreviewProps> = ({
  values,
  uploadedImages,
  createPostInitials,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const iconMap: { [key: string]: JSX.Element } = {
    CallIcon: <CallIcon />,
    CalendarMonthIcon: <CalendarMonthIcon />,
    ShoppingCartIcon: <ShoppingCartIcon />,
    PersonAddIcon: <PersonAddIcon />,
    SchoolIcon: <SchoolIcon />,
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % uploadedImages.length);
  };

  const handlePrev = () => {
    setCurrentIndex(
      (prevIndex) =>
        (prevIndex - 1 + uploadedImages.length) % uploadedImages.length
    );
  };

  return (
    <Box>
      <Card
        elevation={3}
        sx={{
          borderRadius: 2,
          p: 2,
          mb: 2,
          maxWidth: "100%",
          mx: "auto",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: 200,
            backgroundColor: "#f9fafb",
            borderRadius: 2,
            mb: 2,
            mt: 2,
          }}
        >
          {uploadedImages &&
            uploadedImages.length === 0 &&
            createPostInitials.media &&
            createPostInitials.media.length > 0 && (
              <CardMedia
                component="div"
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  position: "relative",
                }}
              >
                <img
                  src={createPostInitials.media[currentIndex].sourceUrl}
                  alt={`Image ${currentIndex + 1}`}
                  style={{
                    width: "100%",
                    height: "242px",
                    objectFit: "cover",
                    borderRadius: "8px",
                    transition: "opacity 0.5s ease-in-out",
                  }}
                  referrerPolicy="no-referrer"
                />

                {/* Previous Button */}
                {createPostInitials.media.length > 1 && currentIndex > 0 && (
                  <IconButton
                    onClick={handlePrev}
                    sx={{
                      position: "absolute",
                      left: 10,
                      backgroundColor: "rgba(0,0,0,0.5)",
                      color: "white",
                      "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                    }}
                  >
                    <ArrowBackIos />
                  </IconButton>
                )}

                {/* Next Button */}
                {createPostInitials.media.length > 1 &&
                  currentIndex < createPostInitials.media.length && (
                    <IconButton
                      onClick={handleNext}
                      sx={{
                        position: "absolute",
                        right: 10,
                        backgroundColor: "rgba(0,0,0,0.5)",
                        color: "white",
                        "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                      }}
                    >
                      <ArrowForwardIos />
                    </IconButton>
                  )}
              </CardMedia>
            )}

          {uploadedImages && uploadedImages.length > 0 && (
            <CardMedia
              component="div"
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                position: "relative",
              }}
            >
              <img
                src={
                  (uploadedImages[currentIndex] as any).isFromGallery ||
                  (uploadedImages[currentIndex] as any).isFromExistingPost
                    ? (uploadedImages[currentIndex] as any).s3Url
                    : URL.createObjectURL(
                        uploadedImages[currentIndex] as unknown as MediaSource
                      )
                }
                alt={`Image ${currentIndex + 1}`}
                style={{
                  width: "100%",
                  height: "242px",
                  objectFit: "cover",
                  borderRadius: "8px",
                  transition: "opacity 0.5s ease-in-out",
                }}
                referrerPolicy="no-referrer"
                onError={(e) => {
                  // Fallback for broken images
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                  const fallbackDiv =
                    target.nextElementSibling as HTMLElement;
                  if (fallbackDiv) {
                    fallbackDiv.style.display = "flex";
                  }
                }}
              />
              <Box
                sx={{
                  width: "100%",
                  height: "242px",
                  display: "none",
                  alignItems: "center",
                  justifyContent: "center",
                  backgroundColor: "#f5f5f5",
                  border: "1px dashed #ccc",
                  borderRadius: "8px",
                }}
              >
                <Box textAlign="center">
                  <ImageOutlinedIcon
                    sx={{ fontSize: 60, color: "#ccc", mb: 1 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    Image not available
                  </Typography>
                </Box>
              </Box>

              {/* Previous Button */}
              {uploadedImages.length > 1 && currentIndex > 0 && (
                <IconButton
                  onClick={handlePrev}
                  sx={{
                    position: "absolute",
                    left: 10,
                    backgroundColor: "rgba(0,0,0,0.5)",
                    color: "white",
                    "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                  }}
                >
                  <ArrowBackIos />
                </IconButton>
              )}

              {/* Next Button */}
              {uploadedImages.length > 1 &&
                currentIndex < uploadedImages.length && (
                  <IconButton
                    onClick={handleNext}
                    sx={{
                      position: "absolute",
                      right: 10,
                      backgroundColor: "rgba(0,0,0,0.5)",
                      color: "white",
                      "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                    }}
                  >
                    <ArrowForwardIos />
                  </IconButton>
                )}
            </CardMedia>
          )}
          {uploadedImages &&
            uploadedImages.length === 0 &&
            createPostInitials.media &&
            createPostInitials.media.length === 0 && (
              <>
                <ImageOutlinedIcon sx={{ fontSize: 50, color: "#c4c4c4" }} />
                <Typography variant="body1" sx={{ mt: 1, color: "#6c757d" }}>
                  No Image Added
                </Typography>
              </>
            )}
        </Box>
        <CardContent>
          {values.topicType === TOPIC_TYPES.Event && (
            <Typography variant="h6" sx={{ fontWeight: 600 }} gutterBottom>
              {values.event?.title || ""}
            </Typography>
          )}

          <Typography variant="body2" color="text.secondary">
            {values.summary}
          </Typography>
        </CardContent>
        <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
          {values.callToAction != null &&
            EVENT_TYPE_CONFIGS.filter(
              (x) => x.key === values.callToAction?.actionType
            ).map((btn) => (
              <Button
                key={btn.key}
                variant="contained"
                color="primary"
                startIcon={iconMap[btn.icon]}
                sx={{ textTransform: "none", borderRadius: 2 }}
              >
                {btn.label}
              </Button>
            ))}
        </Box>
      </Card>
    </Box>
  );
};

export default GooglePostPreview;
