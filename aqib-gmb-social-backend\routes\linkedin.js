const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");
const {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getLinkedInAccounts,
  getPages,
  createPost,
} = require("../controllers/linkedin.controller");

// Welcome endpoint
router.get("/", welcome);

// Authentication endpoints
router.post("/authenticate", isAuthenticated, authenticate);
router.get("/callback", callback);
router.post("/callback-validation", callbackValidation);

// Account/Page management endpoints
router.get("/accounts/:userId", isAuthenticated, getLinkedInAccounts);
router.get("/pages/:userId", isAuthenticated, getPages);

// Post management endpoints
router.post("/posts/:userId", isAuthenticated, createPost);

module.exports = router;
