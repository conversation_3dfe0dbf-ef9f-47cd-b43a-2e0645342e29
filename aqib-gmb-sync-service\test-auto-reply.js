#!/usr/bin/env node

/**
 * Test script for Auto Reply Service
 * This script allows manual testing of the auto reply functionality
 */

require("dotenv").config();

const database = require("./config/database");
const autoReplyService = require("./services/autoReplyService");
const autoReplyModel = require("./models/autoReplyModel");
const logger = require("./utils/logger");

class AutoReplyTester {
  constructor() {
    this.startTime = new Date();
    this.testResults = {
      configsProcessed: 0,
      reviewsProcessed: 0,
      repliesSent: 0,
      errors: [],
    };
  }

  /**
   * Initialize and run the test
   */
  async run() {
    try {
      console.log("=".repeat(60));
      console.log("GMB Auto Reply Service - Manual Test");
      console.log("=".repeat(60));
      console.log(`Started at: ${this.startTime.toISOString()}`);
      console.log("");

      // Initialize database connection
      await this.initializeDatabase();

      // Display current configuration
      await this.displayConfiguration();

      // Display auto reply configurations
      await this.displayAutoReplyConfigs();

      // Display pending reviews
      await this.displayPendingReviews();

      // Run the auto reply process (unless config-only mode)
      if (!configOnly) {
        if (dryRun) {
          console.log("");
          console.log("🔍 DRY RUN MODE - No actual replies will be sent");
          console.log("-".repeat(40));
        }
        await this.runAutoReplyProcess();

        // Display results
        await this.displayResults();
      } else {
        console.log("");
        console.log("⏸️  Skipping auto reply process (--config-only mode)");
      }

      console.log("");
      console.log("=".repeat(60));
      console.log("Test completed successfully!");
      console.log("=".repeat(60));
    } catch (error) {
      console.error("Test failed:", error.message);
      logger.error("Auto reply test failed:", {
        error: error.message,
        stack: error.stack,
      });
      process.exit(1);
    } finally {
      // Close database connections
      await database.close();
    }
  }

  /**
   * Initialize database connection
   */
  async initializeDatabase() {
    try {
      console.log("Initializing database connection...");
      await database.initialize();
      console.log("✓ Database connection established");
    } catch (error) {
      console.error("✗ Database initialization failed:", error.message);
      throw error;
    }
  }

  /**
   * Display current configuration
   */
  async displayConfiguration() {
    console.log("");
    console.log("Configuration:");
    console.log("-".repeat(40));
    console.log(`Backend API URL: ${process.env.BACKEND_API_URL}`);
    console.log(`Service User ID: ${process.env.SERVICE_USER_ID}`);
    console.log(`Has Auth Token: ${!!process.env.SERVICE_AUTH_TOKEN}`);
    console.log(
      `Auto Reply Schedule: ${process.env.AUTO_REPLY_SCHEDULE || "*/5 * * * *"}`
    );
    console.log(`Debug Mode: ${verbose ? "✓" : "✗"}`);
    console.log(`Dry Run Mode: ${dryRun ? "✓" : "✗"}`);

    // Check if auto reply service is configured
    const isConfigured = !!(
      process.env.BACKEND_API_URL && process.env.SERVICE_AUTH_TOKEN
    );
    console.log(`Service Configured: ${isConfigured ? "✓" : "✗"}`);

    if (!isConfigured) {
      console.log("⚠️  Service is not properly configured!");
      console.log("Please check your environment variables.");
    }

    // Test backend connectivity
    await this.testBackendConnectivity();
  }

  /**
   * Test backend API connectivity
   */
  async testBackendConnectivity() {
    console.log("");
    console.log("Backend Connectivity Test:");
    console.log("-".repeat(30));

    try {
      const backendApiService = require("./services/backendApiService");

      // Test a simple AI generation call
      const testResult = await backendApiService.generateAIReply(
        "Test review for connectivity check",
        5
      );

      if (testResult.success) {
        console.log("✓ Backend API connectivity: OK");
        if (verbose) {
          console.log(
            `  Generated text length: ${
              testResult.data.generatedText?.length || 0
            } chars`
          );
        }
      } else {
        console.log("✗ Backend API connectivity: FAILED");
        console.log(`  Error: ${testResult.error}`);
      }
    } catch (error) {
      console.log("✗ Backend API connectivity: ERROR");
      console.log(`  Error: ${error.message}`);
    }
  }

  /**
   * Display auto reply configurations
   */
  async displayAutoReplyConfigs() {
    console.log("");
    console.log("Auto Reply Configurations:");
    console.log("-".repeat(40));

    try {
      const configs = await autoReplyModel.getEnabledAutoReplyBusinesses();

      if (configs.length === 0) {
        console.log("⚠️  No auto reply configurations found!");
        console.log(
          "Please set up auto reply configurations in the admin panel."
        );
        return;
      }

      console.log(`Found ${configs.length} auto reply configuration(s):`);
      console.log("");

      configs.forEach((config, index) => {
        console.log(
          `${index + 1}. ${config.businessName || "Unknown Business"}`
        );
        console.log(`   Business ID: ${config.business_id}`);
        console.log(`   Account ID: ${config.account_id || "All accounts"}`);
        console.log(`   Location ID: ${config.location_id || "All locations"}`);
        console.log(`   Hierarchy Level: ${config.hierarchy_level}`);
        console.log(`   Enabled: ${config.is_enabled ? "✓" : "✗"}`);
        console.log(
          `   Only Business Hours: ${config.only_business_hours ? "✓" : "✗"}`
        );
        if (config.only_business_hours) {
          console.log(
            `   Business Hours: ${config.business_hours_start} - ${config.business_hours_end}`
          );
        }
        console.log("");
      });
    } catch (error) {
      console.error(
        "✗ Failed to fetch auto reply configurations:",
        error.message
      );
    }
  }

  /**
   * Display pending reviews for auto reply
   */
  async displayPendingReviews() {
    console.log("");
    console.log("Pending Reviews for Auto Reply:");
    console.log("-".repeat(40));

    try {
      const configs = await autoReplyModel.getEnabledAutoReplyBusinesses();
      let totalPendingReviews = 0;

      for (const config of configs) {
        if (!config.is_enabled) continue;

        const reviews = await autoReplyModel.getPendingReviews(config);
        totalPendingReviews += reviews.length;

        if (reviews.length > 0) {
          console.log(
            `${config.businessName || "Unknown Business"} (${
              config.hierarchy_level
            }):`
          );
          console.log(`  ${reviews.length} pending review(s)`);

          // Show first few reviews as examples
          const samplesToShow = Math.min(3, reviews.length);
          for (let i = 0; i < samplesToShow; i++) {
            const review = reviews[i];
            console.log(
              `  - ${review.star_rating_numeric}⭐ "${
                review.comment?.substring(0, 50) || "No comment"
              }${review.comment?.length > 50 ? "..." : ""}"`
            );
          }
          if (reviews.length > samplesToShow) {
            console.log(`  ... and ${reviews.length - samplesToShow} more`);
          }
          console.log("");
        }
      }

      if (totalPendingReviews === 0) {
        console.log("✓ No pending reviews found for auto reply");
      } else {
        console.log(`Total pending reviews: ${totalPendingReviews}`);
      }
    } catch (error) {
      console.error("✗ Failed to fetch pending reviews:", error.message);
    }
  }

  /**
   * Run the auto reply process
   */
  async runAutoReplyProcess() {
    console.log("");
    console.log("Starting auto reply process...");
    console.log("-".repeat(40));

    try {
      const startTime = Date.now();

      // Run the auto reply service
      await autoReplyService.processAutoReplies();

      const duration = Date.now() - startTime;
      console.log(`✓ Auto reply process completed in ${duration}ms`);
    } catch (error) {
      console.error("✗ Auto reply process failed:", error.message);
      throw error;
    }
  }

  /**
   * Display auto reply results
   */
  async displayResults() {
    console.log("");
    console.log("Auto Reply Results:");
    console.log("-".repeat(40));

    const stats = autoReplyService.getStats();

    console.log(
      `Processing Status: ${stats.isProcessing ? "Running" : "Idle"}`
    );
    console.log(`Processed Count: ${stats.processedCount}`);
    console.log(`Error Count: ${stats.errorCount}`);
    console.log(`Last Run: ${stats.lastRun}`);

    // Get recent auto reply logs
    await this.displayRecentLogs();
  }

  /**
   * Display recent auto reply logs
   */
  async displayRecentLogs() {
    console.log("");
    console.log("Recent Auto Reply Activity:");
    console.log("-".repeat(30));

    try {
      // Query recent auto reply logs from database
      const query = `
        SELECT 
          r.reviewId,
          r.comment,
          r.star_rating_numeric,
          r.reviewerName,
          l.gmbLocationName,
          ar.reply_text,
          ar.created_at,
          ar.status
        FROM auto_replies ar
        JOIN reviews r ON ar.review_id = r.reviewId
        JOIN gmb_locations l ON r.locationId = l.gmbLocationId
        WHERE ar.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY ar.created_at DESC
        LIMIT 10
      `;

      const recentReplies = await database.query(query);

      if (recentReplies.length === 0) {
        console.log("No recent auto replies found in the last hour");
      } else {
        console.log(`Found ${recentReplies.length} recent auto reply(ies):`);
        console.log("");

        recentReplies.forEach((reply, index) => {
          console.log(`${index + 1}. ${reply.gmbLocationName}`);
          console.log(
            `   Review: ${reply.star_rating_numeric}⭐ "${
              reply.comment?.substring(0, 50) || "No comment"
            }${reply.comment?.length > 50 ? "..." : ""}"`
          );
          console.log(
            `   Reply: "${reply.reply_text?.substring(0, 50) || "No reply"}${
              reply.reply_text?.length > 50 ? "..." : ""
            }"`
          );
          console.log(`   Status: ${reply.status}`);
          console.log(`   Time: ${reply.created_at}`);
          console.log("");
        });
      }
    } catch (error) {
      console.error("✗ Failed to fetch recent logs:", error.message);
    }
  }
}

// Command line argument parsing
const args = process.argv.slice(2);
const showHelp = args.includes("--help") || args.includes("-h");
const dryRun = args.includes("--dry-run") || args.includes("-d");
const verbose = args.includes("--verbose") || args.includes("-v");
const configOnly = args.includes("--config-only") || args.includes("-c");

if (showHelp) {
  console.log("GMB Auto Reply Service Test");
  console.log("");
  console.log("Usage: node test-auto-reply.js [options]");
  console.log("");
  console.log("Options:");
  console.log("  --help, -h         Show this help message");
  console.log(
    "  --dry-run, -d      Show what would be processed without actually sending replies"
  );
  console.log("  --verbose, -v      Show detailed debug information");
  console.log(
    "  --config-only, -c  Only check configuration and pending reviews, don't run auto reply"
  );
  console.log("");
  console.log("This script will:");
  console.log("1. Check auto reply service configuration");
  console.log("2. Display current auto reply configurations");
  console.log("3. Show pending reviews for auto reply");
  console.log("4. Run the auto reply process (unless --config-only)");
  console.log("5. Display results and recent activity");
  console.log("");
  console.log("Examples:");
  console.log("  node test-auto-reply.js                    # Full test run");
  console.log(
    "  node test-auto-reply.js --config-only      # Check config only"
  );
  console.log(
    "  node test-auto-reply.js --dry-run          # Simulate without sending"
  );
  console.log("  node test-auto-reply.js --verbose          # Detailed output");
  console.log("");
  process.exit(0);
}

// Set global flags for debugging
if (verbose) {
  process.env.DEBUG_AUTO_REPLY = "true";
}
if (dryRun) {
  process.env.DRY_RUN_MODE = "true";
}

// Run the test
const tester = new AutoReplyTester();
tester.run().catch((error) => {
  console.error("Fatal error:", error);
  process.exit(1);
});
