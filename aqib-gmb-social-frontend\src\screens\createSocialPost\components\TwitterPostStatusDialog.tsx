import React from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogContentText,
  Box,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  CircularProgress,
  IconButton,
  styled,
  keyframes,
} from "@mui/material";
import {
  CheckCircleOutline as CheckCircleOutlineIcon,
  Cancel as CancelIcon,
  Twitter as TwitterIcon,
  OpenInNew as OpenInNewIcon,
  Verified as VerifiedIcon,
} from "@mui/icons-material";
import { ITwitterSelectedAccount } from "../../../interfaces/request/ITwitterCreatePost";

// Blinking animation for status text
const blink = keyframes`
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
`;

const BlinkingText = styled(Typography)`
  animation: ${blink} 1.5s infinite;
`;

interface ITwitterAccountPost {
  accountInfo: ITwitterSelectedAccount & {
    status?: boolean | null;
    error?: string;
    twitterUrl?: string;
  };
}

interface TwitterPostStatusDialogProps {
  open: boolean;
  selectedAccounts: ITwitterAccountPost[];
  postCreationProgress: {
    percent: number;
    status: string;
  };
}

const TwitterPostStatusDialog: React.FC<TwitterPostStatusDialogProps> = ({
  open,
  selectedAccounts,
  postCreationProgress,
}) => {
  const getProgressColor = () => {
    if (postCreationProgress.percent === 100) {
      const hasErrors = selectedAccounts.some(
        (account) => account.accountInfo.status === false
      );
      return hasErrors ? "#f44336" : "#4caf50"; // Red if errors, green if all success
    }
    return "#2196f3"; // Blue for in progress
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={() => console.log("On Close")}
    >
      <DialogTitle sx={{ display: "flex", alignItems: "center" }}>
        <TwitterIcon sx={{ mr: 1, color: "#1DA1F2" }} />
        Twitter Post Upload Status
      </DialogTitle>
      <Box sx={{ position: "relative", width: "100%" }}>
        <LinearProgress
          variant="determinate"
          value={postCreationProgress.percent}
          color="secondary"
          sx={{
            height: "20px",
            backgroundColor: "#d3d3d3",
            "& .MuiLinearProgress-bar": {
              backgroundColor: getProgressColor(),
            },
          }}
        />
        <BlinkingText
          variant="body2"
          sx={{
            position: "absolute",
            top: 0,
            left: "13%",
            transform: "translateX(-50%)",
            fontWeight: "bold",
            color: "#ffffff",
          }}
        >
          {postCreationProgress.status}...
        </BlinkingText>
      </Box>
      <DialogContent>
        <DialogContentText>
          <Box
            noValidate
            component="form"
            sx={{
              display: "flex",
              flexDirection: "column",
              m: "auto",
            }}
          >
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Account Name</b>
                    </TableCell>
                    <TableCell>
                      <b>Username</b>
                    </TableCell>
                    <TableCell>
                      <b>Followers</b>
                    </TableCell>
                    <TableCell>
                      <b>Status</b>
                    </TableCell>
                    <TableCell>
                      <b>Actions</b>
                    </TableCell>
                    <TableCell>
                      <b>Error Message</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedAccounts &&
                    selectedAccounts.map(
                      (account: ITwitterAccountPost, index: number) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {account.accountInfo.accountName}
                              </Typography>
                              {account.accountInfo.isVerified && (
                                <VerifiedIcon
                                  sx={{ ml: 0.5, fontSize: 16, color: "#1DA1F2" }}
                                />
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="textSecondary">
                              @{account.accountInfo.accountUsername}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatNumber(account.accountInfo.followersCount)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {account.accountInfo.status == null ? (
                              <CircularProgress color="secondary" size="30px" />
                            ) : account.accountInfo.status ? (
                              <CheckCircleOutlineIcon color="success" />
                            ) : (
                              <CancelIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell>
                            {account.accountInfo.status === true &&
                              account.accountInfo.twitterUrl && (
                                <IconButton
                                  size="small"
                                  onClick={() =>
                                    window.open(
                                      account.accountInfo.twitterUrl,
                                      "_blank"
                                    )
                                  }
                                  sx={{
                                    color: "#1DA1F2",
                                    "&:hover": { backgroundColor: "rgba(29, 161, 242, 0.1)" },
                                  }}
                                  title="View Tweet"
                                >
                                  <OpenInNewIcon fontSize="small" />
                                </IconButton>
                              )}
                          </TableCell>
                          <TableCell>
                            {account.accountInfo.status === false && (
                              <Typography
                                variant="caption"
                                color="error"
                                sx={{ maxWidth: 200, wordBreak: "break-word" }}
                              >
                                {account.accountInfo.error || "Unknown error occurred"}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Summary */}
            <Box sx={{ mt: 2, p: 2, backgroundColor: "#f5f5f5", borderRadius: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Summary:
              </Typography>
              <Box sx={{ display: "flex", gap: 3 }}>
                <Typography variant="body2">
                  <strong>Total Accounts:</strong> {selectedAccounts.length}
                </Typography>
                <Typography variant="body2" color="success.main">
                  <strong>Successful:</strong>{" "}
                  {selectedAccounts.filter((acc) => acc.accountInfo.status === true).length}
                </Typography>
                <Typography variant="body2" color="error.main">
                  <strong>Failed:</strong>{" "}
                  {selectedAccounts.filter((acc) => acc.accountInfo.status === false).length}
                </Typography>
                <Typography variant="body2" color="info.main">
                  <strong>Pending:</strong>{" "}
                  {selectedAccounts.filter((acc) => acc.accountInfo.status === null).length}
                </Typography>
              </Box>
            </Box>
          </Box>
        </DialogContentText>
      </DialogContent>
    </Dialog>
  );
};

export default TwitterPostStatusDialog;
