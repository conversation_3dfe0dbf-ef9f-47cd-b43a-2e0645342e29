import Avatar from "@mui/material/Avatar";
import { Box, Drawer, Grid, Grid2, Stack } from "@mui/material";

const GenericDrawer = (props: {
  component: React.ReactNode;
  isShow: boolean;
  width?: string;
  callback: () => undefined | null | void;
}) => {
  return (
    <Drawer
      anchor={"right"}
      open={props.isShow}
      ModalProps={
        {
          // onBackdropClick: () => props.callback(),
          // setOpenAddEdit({ isShow: false, data: null, userId: 0 }),
        }
      }
      sx={{
        "& .MuiDrawer-paper": {
          width: props.width ? props.width : "100%",
          minWidth: "400px",
          maxWidth: props.width ? props.width : "400px", // Set the max width
        },
        zIndex: (theme) => {
          return theme.zIndex.drawer + 1;
        },
      }}
      // PaperProps={{ style: { width: window.innerWidth * 0.25 } }}
    >
      <Box className="height100">{props.component}</Box>
    </Drawer>
  );
};

export default GenericDrawer;
