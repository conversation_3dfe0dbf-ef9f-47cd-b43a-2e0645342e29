const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  welcome,
  getReplyTemplates,
  createReplyTemplate,
  updateReplyTemplate,
  deleteReplyTemplate,
  getAutoReplySettings,
  updateAutoReplySettings,
  mapTemplateToBusinesses,
  getTemplateForAutoReply,
  generateTemplateTextWithAI,
} = require("../controllers/reviewSettings.controller");

router.get("/", welcome);

// Reply Templates Routes
router.get("/templates/:userId", isAuthenticated, getReplyTemplates);
router.post("/templates/:userId", isAuthenticated, createReplyTemplate);
router.put(
  "/templates/:userId/:templateId",
  isAuthenticated,
  updateReplyTemplate
);
router.delete(
  "/templates/:userId/:templateId",
  isAuthenticated,
  deleteReplyTemplate
);

// Business Template Mapping
router.post(
  "/templates/:userId/:templateId/map-businesses",
  isAuthenticated,
  mapTemplateToBusinesses
);

// Auto-Reply Settings Routes
router.get("/auto-reply/:businessId", isAuthenticated, getAutoReplySettings);
router.put("/auto-reply/:businessId", isAuthenticated, updateAutoReplySettings);

// Auto-Reply System Route (for internal use)
router.get(
  "/auto-reply-template/:businessId/:starRating",
  getTemplateForAutoReply
);

// AI Template Generation Route
router.post(
  "/generate-template-text",
  isAuthenticated,
  generateTemplateTextWithAI
);

module.exports = router;
