import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface IResponseTimeAnalysisChartProps {
  data: Record<string, number>;
  title?: string;
}

const ResponseTimeAnalysisChart: React.FC<IResponseTimeAnalysisChartProps> = ({
  data,
  title = "Response Time Distribution",
}) => {
  const theme = useTheme();

  // Define time ranges and their labels
  const timeRanges = [
    { key: "0-1h", label: "Within 1 hour", color: theme.palette.success.main },
    { key: "1-6h", label: "1-6 hours", color: theme.palette.info.main },
    { key: "6-24h", label: "6-24 hours", color: theme.palette.warning.main },
    { key: "1-3d", label: "1-3 days", color: theme.palette.error.light },
    { key: "3d+", label: "3+ days", color: theme.palette.error.main },
  ];

  const chartData = {
    labels: timeRanges.map(range => range.label),
    datasets: [
      {
        label: "Number of Replies",
        data: timeRanges.map(range => data[range.key] || 0),
        backgroundColor: timeRanges.map(range => range.color),
        borderColor: timeRanges.map(range => range.color),
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const total = timeRanges.reduce((sum, range) => sum + (data[range.key] || 0), 0);
            const percentage = total > 0 ? ((context.parsed.y / total) * 100).toFixed(1) : "0";
            return `${context.parsed.y} replies (${percentage}%)`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Response Time Range",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Number of Replies",
        },
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  };

  // Calculate statistics
  const totalReplies = timeRanges.reduce((sum, range) => sum + (data[range.key] || 0), 0);
  const quickReplies = (data["0-1h"] || 0) + (data["1-6h"] || 0); // Within 6 hours
  const quickResponseRate = totalReplies > 0 ? (quickReplies / totalReplies) * 100 : 0;
  
  // Calculate weighted average response time (approximate)
  const timeWeights = {
    "0-1h": 0.5,    // 30 minutes average
    "1-6h": 3.5,    // 3.5 hours average
    "6-24h": 15,    // 15 hours average
    "1-3d": 48,     // 2 days average
    "3d+": 96,      // 4 days average (conservative estimate)
  };
  
  const weightedSum = timeRanges.reduce((sum, range) => {
    return sum + (data[range.key] || 0) * timeWeights[range.key as keyof typeof timeWeights];
  }, 0);
  
  const avgResponseTime = totalReplies > 0 ? weightedSum / totalReplies : 0;
  
  const formatResponseTime = (hours: number): string => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${hours.toFixed(1)}h`;
    return `${(hours / 24).toFixed(1)}d`;
  };

  // Performance assessment
  const getPerformanceLevel = () => {
    if (quickResponseRate >= 80) return { level: "Excellent", color: theme.palette.success.main };
    if (quickResponseRate >= 60) return { level: "Good", color: theme.palette.info.main };
    if (quickResponseRate >= 40) return { level: "Fair", color: theme.palette.warning.main };
    return { level: "Needs Improvement", color: theme.palette.error.main };
  };

  const performance = getPerformanceLevel();

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 4, mb: 2, flexWrap: "wrap" }}>
          <Typography variant="body2" color="text.secondary">
            Total Replies: <strong>{totalReplies}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Quick Responses (≤6h): <strong>{quickReplies} ({quickResponseRate.toFixed(1)}%)</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Avg Response Time: <strong>{formatResponseTime(avgResponseTime)}</strong>
          </Typography>
          <Typography 
            variant="body2" 
            sx={{ 
              color: performance.color,
              fontWeight: "medium"
            }}
          >
            Performance: <strong>{performance.level}</strong>
          </Typography>
        </Box>
      </Box>

      {totalReplies > 0 ? (
        <Bar data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No reply data available for the selected period
          </Typography>
        </Box>
      )}

      {totalReplies > 0 && (
        <Box sx={{ mt: 3, p: 2, backgroundColor: "#f5f5f5", borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Response Time Guidelines:</strong>
            <br />
            • Excellent: 80%+ replies within 6 hours
            <br />
            • Good: 60-79% replies within 6 hours
            <br />
            • Fair: 40-59% replies within 6 hours
            <br />
            • Needs Improvement: &lt;40% replies within 6 hours
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ResponseTimeAnalysisChart;
