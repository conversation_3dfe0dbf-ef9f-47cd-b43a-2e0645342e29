import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale, // 👈 You need this
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale, // 👈 Register it here too
  ArcE<PERSON>,
  Toolt<PERSON>,
  Legend
);

interface Props {
  data: any;
}

const BusinessInteractionsChart: React.FC<Props> = ({ data }) => {
  // transform `data` to chartData here
  const chartData = {
    labels: ["Nov", "Dec", "Jan", "Feb", "Mar", "Apr"], // Replace with real dates
    datasets: [
      {
        label: "Interactions",
        data: [100, 150, 130, 160, 120, 110], // Replace with real values from `data`
        borderColor: "#1976d2",
        backgroundColor: "#90caf9",
        fill: true,
        tension: 0.4,
      },
    ],
  };

  return <Line data={chartData} />;
};

export default BusinessInteractionsChart;
