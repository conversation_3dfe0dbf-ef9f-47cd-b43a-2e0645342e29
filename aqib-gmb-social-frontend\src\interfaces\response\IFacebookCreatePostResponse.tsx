import { IFacebookMedia } from "../request/IFacebookCreatePost";

export interface IFacebookCreatePostResponse {
  success: boolean;
  message: string;
  data?: IFacebookPostData;
  error?: string;
}

export interface IFacebookPostData {
  id: string;
  postId: string;
  pageId: string;
  message: string;
  description?: string;
  link?: string;
  facebookUrl: string;
  status: string;
  createdTime: string;
  media?: IFacebookMedia[];
}

export interface IFacebookPagesResponse {
  success: boolean;
  message: string;
  pages?: IFacebookPageData[];
  error?: string;
}

export interface IFacebookPageData {
  id: number;
  user_id: number;
  business_id: number;
  page_id: string;
  page_name: string;
  page_access_token: string;
  page_category?: string;
  page_picture_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
