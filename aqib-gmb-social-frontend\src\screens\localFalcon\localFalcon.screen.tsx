import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import LocalFalconControls from "../../components/localFalcon/LocalFalconControls.component";
import LocalFalconMap from "../../components/localFalcon/LocalFalconMap.component";
import LocalFalconDashboard from "../../components/localFalcon/LocalFalconDashboard.component";
import LocalFalconService, {
  LocalFalconConfiguration,
  LocalFalconSearchRequest,
  LocalFalconScanRequest,
  LocalFalconGridPoint,
  LocalFalconRankingResult,
  LocalFalconScanResult,
  LocalFalconBusiness,
  LocalFalconTrendData,
  LocalFalconAlert,
} from "../../services/localFalcon/localFalcon.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";

interface LocalFalconScreenProps {
  title: string;
}

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  address: string;
  placeId: string;
}

const LocalFalconScreen: React.FC<LocalFalconScreenProps> = ({ title }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [localFalconService] = useState(new LocalFalconService(dispatch));
  const [loading, setLoading] = useState(false);

  // Toast context for notifications
  const { setToastConfig } = useContext(ToastContext);

  // State management
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [gridPoints, setGridPoints] = useState<LocalFalconGridPoint[]>([]);
  const [rankingResults, setRankingResults] = useState<
    LocalFalconRankingResult[]
  >([]);
  const [scanResult, setScanResult] = useState<LocalFalconScanResult | null>(
    null
  );
  const [businesses, setBusinesses] = useState<LocalFalconBusiness[]>([]);
  const [trendData, setTrendData] = useState<LocalFalconTrendData[]>([]);
  const [alerts, setAlerts] = useState<LocalFalconAlert[]>([]);

  const [configuration, setConfiguration] = useState<LocalFalconConfiguration>({
    name: "",
    keyword: "",
    businessName: "",
    placeId: "",
    centerLat: 0,
    centerLng: 0,
    gridSize: "5x5",
    radius: 1,
    unit: "kilometers",
    isScheduleEnabled: false,
    settings: {},
  });

  // UI state
  const [activeTab, setActiveTab] = useState(0);
  const [showHeatMap, setShowHeatMap] = useState(false);
  const [savedConfigurations, setSavedConfigurations] = useState<
    LocalFalconConfiguration[]
  >([]);

  const user = useSelector((state: any) => state.authReducer?.userInfo);

  useEffect(() => {
    document.title = title;
    if (user?.id) {
      loadSavedConfigurations();
      loadAlerts();
    }
  }, [title, user]);

  const loadSavedConfigurations = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await localFalconService.getConfigurations(user.id);
      if (response.success) {
        setSavedConfigurations(response.data);
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to load saved configurations",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const loadAlerts = async () => {
    if (!user?.id) return;

    try {
      const response = await localFalconService.getAlerts(user.id);
      if (response.success) {
        setAlerts(response.data);
      }
    } catch (error: any) {
      console.error("Failed to load alerts:", error);
    }
  };

  const handleLocationSearch = async (
    searchRequest: LocalFalconSearchRequest
  ) => {
    try {
      setLoading(true);

      const response = await localFalconService.searchPlaces(searchRequest);

      if (response.success && response.data.length > 0) {
        const locationData = response.data[0];
        setCurrentLocation({
          name: locationData.name,
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address,
          placeId: locationData.placeId,
        });

        setConfiguration((prev) => ({
          ...prev,
          centerLat: locationData.lat,
          centerLng: locationData.lng,
        }));

        // Auto-generate grid when location is found
        await generateGrid(locationData.lat, locationData.lng);

        setToastConfig(
          ToastSeverity.Success,
          "Location found and grid generated successfully!",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to search location",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const generateGrid = async (centerLat?: number, centerLng?: number) => {
    try {
      setLoading(true);

      const lat = centerLat || configuration.centerLat;
      const lng = centerLng || configuration.centerLng;

      if (!lat || !lng) {
        throw new Error("Center coordinates are required");
      }

      const response = await localFalconService.calculateGrid({
        lat,
        lng,
        gridSize: configuration.gridSize,
        radius: configuration.radius,
        unit: configuration.unit,
      });

      if (response.success) {
        setGridPoints(response.data.gridPoints);
        setToastConfig(
          ToastSeverity.Success,
          "Grid generated successfully!",
          true
        );
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to generate grid",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessSearch = async (query: string) => {
    if (!query || query.length < 3) return;

    try {
      const response = await localFalconService.searchPlaces({
        query,
        lat: configuration.centerLat || 0,
        lng: configuration.centerLng || 0,
        radius: configuration.radius,
        unit: configuration.unit,
      });

      if (response.success) {
        setBusinesses(response.data);
      }
    } catch (error: any) {
      console.error("Failed to search businesses:", error);
    }
  };

  const handleScan = async (scanRequest: LocalFalconScanRequest) => {
    try {
      setLoading(true);

      const response = await localFalconService.runGridScan(scanRequest);

      if (response.success) {
        const result = response.data;
        setScanResult(result);
        setRankingResults(result.rankings);
        setGridPoints(result.gridPoints);

        setToastConfig(
          ToastSeverity.Success,
          `Scan completed! Average position: ${result.averagePosition.toFixed(
            1
          )}, Visibility: ${result.visibilityPercentage.toFixed(1)}%`,
          true
        );

        // Switch to dashboard tab to show results
        setActiveTab(1);
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to run scan",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleConfigurationChange = (
    updates: Partial<LocalFalconConfiguration>
  ) => {
    setConfiguration((prev) => ({ ...prev, ...updates }));
  };

  const handleSaveConfiguration = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        throw new Error("Please log in to save configurations");
      }

      if (!configuration.name.trim()) {
        throw new Error("Configuration name is required");
      }

      const configToSave = {
        ...configuration,
        userId: user.id,
      };

      const response = await localFalconService.saveConfiguration(configToSave);

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Configuration saved successfully!",
          true
        );
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to save configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLoadConfiguration = async (config: LocalFalconConfiguration) => {
    try {
      setLoading(true);

      setConfiguration(config);
      setCurrentLocation({
        name: config.businessName || "Loaded Location",
        lat: config.centerLat,
        lng: config.centerLng,
        address: "",
        placeId: config.placeId || "",
      });

      // Load trend data for this configuration
      if (config.id) {
        const trendResponse = await localFalconService.getTrendData(
          config.id.toString()
        );
        if (trendResponse.success) {
          setTrendData(trendResponse.data);
        }
      }

      await generateGrid(config.centerLat, config.centerLng);

      setToastConfig(
        ToastSeverity.Success,
        "Configuration loaded successfully!",
        true
      );
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to load configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConfiguration = async (configId: number) => {
    try {
      setLoading(true);

      const response = await localFalconService.deleteConfiguration(
        configId.toString()
      );

      if (response.success) {
        setToastConfig(
          ToastSeverity.Success,
          "Configuration deleted successfully!",
          true
        );
        await loadSavedConfigurations();
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to delete configuration",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const handleAlertRead = async (alertId: number) => {
    try {
      await localFalconService.markAlertAsRead(alertId);
      setAlerts((prev) =>
        prev.map((alert) =>
          alert.id === alertId ? { ...alert, isRead: true } : alert
        )
      );
    } catch (error: any) {
      console.error("Failed to mark alert as read:", error);
    }
  };

  // Load sample scan data for testing
  const loadSampleData = async () => {
    try {
      setLoading(true);

      // Sample data from the JSON file
      const sampleScanResult: LocalFalconScanResult = {
        keyword: "eye hospital",
        businessName: "Sri Eye Care Speciality Eye Hospital",
        gridConfiguration: {
          lat: 13.0196844,
          lng: 77.6285592,
          gridSize: "5x5",
          radius: 1,
          unit: "kilometers",
        },
        gridPoints: [],
        rankings: [],
        averagePosition: 2.09,
        visibilityPercentage: 88,
        totalSearches: 25,
        foundInResults: 22,
        rawResponse: {
          code: 200,
          success: true,
          parameters: {
            keyword: "eye hospital",
            lat: "13.0196844",
            lng: "77.6285592",
            grid_size: "5",
            radius: "1",
            measurement: "km",
            place_id: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
          },
          data: {
            points: 25,
            found: 22,
            percent: 88,
            arp: 2.090909090909091,
            atrp: 6.88,
            solv: 72,
            results: [
              {
                lat: 13.010701247158805,
                lng: 77.61933902148155,
                found: true,
                rank: 3,
                count: 20,
                results: [
                  {
                    rank: 1,
                    place_id: "ChIJFULDvvMWrjsRcGgSqCCXsUI",
                    business: "Dr Agarwals Eye Hospital",
                    address:
                      "33, Coles Rd, opposite Bata Showroom, Cleveland Town, Frazer Town, Bengaluru, Karnataka 560005",
                    rating: 4.8,
                    reviews: 5499,
                    lat: 12.9962226,
                    lng: 77.6127119,
                  },
                  {
                    rank: 3,
                    place_id: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
                    business: "Sri Eye Care Speciality Eye Hospital",
                    address:
                      "HBR Layout 2nd Block, Stage 1, HBR Layout, Bengaluru, Karnataka 560043",
                    rating: 4.8,
                    reviews: 261,
                    lat: 13.0196844,
                    lng: 77.6285592,
                  },
                ],
              },
              {
                lat: 13.015192823579401,
                lng: 77.61933902148155,
                found: false,
                rank: false,
                count: 20,
                results: [],
              },
              {
                lat: 13.0196844,
                lng: 77.61933902148155,
                found: true,
                rank: 1,
                count: 20,
                results: [
                  {
                    rank: 1,
                    place_id: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
                    business: "Sri Eye Care Speciality Eye Hospital",
                    address:
                      "HBR Layout 2nd Block, Stage 1, HBR Layout, Bengaluru, Karnataka 560043",
                    rating: 4.8,
                    reviews: 261,
                    lat: 13.0196844,
                    lng: 77.6285592,
                  },
                ],
              },
            ],
          },
        },
      };

      // Set the sample data
      setScanResult(sampleScanResult);
      setCurrentLocation({
        name: sampleScanResult.businessName,
        lat: sampleScanResult.gridConfiguration.lat,
        lng: sampleScanResult.gridConfiguration.lng,
        address:
          "HBR Layout 2nd Block, Stage 1, HBR Layout, Bengaluru, Karnataka 560043",
        placeId: "ChIJceJaLR8XrjsRLtW3QoeKOxc",
      });

      setToastConfig(
        ToastSeverity.Success,
        "Sample scan data loaded successfully!",
        true
      );

      // Switch to dashboard tab to show results
      setActiveTab(1);
    } catch (error: any) {
      setToastConfig(ToastSeverity.Error, "Failed to load sample data", true);
    } finally {
      setLoading(false);
    }
  };

  // Show login message if user is not authenticated
  if (!user?.id) {
    return (
      <LeftMenuComponent>
        <Box>
          <Box sx={{ marginBottom: "5px" }}>
            <h3 className="pageTitle">Local Falcon</h3>
            <Typography variant="subtitle2" className="subtitle2">
              Advanced local search ranking analysis and competitor tracking
            </Typography>
          </Box>

          <Alert
            severity="warning"
            sx={{ mt: 2 }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => navigate("/")}
                variant="outlined"
              >
                Login
              </Button>
            }
          >
            Please log in to access Local Falcon functionality. This feature
            requires authentication to save configurations and track ranking
            history.
          </Alert>
        </Box>
      </LeftMenuComponent>
    );
  }

  return (
    <LeftMenuComponent>
      <Box>
        <Box sx={{ marginBottom: "5px" }}>
          <h3 className="pageTitle">Local Falcon</h3>
          <Typography variant="subtitle2" className="subtitle2">
            Advanced local search ranking analysis and competitor tracking
          </Typography>
        </Box>

        {/* Tab Navigation */}
        <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
          >
            <Tab label="Search & Scan" />
            <Tab label="Analytics Dashboard" />
          </Tabs>
        </Box>

        {/* Sample Data Button for Testing */}
        <Box sx={{ mb: 2 }}>
          <Button
            variant="outlined"
            color="secondary"
            onClick={loadSampleData}
            disabled={loading}
            size="small"
          >
            Load Sample Scan Data (Demo)
          </Button>
        </Box>

        {activeTab === 0 && (
          <Grid container spacing={3}>
            {/* Controls Panel */}
            <Grid item xs={12} md={4}>
              <LocalFalconControls
                onSearch={handleLocationSearch}
                onScan={handleScan}
                onSaveConfiguration={handleSaveConfiguration}
                loading={loading}
                currentLocation={currentLocation}
                savedConfigurations={savedConfigurations}
                onLoadConfiguration={handleLoadConfiguration}
                onDeleteConfiguration={handleDeleteConfiguration}
                configuration={configuration}
                onConfigurationChange={handleConfigurationChange}
                businesses={businesses}
                onBusinessSearch={handleBusinessSearch}
                localFalconService={localFalconService}
              />
            </Grid>

            {/* Map Panel */}
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 2, height: 600 }}>
                <LocalFalconMap
                  center={
                    currentLocation
                      ? { lat: currentLocation.lat, lng: currentLocation.lng }
                      : null
                  }
                  gridPoints={gridPoints}
                  rankingResults={rankingResults}
                  scanResult={scanResult}
                  loading={loading}
                  showHeatMap={showHeatMap}
                  onShowHeatMapChange={setShowHeatMap}
                />
              </Paper>
            </Grid>
          </Grid>
        )}

        {activeTab === 1 && (
          <Paper sx={{ p: 1 }}>
            <LocalFalconDashboard
              scanResult={scanResult}
              trendData={trendData}
              alerts={alerts}
              competitors={[]}
              loading={loading}
              onAlertRead={handleAlertRead}
            />
          </Paper>
        )}
      </Box>
    </LeftMenuComponent>
  );
};

export default LocalFalconScreen;
