import React, { useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Di<PERSON><PERSON>,
  Button,
  IconButton,
  Alert,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import AddServicesModal from "./addServices.component";
import EditServiceModal from "./editServiceModal.component";

interface Service {
  name: string;
  description: string;
  isGoogleSuggested?: boolean;
}

interface Category {
  name: string;
  isPrimary: boolean;
  services: Service[];
  googleSuggestedServices?: Array<{
    serviceTypeId: string;
    displayName: string;
  }>;
}

interface ServicesDisplayProps {
  categories: Category[];
  onUpdateCategories?: (updatedCategories: Category[]) => void;
}

const ServicesDisplay: React.FC<ServicesDisplayProps> = ({
  categories,
  onUpdateCategories,
}) => {
  const [addServicesModalOpen, setAddServicesModalOpen] = useState(false);
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState<
    number | null
  >(null);
  const [editServiceModalOpen, setEditServiceModalOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<{
    service: Service;
    categoryIndex: number;
    serviceIndex: number;
  } | null>(null);

  const handleOpenAddServicesModal = (categoryIndex: number) => {
    setSelectedCategoryIndex(categoryIndex);
    setAddServicesModalOpen(true);
  };

  const handleCloseAddServicesModal = () => {
    setAddServicesModalOpen(false);
    setSelectedCategoryIndex(null);
  };

  const handleAddService = (serviceName: string) => {
    if (selectedCategoryIndex !== null && onUpdateCategories) {
      const updatedCategories = [...safeCategories];
      updatedCategories[selectedCategoryIndex].services.push({
        name: serviceName,
        description: "",
      });
      onUpdateCategories(updatedCategories);
    }
    handleCloseAddServicesModal();
  };

  const handleDeleteCategory = (categoryIndex: number) => {
    if (onUpdateCategories) {
      const updatedCategories = safeCategories.filter(
        (_, index) => index !== categoryIndex
      );
      onUpdateCategories(updatedCategories);
    }
  };

  const handleEditService = (
    service: Service,
    categoryIndex: number,
    serviceIndex: number
  ) => {
    setSelectedService({ service, categoryIndex, serviceIndex });
    setEditServiceModalOpen(true);
  };

  const handleSaveService = (updatedService: {
    name: string;
    description: string;
    price?: string;
  }) => {
    if (selectedService && onUpdateCategories) {
      const updatedCategories = [...safeCategories];
      updatedCategories[selectedService.categoryIndex].services[
        selectedService.serviceIndex
      ] = {
        name: updatedService.name,
        description: updatedService.description,
      };
      onUpdateCategories(updatedCategories);
    }
    setEditServiceModalOpen(false);
    setSelectedService(null);
  };

  const handleDeleteService = () => {
    if (selectedService && onUpdateCategories) {
      const updatedCategories = [...safeCategories];
      updatedCategories[selectedService.categoryIndex].services.splice(
        selectedService.serviceIndex,
        1
      );
      onUpdateCategories(updatedCategories);
    }
    setEditServiceModalOpen(false);
    setSelectedService(null);
  };

  // Add debugging and safety checks
  console.log("ServicesDisplay - categories:", categories);

  // Safety check for categories
  const safeCategories = categories || [];

  return (
    <Box>
      {/* Pending review alert */}
      <Alert
        severity="warning"
        icon={<InfoOutlinedIcon />}
        sx={{
          mb: 2,
          bgcolor: "#FFF3CD",
          color: "#856404",
          border: "1px solid #FFEAA7",
          "& .MuiAlert-icon": {
            color: "#F39C12",
          },
          "& .MuiAlert-message": {
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            fontWeight: 400,
          },
          px: { xs: 1, sm: 1.5 },
          py: { xs: 0.5, sm: 0.75 },
          borderRadius: "5px",
        }}
        action={
          <Button
            size="small"
            sx={{
              fontSize: { xs: "0.7rem", sm: "0.8125rem" },
              p: { xs: "2px 4px", sm: "3px 9px" },
              color: "#309898",
              fontWeight: 500,
              textTransform: "none",
              "&:hover": {
                bgcolor: "#30989833",
              },
            }}
          >
            Learn more
          </Button>
        }
      >
        Your edit is pending review. It may take up to one day to be published.
      </Alert>

      {safeCategories.length === 0 ? (
        <Box sx={{ textAlign: "center", py: 2 }}>
          <Typography variant="body1" color="text.secondary">
            No categories available. Loading...
          </Typography>
        </Box>
      ) : (
        safeCategories.map((category, categoryIndex) => (
          <Box key={categoryIndex} sx={{ mb: { xs: 2, sm: 2.5 } }}>
            <Box sx={{ mb: 0.75 }}>
              <Typography
                variant="h6"
                sx={{
                  fontSize: { xs: "1rem", sm: "1.25rem" },
                  fontWeight: 600,
                  color: "#2F2F2F",
                  wordBreak: "break-word",
                  mb: 0.25,
                }}
              >
                {category.name}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  display: "block",
                  fontSize: { xs: "0.7rem", sm: "0.75rem" },
                  color: category.isPrimary ? "#309898" : "#757575",
                  fontWeight: 500,
                  textTransform: "uppercase",
                  letterSpacing: "0.5px",
                }}
              >
                {category.isPrimary
                  ? "Primary category"
                  : "Additional category"}
              </Typography>
            </Box>

            <Divider sx={{ my: 0.75, borderColor: "#EBEBEB" }} />

            {category.services.length === 0 ? (
              <Box
                sx={{
                  py: 1,
                  textAlign: "center",
                  color: "text.secondary",
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: { xs: "0.8rem", sm: "0.875rem" },
                    mb: 0.25,
                    color: "#757575",
                  }}
                >
                  No services added yet
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    fontSize: { xs: "0.7rem", sm: "0.75rem" },
                    color: "#9E9E9E",
                  }}
                >
                  Click "Add more services" to get started
                </Typography>
              </Box>
            ) : (
              <Box>
                {/* Google Suggested Services */}
                {(() => {
                  const googleServices = category.services.filter(
                    (service) => service.isGoogleSuggested
                  );
                  return googleServices.length > 0 ? (
                    <Box sx={{ mb: 2 }}>
                      {googleServices.map((service, serviceIndex) => (
                        <Box
                          key={`google-${serviceIndex}`}
                          onClick={() =>
                            handleEditService(
                              service,
                              categoryIndex,
                              category.services.indexOf(service)
                            )
                          }
                          sx={{
                            py: { xs: 1, sm: 1.25 },
                            px: { xs: 0.5, sm: 1 },
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: { xs: "flex-start", sm: "center" },
                            borderBottom: "1px solid #EBEBEB",
                            flexWrap: { xs: "wrap", sm: "nowrap" },
                            cursor: "pointer",
                            "&:hover": {
                              bgcolor: "#f4f4f4",
                            },
                          }}
                        >
                          <Box
                            sx={{
                              width: { xs: "calc(100% - 40px)", sm: "auto" },
                              flexGrow: 1,
                              pr: { xs: 1, sm: 2 },
                            }}
                          >
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontSize: { xs: "0.9rem", sm: "1rem" },
                                fontWeight: 500,
                                color: "#2F2F2F",
                                wordBreak: "break-word",
                                mb: 0.25,
                              }}
                            >
                              {service.name}
                            </Typography>
                            <Typography
                              variant="body2"
                              sx={{
                                fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                color: "#464255",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                                lineHeight: 1.3,
                              }}
                            >
                              {service.description}
                            </Typography>
                          </Box>
                          <IconButton
                            sx={{
                              p: { xs: 0.5, sm: 0.75 },
                              alignSelf: { xs: "center", sm: "auto" },
                            }}
                          >
                            <ChevronRightIcon
                              sx={{
                                fontSize: { xs: "1.25rem", sm: "1.5rem" },
                              }}
                            />
                          </IconButton>
                        </Box>
                      ))}
                    </Box>
                  ) : null;
                })()}
              </Box>
            )}

            <Box
              sx={{
                mt: 1,
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                flexWrap: "wrap",
                gap: 1,
              }}
            >
              <Button
                startIcon={<AddIcon />}
                onClick={() => handleOpenAddServicesModal(categoryIndex)}
                size="small"
                sx={{
                  color: "#309898",
                  textTransform: "none",
                  fontSize: { xs: "0.8rem", sm: "0.875rem" },
                  fontWeight: 500,
                  px: { xs: 1, sm: 1.5 },
                  py: { xs: 0.5, sm: 0.625 },
                  border: "1px solid #309898",
                  borderRadius: "5px",
                  "&:hover": {
                    bgcolor: "#30989833",
                    borderColor: "#309898",
                  },
                  "& .MuiButton-startIcon": {
                    mr: { xs: 0.5, sm: 1 },
                  },
                }}
              >
                Add more services
              </Button>

              {!category.isPrimary && (
                <Button
                  startIcon={<DeleteOutlineIcon />}
                  onClick={() => handleDeleteCategory(categoryIndex)}
                  size="small"
                  sx={{
                    textTransform: "none",
                    fontSize: { xs: "0.8rem", sm: "0.875rem" },
                    color: "#F24245",
                    fontWeight: 500,
                    px: { xs: 1, sm: 1.5 },
                    py: { xs: 0.5, sm: 0.625 },
                    border: "1px solid #F24245",
                    borderRadius: "5px",
                    "&:hover": {
                      bgcolor: "#********",
                      borderColor: "#F24245",
                    },
                    "& .MuiButton-startIcon": {
                      mr: { xs: 0.5, sm: 1 },
                    },
                  }}
                >
                  Delete
                </Button>
              )}
            </Box>

            {categoryIndex < safeCategories.length - 1 && (
              <Divider sx={{ my: { xs: 1.5, sm: 2 } }} />
            )}
          </Box>
        ))
      )}

      {/* Add another business category button */}
      <Box
        sx={{
          mt: { xs: 2, sm: 2.5 },
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          size="small"
          onClick={() => {
            // Add a new category
            if (onUpdateCategories) {
              const newCategory = {
                name: "New Category",
                isPrimary: false,
                services: [],
              };
              onUpdateCategories([...safeCategories, newCategory]);
            }
          }}
          sx={{
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            fontWeight: 500,
            py: { xs: 0.625, sm: 0.75 },
            px: { xs: 1.25, sm: 1.75 },
            color: "#309898",
            borderColor: "#309898",
            borderRadius: "5px",
            "&:hover": {
              bgcolor: "#30989833",
              borderColor: "#309898",
            },
            "& .MuiButton-startIcon": {
              mr: { xs: 0.5, sm: 1 },
            },
          }}
        >
          Add another business category
        </Button>
      </Box>

      {/* Add Services Modal */}
      {selectedCategoryIndex !== null && (
        <AddServicesModal
          open={addServicesModalOpen}
          onClose={handleCloseAddServicesModal}
          categoryName={safeCategories[selectedCategoryIndex]?.name || ""}
          isPrimary={safeCategories[selectedCategoryIndex]?.isPrimary || false}
          onAddService={handleAddService}
        />
      )}

      {/* Edit Service Modal */}
      <EditServiceModal
        open={editServiceModalOpen}
        onClose={() => {
          setEditServiceModalOpen(false);
          setSelectedService(null);
        }}
        service={selectedService?.service || null}
        onSave={handleSaveService}
        onDelete={handleDeleteService}
      />
    </Box>
  );
};

export default ServicesDisplay;
