import { Dispatch } from "react";
import { Action } from "redux";
import HttpHelperService from "../httpHelper.service";

// Local Falcon API Interfaces
export interface LocalFalconGridRequest {
  lat: number;
  lng: number;
  gridSize: string; // "3x3", "4x4", "5x5", etc. up to "21x21"
  radius: number;
  unit: "miles" | "kilometers" | "meters";
}

export interface LocalFalconGridPoint {
  lat: number;
  lng: number;
  index: number;
  gridPosition: {
    row: number;
    col: number;
  };
}

export interface LocalFalconSearchRequest {
  query: string;
  lat: number;
  lng: number;
  radius?: number;
  unit?: "miles" | "kilometers" | "meters";
  near?: string;
}

export interface LocalFalconScanRequest {
  keyword: string;
  businessName: string;
  lat: number;
  lng: number;
  gridSize: string;
  radius: number;
  unit: "miles" | "kilometers" | "meters";
  placeId?: string;
}

export interface LocalFalconRankingRequest {
  keyword: string;
  businessName: string;
  lat: number;
  lng: number;
  placeId?: string;
}

export interface LocalFalconBusiness {
  name: string;
  placeId: string;
  address: string;
  rating: number;
  totalRatings: number;
  lat: number;
  lng: number;
  isServiceAreaBusiness: boolean;
}

export interface LocalFalconRankingResult {
  position: number;
  business: LocalFalconBusiness;
  distance: number;
  gridPoint: LocalFalconGridPoint;
  searchResults: LocalFalconBusiness[];
}

export interface LocalFalconScanResult {
  keyword: string;
  businessName: string;
  gridConfiguration: LocalFalconGridRequest;
  gridPoints: LocalFalconGridPoint[];
  rankings: LocalFalconRankingResult[];
  averagePosition: number;
  visibilityPercentage: number;
  totalSearches: number;
  foundInResults: number;
  rawResponse?: any; // Raw API response from Local Falcon
}

export interface LocalFalconConfiguration {
  id?: number;
  userId?: number;
  name: string;
  keyword: string;
  businessName: string;
  placeId?: string;
  centerLat: number;
  centerLng: number;
  gridSize: string;
  radius: number;
  unit: "miles" | "kilometers" | "meters";
  isScheduleEnabled: boolean;
  scheduleFrequency?: "daily" | "weekly" | "monthly";
  alertThreshold?: number;
  settings?: any;
}

export interface LocalFalconAlert {
  id: number;
  configurationId: number;
  alertType: "ranking_drop" | "ranking_improvement" | "not_found";
  message: string;
  previousPosition?: number;
  currentPosition?: number;
  createdAt: string;
  isRead: boolean;
}

export interface LocalFalconTrendData {
  date: string;
  averagePosition: number;
  visibilityPercentage: number;
  totalSearches: number;
  foundInResults: number;
}

class LocalFalconService {
  private _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  /**
   * Calculate grid points from base coordinate
   */
  calculateGrid = async (gridRequest: LocalFalconGridRequest) => {
    return await this._httpHelperService.post(
      "/local-falcon/grid",
      gridRequest
    );
  };

  /**
   * Search for location suggestions using backend API
   */
  searchLocationSuggestions = async (query: string, near?: string) => {
    const params = new URLSearchParams({ query });
    if (near) {
      params.append("near", near);
    }

    return await this._httpHelperService.get(
      `/local-falcon/location-suggestions?${params.toString()}`
    );
  };

  /**
   * Search for Google My Business locations using backend API
   */
  searchPlaces = async (searchRequest: LocalFalconSearchRequest) => {
    return await this._httpHelperService.post(
      "/local-falcon/places",
      searchRequest
    );
  };

  /**
   * Get business ranking at specific coordinate point
   */
  getBusinessRanking = async (rankingRequest: LocalFalconRankingRequest) => {
    return await this._httpHelperService.post(
      "/local-falcon/ranking",
      rankingRequest
    );
  };

  /**
   * Perform keyword search at specific coordinate point
   */
  performSearch = async (searchRequest: LocalFalconSearchRequest) => {
    return await this._httpHelperService.post(
      "/local-falcon/search",
      searchRequest
    );
  };

  /**
   * Run a full grid scan
   */
  runGridScan = async (scanRequest: LocalFalconScanRequest) => {
    return await this._httpHelperService.post(
      "/local-falcon/scan",
      scanRequest
    );
  };

  /**
   * Save Local Falcon configuration
   */
  saveConfiguration = async (config: LocalFalconConfiguration) => {
    return await this._httpHelperService.post(
      "/local-falcon/configurations",
      config
    );
  };

  /**
   * Get all saved configurations for user
   */
  getConfigurations = async (userId: number) => {
    return await this._httpHelperService.get(
      `/local-falcon/configurations?userId=${userId}`
    );
  };

  /**
   * Get specific configuration by ID
   */
  getConfiguration = async (configId: string) => {
    return await this._httpHelperService.get(
      `/local-falcon/configurations/${configId}`
    );
  };

  /**
   * Update configuration
   */
  updateConfiguration = async (
    configId: string,
    config: Partial<LocalFalconConfiguration>
  ) => {
    return await this._httpHelperService.put(
      `/local-falcon/configurations/${configId}`,
      config
    );
  };

  /**
   * Delete configuration
   */
  deleteConfiguration = async (configId: string) => {
    return await this._httpHelperService.delete(
      `/local-falcon/configurations/${configId}`
    );
  };

  /**
   * Get scan history for configuration
   */
  getScanHistory = async (configId: string, limit: number = 30) => {
    return await this._httpHelperService.get(
      `/local-falcon/configurations/${configId}/history?limit=${limit}`
    );
  };

  /**
   * Get trend data for configuration
   */
  getTrendData = async (configId: string, days: number = 30) => {
    return await this._httpHelperService.get(
      `/local-falcon/configurations/${configId}/trends?days=${days}`
    );
  };

  /**
   * Get alerts for user
   */
  getAlerts = async (userId: number, unreadOnly: boolean = false) => {
    return await this._httpHelperService.get(
      `/local-falcon/alerts?userId=${userId}&unreadOnly=${unreadOnly}`
    );
  };

  /**
   * Mark alert as read
   */
  markAlertAsRead = async (alertId: number) => {
    return await this._httpHelperService.put(
      `/local-falcon/alerts/${alertId}/read`,
      {}
    );
  };

  /**
   * Get competitor analysis
   */
  getCompetitorAnalysis = async (configId: string) => {
    return await this._httpHelperService.get(
      `/local-falcon/configurations/${configId}/competitors`
    );
  };
}

export default LocalFalconService;
