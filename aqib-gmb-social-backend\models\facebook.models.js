const pool = require("../config/db");
const logger = require("../utils/logger");

module.exports = class Facebook {
  /**
   * Save Facebook OAuth tokens
   * @param {Object} tokenData - Token data
   * @returns {Promise<Object>} Save result
   */
  static async saveOAuthTokens(tokenData) {
    try {
      const query = `
        INSERT INTO facebook_oauth_tokens
        (user_id, facebook_user_id, facebook_user_name, facebook_user_email, facebook_user_picture, access_token, refresh_token, expires_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        facebook_user_name = VALUES(facebook_user_name),
        facebook_user_email = VALUES(facebook_user_email),
        facebook_user_picture = VALUES(facebook_user_picture),
        access_token = VALUES(access_token),
        refresh_token = VALUES(refresh_token),
        expires_at = VALUES(expires_at),
        updated_at = CURRENT_TIMESTAMP
      `;

      const values = [
        tokenData.userId,
        tokenData.facebookUserId,
        tokenData.facebookUserName || null,
        tokenData.facebookUserEmail || null,
        tokenData.facebookUserPicture || null,
        tokenData.accessToken,
        tokenData.refreshToken || null,
        tokenData.expiresAt || null,
      ];

      const result = await pool.query(query, values);

      logger.info("Facebook OAuth tokens saved successfully", {
        userId: tokenData.userId,
        facebookUserId: tokenData.facebookUserId,
        facebookUserEmail: tokenData.facebookUserEmail,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving Facebook OAuth tokens:", {
        error: error.message,
        userId: tokenData.userId,
        facebookUserId: tokenData.facebookUserId,
      });
      throw error;
    }
  }

  /**
   * Save Facebook pages
   * @param {Array} pagesData - Array of page data
   * @returns {Promise<Object>} Save result
   */
  static async savePages(pagesData) {
    try {
      const query = `
        INSERT INTO facebook_pages
        (facebook_oauth_token_id, page_id, page_name, page_access_token, page_category, page_picture_url)
        VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        page_name = VALUES(page_name),
        page_access_token = VALUES(page_access_token),
        page_category = VALUES(page_category),
        page_picture_url = VALUES(page_picture_url),
        is_active = 1,
        updated_at = CURRENT_TIMESTAMP
      `;

      const results = [];
      for (const page of pagesData) {
        const values = [
          page.facebookOAuthTokenId,
          page.pageId,
          page.pageName,
          page.pageAccessToken,
          page.pageCategory || null,
          page.pagePictureUrl || null,
        ];
        const result = await pool.query(query, values);
        results.push(result);
      }

      logger.info("Facebook pages saved successfully", {
        pagesCount: pagesData.length,
        userId: pagesData[0]?.userId,
      });

      return { success: true, results };
    } catch (error) {
      logger.error("Error saving Facebook pages:", {
        error: error.message,
        pagesCount: pagesData.length,
      });
      throw error;
    }
  }

  /**
   * Get Facebook accounts for user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Facebook accounts result
   */
  static async getFacebookAccounts(userId) {
    try {
      const query = `
        SELECT id, user_id, facebook_user_id, facebook_user_name, facebook_user_email,
               facebook_user_picture, created_at, updated_at
        FROM facebook_oauth_tokens
        WHERE user_id = ? AND status_id = 1
        ORDER BY created_at DESC
      `;

      const results = await pool.query(query, [userId]);

      logger.info("Facebook accounts retrieved", {
        userId,
        accountsCount: results.length,
      });

      return { success: true, accounts: results };
    } catch (error) {
      logger.error("Error getting Facebook accounts:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Get Facebook pages for user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Pages result
   */
  static async getPages(userId) {
    try {
      const query = `
        SELECT fp.*, fot.facebook_user_name, fot.facebook_user_email
        FROM facebook_pages fp
        INNER JOIN facebook_oauth_tokens fot ON fp.facebook_oauth_token_id = fot.id
        WHERE fot.user_id = ? AND fp.is_active = 1 AND fot.status_id = 1
        ORDER BY fp.page_name
      `;

      const results = await pool.query(query, [userId]);

      logger.info("Facebook pages retrieved", {
        userId,
        pagesCount: results.length,
      });

      return { success: true, pages: results };
    } catch (error) {
      logger.error("Error getting Facebook pages:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Save Facebook post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Save result
   */
  static async savePost(postData) {
    try {
      const query = `
        INSERT INTO facebook_posts
        (user_id, page_id, facebook_post_id, bulk_post_id,
         is_bulk_post, post_content, post_response, message, description,
         link, published, scheduled_publish_time, status, facebook_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        postData.userId,
        postData.pageId,
        postData.facebookPostId || null,
        postData.bulkPostId || null,
        postData.isBulkPost || false,
        JSON.stringify(postData.postContent),
        JSON.stringify(postData.postResponse || {}),
        postData.message,
        postData.description || null,
        postData.link || null,
        postData.published !== undefined ? postData.published : true,
        postData.scheduledPublishTime || null,
        postData.status || "published",
        postData.facebookUrl || null,
      ];

      const result = await pool.query(query, values);

      logger.info("Facebook post saved successfully", {
        userId: postData.userId,
        pageId: postData.pageId,
        postId: result.insertId,
      });

      return { success: true, postId: result.insertId };
    } catch (error) {
      logger.error("Error saving Facebook post:", {
        error: error.message,
        userId: postData.userId,
        pageId: postData.pageId,
      });
      throw error;
    }
  }

  /**
   * Get Facebook posts
   * @param {number} userId - User ID
   * @param {string} pageId - Page ID (optional)
   * @param {number} page - Page number
   * @param {number} limit - Limit per page
   * @returns {Promise<Object>} Posts result
   */
  static async getPosts(userId, pageId = null, page = 1, limit = 10) {
    try {
      let query = `
        SELECT fp.*, fpg.page_name, fot.facebook_user_name, fot.facebook_user_email
        FROM facebook_posts fp
        LEFT JOIN facebook_pages fpg ON fp.page_id = fpg.page_id
        LEFT JOIN facebook_oauth_tokens fot ON fpg.facebook_oauth_token_id = fot.id
        WHERE fp.user_id = ?
      `;
      let params = [userId];

      if (pageId) {
        query += ` AND fp.page_id = ?`;
        params.push(pageId);
      }

      query += ` ORDER BY fp.created_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, (page - 1) * limit);

      const results = await pool.query(query, params);

      logger.info("Facebook posts retrieved", {
        userId,
        pageId,
        postsCount: results.length,
      });

      return { success: true, posts: results };
    } catch (error) {
      logger.error("Error getting Facebook posts:", {
        error: error.message,
        userId,
        pageId,
      });
      throw error;
    }
  }
};
