import React, { useState } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import { theme } from "../../theme";

ChartJS.register(ArcElement, Tooltip, Legend);

const HomeChartCard = () => {
  const [showValue, setShowValue] = useState(true);

  const totalOrderData = {
    datasets: [
      {
        data: [81, 19],
        backgroundColor: [theme.palette.secondary?.main, "#F9E0F2"],
        borderWidth: 0,
      },
    ],
  };

  const customerGrowthData = {
    datasets: [
      {
        data: [22, 78],
        backgroundColor: ["#06A27A", "#E2F3ED"],
        borderWidth: 0,
      },
    ],
  };

  const options = {
    cutout: "75%",
    plugins: {
      tooltip: { enabled: false },
      legend: { display: false },
    },
  };

  const centerTextStyle = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    fontWeight: "bold",
    fontSize: "14px",
  };

  return (
    <Box mb={4}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        className=""
        mb={2}
      >
        <Typography variant="subtitle1" fontWeight="bold">
          Stars Count
        </Typography>
        <Box display="flex" alignItems="center" gap={1}>
          <FormControlLabel control={<Checkbox size="small" />} label="Chart" />
          <FormControlLabel
            control={
              <Checkbox
                size="small"
                checked={showValue}
                onChange={() => setShowValue(!showValue)}
              />
            }
            label="Show Value"
          />
        </Box>
      </Box>
      <Box
        className="pieChartInner"
        display="flex"
        justifyContent="space-around"
      >
        {/* Total Order Chart */}
        <Box position="relative" width={250} height={250}>
          <Doughnut data={totalOrderData} options={options} />
          {showValue && <Box sx={centerTextStyle}>81%</Box>}
          <Typography align="center" mt={1}>
            Total Order
          </Typography>
        </Box>

        {/* Customer Growth Chart */}
        <Box position="relative" width={250} height={250}>
          <Doughnut data={customerGrowthData} options={options} />
          {showValue && <Box sx={centerTextStyle}>22%</Box>}
          <Typography align="center" mt={1}>
            Customer Growth
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default HomeChartCard;
