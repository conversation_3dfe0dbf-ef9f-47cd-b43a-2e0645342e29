import React from "react";
import { But<PERSON>, <PERSON>, Typography } from "@mui/material";
import { useNavigate } from "react-router-dom";

interface InstagramLoginProps {
  userId: number;
  businessId: number;
}

const InstagramLogin: React.FC<InstagramLoginProps> = ({
  userId,
  businessId,
}) => {
  const CLIENT_ID = "686523524091533";
  const REDIRECT_URI = "http://localhost:3000/v1/instagram/callback";

  const handleInstagramLogin = () => {
    // Include userId and businessId in state parameter
    const state = JSON.stringify({ userId, businessId });

    // Use only valid Facebook API scopes for Instagram Business API
    const scopes = [
      "public_profile",
      "email",
      "pages_show_list",
      "pages_read_engagement",
      "pages_manage_posts",
    ];

    const authUrl =
      `https://www.facebook.com/v20.0/dialog/oauth` +
      `?client_id=${CLIENT_ID}` +
      `&redirect_uri=${encodeURIComponent(REDIRECT_URI)}` +
      `&scope=${encodeURIComponent(scopes.join(","))}` +
      `&response_type=code` +
      `&state=${encodeURIComponent(state)}`;

    window.location.href = authUrl;
  };

  return (
    <Box sx={{ textAlign: "center", my: 2 }}>
      <Button
        onClick={handleInstagramLogin}
        variant="contained"
        sx={{
          bgcolor: "#e1306c",
          "&:hover": { bgcolor: "#c13584" },
          color: "white",
          px: 3,
          py: 1,
        }}
      >
        Connect Instagram Account
      </Button>
      <Typography
        variant="caption"
        sx={{ display: "block", mt: 1, color: "text.secondary" }}
      >
        Connect your Instagram Business account to manage posts
      </Typography>
    </Box>
  );
};

export default InstagramLogin;
