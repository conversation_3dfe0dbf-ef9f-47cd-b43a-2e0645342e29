import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import PhoneIcon from "@mui/icons-material/Phone";
import { Formik, Form, FormikHelpers } from "formik";
import * as yup from "yup";

interface PhoneNumber {
  number: string;
  type: string;
}

interface PhoneNumberFormValues {
  phoneNumber: string;
  phoneType: string;
}

interface PhoneNumberManagementProps {
  open: boolean;
  onClose: () => void;
  phoneNumbers: PhoneNumber[];
  onAddPhoneNumber: (phoneNumber: string, phoneType: string) => void;
  onRemovePhoneNumber: (phoneNumber: string) => void;
  onUpdatePhoneNumber?: (oldNumber: string, newNumber: string, phoneType: string) => void;
}

const PhoneNumberManagement: React.FC<PhoneNumberManagementProps> = ({
  open,
  onClose,
  phoneNumbers,
  onAddPhoneNumber,
  onRemovePhoneNumber,
  onUpdatePhoneNumber,
}) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [editingPhone, setEditingPhone] = useState<PhoneNumber | null>(null);

  const phoneTypes = [
    { value: "PRIMARY", label: "Primary" },
    { value: "MOBILE", label: "Mobile" },
    { value: "FAX", label: "Fax" },
    { value: "LANDLINE", label: "Landline" },
    { value: "TOLL_FREE", label: "Toll Free" },
  ];

  const validationSchema = yup.object({
    phoneNumber: yup
      .string()
      .required("Phone number is required")
      .matches(
        /^[\+]?[1-9][\d]{0,15}$/,
        "Please enter a valid phone number"
      ),
    phoneType: yup
      .string()
      .required("Phone type is required"),
  });

  const initialValues: PhoneNumberFormValues = {
    phoneNumber: editingPhone?.number || "",
    phoneType: editingPhone?.type || "PRIMARY",
  };

  const handleSubmit = async (
    values: PhoneNumberFormValues,
    { setSubmitting, resetForm }: FormikHelpers<PhoneNumberFormValues>
  ) => {
    try {
      if (editingPhone && onUpdatePhoneNumber) {
        // Update existing phone number
        onUpdatePhoneNumber(editingPhone.number, values.phoneNumber, values.phoneType);
      } else {
        // Add new phone number
        onAddPhoneNumber(values.phoneNumber, values.phoneType);
      }
      
      resetForm();
      setEditingPhone(null);
    } catch (error) {
      console.error("Error managing phone number:", error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEdit = (phone: PhoneNumber) => {
    setEditingPhone(phone);
  };

  const handleCancelEdit = () => {
    setEditingPhone(null);
  };

  const handleDelete = (phoneNumber: string) => {
    onRemovePhoneNumber(phoneNumber);
  };

  const formatPhoneNumber = (phone: string) => {
    // Basic phone number formatting
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      fullScreen={fullScreen}
      className="commonDialog"
      PaperProps={{
        style: {
          backgroundColor: "white",
          borderRadius: "8px",
          maxWidth: fullScreen ? "100%" : "600px",
        },
      }}
    >
      <DialogTitle
        className="modal-modal-title"
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: { xs: "16px", sm: "20px 24px" },
          borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        <Typography
          variant="h6"
          component="div"
          sx={{
            fontWeight: 600,
            color: "black",
            fontSize: { xs: "1.1rem", sm: "1.25rem" },
          }}
        >
          Manage Phone Numbers
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
          className="closeBtn"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent
        sx={{
          padding: { xs: "16px", sm: "24px" },
          backgroundColor: "white",
        }}
      >
        <Typography
          variant="body2"
          color="textSecondary"
          sx={{
            mb: 3,
            color: "rgba(0, 0, 0, 0.6)",
            fontSize: { xs: "0.875rem", sm: "1rem" },
          }}
        >
          Add and manage phone numbers for your business. Customers will be able to call these numbers directly from your Google Business Profile.
        </Typography>

        {/* Existing Phone Numbers */}
        {phoneNumbers.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontSize: "1.1rem",
                fontWeight: 600,
                color: "black",
              }}
            >
              Current Phone Numbers
            </Typography>
            <List>
              {phoneNumbers.map((phone, index) => (
                <ListItem
                  key={index}
                  sx={{
                    border: "1px solid rgba(0, 0, 0, 0.12)",
                    borderRadius: "8px",
                    mb: 1,
                    backgroundColor: "rgba(0, 0, 0, 0.02)",
                  }}
                >
                  <PhoneIcon sx={{ mr: 2, color: "text.secondary" }} />
                  <ListItemText
                    primary={formatPhoneNumber(phone.number)}
                    secondary={
                      <Chip
                        label={phone.type || "Primary"}
                        size="small"
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      aria-label="edit"
                      onClick={() => handleEdit(phone)}
                      sx={{ mr: 1 }}
                    >
                      <CloseIcon />
                    </IconButton>
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => handleDelete(phone.number)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Add/Edit Phone Number Form */}
        <Box sx={{ mt: 3 }}>
          <Typography
            variant="h6"
            sx={{
              mb: 2,
              fontSize: "1.1rem",
              fontWeight: 600,
              color: "black",
            }}
          >
            {editingPhone ? "Edit Phone Number" : "Add New Phone Number"}
          </Typography>

          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              isValid,
              dirty,
              isSubmitting,
            }) => (
              <Form className="commonModal">
                <Box className="commonInput" sx={{ mb: 3 }}>
                  <TextField
                    fullWidth
                    id="phoneNumber"
                    name="phoneNumber"
                    label="Phone Number"
                    value={values.phoneNumber}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.phoneNumber && Boolean(errors.phoneNumber)}
                    helperText={touched.phoneNumber && errors.phoneNumber}
                    placeholder="+****************"
                    sx={{
                      "& .MuiInputBase-input": {
                        color: "black",
                        padding: { xs: "12px 14px", sm: "16px 14px" },
                        fontSize: { xs: "0.9rem", sm: "1rem" },
                      },
                      "& .MuiFormHelperText-root": {
                        marginLeft: 0,
                      },
                    }}
                    InputProps={{
                      style: { backgroundColor: "white" },
                    }}
                  />
                </Box>

                <Box className="commonInput" sx={{ mb: 3 }}>
                  <FormControl fullWidth>
                    <InputLabel id="phoneType-label">Phone Type</InputLabel>
                    <Select
                      labelId="phoneType-label"
                      id="phoneType"
                      name="phoneType"
                      value={values.phoneType}
                      label="Phone Type"
                      onChange={handleChange}
                      error={touched.phoneType && Boolean(errors.phoneType)}
                      sx={{
                        "& .MuiSelect-select": {
                          color: "black",
                        },
                      }}
                    >
                      {phoneTypes.map((type) => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
                  {editingPhone && (
                    <Button
                      onClick={handleCancelEdit}
                      variant="outlined"
                      className="secondaryOutlineBtn"
                      sx={{
                        textTransform: "none",
                        fontSize: { xs: "0.8rem", sm: "0.875rem" },
                      }}
                    >
                      Cancel Edit
                    </Button>
                  )}
                  <Button
                    type="submit"
                    variant="contained"
                    className="primaryFillBtn"
                    disabled={!isValid || (!dirty && !editingPhone) || isSubmitting}
                    startIcon={editingPhone ? undefined : <AddIcon />}
                    sx={{
                      textTransform: "none",
                      fontSize: { xs: "0.8rem", sm: "0.875rem" },
                    }}
                  >
                    {editingPhone ? "Update" : "Add"} Phone Number
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          padding: { xs: "16px", sm: "16px 24px 24px" },
          justifyContent: "flex-end",
          borderTop: "1px solid rgba(0, 0, 0, 0.12)",
        }}
      >
        <Button
          onClick={onClose}
          variant="outlined"
          className="secondaryOutlineBtn"
          sx={{
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PhoneNumberManagement;
