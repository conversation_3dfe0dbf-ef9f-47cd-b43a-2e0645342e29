#!/usr/bin/env node

/**
 * Uninstall GMB Auto Reply Service from Windows Services
 */

require('dotenv').config();

const WindowsServiceManager = require('./services/windowsService');
const logger = require('./utils/logger');

async function uninstallService() {
  try {
    console.log('Uninstalling GMB Auto Reply Service...');
    console.log('======================================');

    // Create service manager instance
    const serviceManager = new WindowsServiceManager();
    
    // Get service information
    const serviceInfo = serviceManager.getServiceInfo();
    
    console.log(`Service Name: ${serviceInfo.name}`);
    console.log(`Description: ${serviceInfo.description}`);
    console.log('');

    // Check if running as administrator
    if (process.platform === 'win32') {
      const { execSync } = require('child_process');
      try {
        execSync('net session', { stdio: 'ignore' });
      } catch (error) {
        console.error('ERROR: This script must be run as Administrator on Windows.');
        console.error('Please run Command Prompt or PowerShell as Administrator and try again.');
        process.exit(1);
      }
    }

    // Stop the service first if it's running
    console.log('Stopping service if running...');
    try {
      const { execSync } = require('child_process');
      execSync(`sc stop "${serviceInfo.name}"`, { stdio: 'ignore' });
      console.log('Service stopped.');
      
      // Wait a moment for the service to fully stop
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.log('Service was not running or could not be stopped.');
    }

    // Uninstall the service
    console.log('Uninstalling service...');
    serviceManager.uninstall();

    // Wait for uninstallation to complete
    console.log('Uninstallation initiated. Please wait...');
    
    // The service uninstallation is asynchronous, so we'll wait a bit
    setTimeout(() => {
      console.log('');
      console.log('Uninstallation completed!');
      console.log('');
      console.log('The GMB Auto Reply Service has been removed from Windows Services.');
      console.log('');
      console.log('Note: Log files in the ./logs directory have been preserved.');
      console.log('You can manually delete them if no longer needed.');
      
      process.exit(0);
    }, 5000);

  } catch (error) {
    console.error('Failed to uninstall service:', error.message);
    logger.error('Service uninstallation failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('GMB Auto Reply Service Uninstaller');
  console.log('==================================');
  console.log('');
  console.log('Usage: node uninstall-service.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --force, -f    Force uninstallation (ignore errors)');
  console.log('');
  console.log('Requirements:');
  console.log('  - Windows operating system');
  console.log('  - Administrator privileges');
  console.log('');
  process.exit(0);
}

if (args.includes('--confirm') || args.includes('-c')) {
  uninstallService();
} else {
  console.log('GMB Auto Reply Service Uninstaller');
  console.log('==================================');
  console.log('');
  console.log('This will remove the GMB Auto Reply Service from Windows Services.');
  console.log('');
  console.log('Are you sure you want to continue? (y/N)');
  
  process.stdin.setRawMode(true);
  process.stdin.resume();
  process.stdin.on('data', (key) => {
    const input = key.toString().toLowerCase();
    
    if (input === 'y' || input === 'yes\n') {
      process.stdin.setRawMode(false);
      process.stdin.pause();
      uninstallService();
    } else {
      console.log('\nUninstallation cancelled.');
      process.exit(0);
    }
  });
}
