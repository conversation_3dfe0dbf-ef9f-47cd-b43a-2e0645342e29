const express = require("express");
const router = express.Router();
const logger = require("../utils/logger");

// Note: Instagram authentication is now handled through the Instagram Business API
// via Facebook Graph API. See /v1/instagram routes for the current implementation.
// This file is kept for potential future auth routes.

router.get("/", (req, res) => {
  res.json({
    message: "Auth routes - Instagram authentication moved to /v1/instagram",
    availableRoutes: {
      instagram: "/v1/instagram/authenticate",
      facebook: "/v1/facebook/authenticate",
      google: "/v1/gmb/authenticate",
    },
  });
});

module.exports = router;
