import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  useTheme,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line, Bar, Pie } from "react-chartjs-2";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import {
  ExcelExportService,
  ChartExportData,
} from "../../services/excelExport.service";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface ChartData {
  data: number[];
  labels: string[];
}

interface GoogleAnalyticsChartsProps {
  impressionsData: ChartData;
  clicksData: ChartData;
  callsData: ChartData;
  directionsData: ChartData;
  websiteClicksData: ChartData;
  messagingData: ChartData;
  bookingsData: ChartData;
  platformBreakdownData: { label: string; value: number }[];
  searchQueriesData: { query: string; impressions: number; clicks: number }[];
  // Export functionality props
  showExport?: boolean;
  selectedLocationIds?: string[];
  availableLocations?: any[];
  locationDataMap?: Record<string, any>;
  dateRange?: {
    from: string;
    to: string;
  };
}

const GoogleAnalyticsCharts: React.FC<GoogleAnalyticsChartsProps> = ({
  impressionsData,
  clicksData,
  callsData,
  directionsData,
  websiteClicksData,
  messagingData,
  bookingsData,
  platformBreakdownData,
  searchQueriesData,
  showExport = false,
  selectedLocationIds = [],
  availableLocations = [],
  locationDataMap = {},
  dateRange,
}) => {
  const theme = useTheme();
  const reportColors = theme.palette.reportColors;
  // Chart.js options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Filter out dates where ALL metrics are zero when there are more than 7 data points
  const filterCombinedZeroValueDates = () => {
    if (impressionsData.labels.length <= 7) {
      return {
        impressions: impressionsData,
        clicks: clicksData,
        calls: callsData,
        directions: directionsData,
        websiteClicks: websiteClicksData,
        messaging: messagingData,
        bookings: bookingsData,
      };
    }

    const filteredLabels: string[] = [];
    const filteredImpressions: number[] = [];
    const filteredClicks: number[] = [];
    const filteredCalls: number[] = [];
    const filteredDirections: number[] = [];
    const filteredWebsiteClicks: number[] = [];
    const filteredMessaging: number[] = [];
    const filteredBookings: number[] = [];

    impressionsData.labels.forEach((label, index) => {
      const impressions = impressionsData.data[index] || 0;
      const clicks = clicksData.data[index] || 0;
      const calls = callsData.data[index] || 0;
      const directions = directionsData.data[index] || 0;
      const websiteClicks = websiteClicksData.data[index] || 0;
      const messaging = messagingData.data[index] || 0;
      const bookings = bookingsData.data[index] || 0;

      // Keep the data point if ANY metric has a non-zero value
      const hasNonZeroValue =
        impressions > 0 ||
        clicks > 0 ||
        calls > 0 ||
        directions > 0 ||
        websiteClicks > 0 ||
        messaging > 0 ||
        bookings > 0;

      if (hasNonZeroValue) {
        filteredLabels.push(label);
        filteredImpressions.push(impressions);
        filteredClicks.push(clicks);
        filteredCalls.push(calls);
        filteredDirections.push(directions);
        filteredWebsiteClicks.push(websiteClicks);
        filteredMessaging.push(messaging);
        filteredBookings.push(bookings);
      }
    });

    // If filtering removes too many points (less than 3), return original data
    if (filteredLabels.length < 3) {
      return {
        impressions: impressionsData,
        clicks: clicksData,
        calls: callsData,
        directions: directionsData,
        websiteClicks: websiteClicksData,
        messaging: messagingData,
        bookings: bookingsData,
      };
    }

    return {
      impressions: { labels: filteredLabels, data: filteredImpressions },
      clicks: { labels: filteredLabels, data: filteredClicks },
      calls: { labels: filteredLabels, data: filteredCalls },
      directions: { labels: filteredLabels, data: filteredDirections },
      websiteClicks: { labels: filteredLabels, data: filteredWebsiteClicks },
      messaging: { labels: filteredLabels, data: filteredMessaging },
      bookings: { labels: filteredLabels, data: filteredBookings },
    };
  };

  // Apply filtering
  const filteredData = filterCombinedZeroValueDates();
  const filteredImpressionsData = filteredData.impressions;
  const filteredClicksData = filteredData.clicks;
  const filteredCallsData = filteredData.calls;
  const filteredDirectionsData = filteredData.directions;
  const filteredWebsiteClicksData = filteredData.websiteClicks;
  const filteredMessagingData = filteredData.messaging;
  const filteredBookingsData = filteredData.bookings;

  // Transform data for Chart.js format
  const createLineChartData = (
    chartData: ChartData,
    label: string,
    color: string,
    backgroundColor?: string
  ) => ({
    labels: chartData.labels,
    datasets: [
      {
        label,
        data: chartData.data,
        borderColor: color,
        backgroundColor: backgroundColor || color + "20",
        borderWidth: 2,
        fill: !!backgroundColor,
      },
    ],
  });

  // Combined metrics chart data
  const combinedChartData = {
    labels: filteredImpressionsData.labels,
    datasets: [
      {
        label: "Impressions",
        data: filteredImpressionsData.data,
        borderColor: reportColors?.color1 || "#2E86AB",
        backgroundColor: reportColors?.color1 + "20" || "#2E86AB20",
        borderWidth: 2,
      },
      {
        label: "Clicks",
        data: filteredClicksData.data,
        borderColor: reportColors?.color2 || "#A23B72",
        backgroundColor: reportColors?.color2 + "20" || "#A23B7220",
        borderWidth: 2,
      },
      {
        label: "Calls",
        data: filteredCallsData.data,
        borderColor: reportColors?.color3 || "#F18F01",
        backgroundColor: reportColors?.color3 + "20" || "#F18F0120",
        borderWidth: 2,
      },
      {
        label: "Directions",
        data: filteredDirectionsData.data,
        borderColor: reportColors?.color4 || "#C73E1D",
        backgroundColor: reportColors?.color4 + "20" || "#C73E1D20",
        borderWidth: 2,
      },
      {
        label: "Website Clicks",
        data: filteredWebsiteClicksData.data,
        borderColor: reportColors?.color5 || "#6A994E",
        backgroundColor: reportColors?.color5 + "20" || "#6A994E20",
        borderWidth: 2,
      },
    ],
  };

  // Customer actions chart data
  const customerActionsData = {
    labels: filteredImpressionsData.labels,
    datasets: [
      {
        label: "Calls",
        data: filteredCallsData.data,
        backgroundColor: "#FF8042",
        borderColor: "#FF8042",
        borderWidth: 1,
      },
      {
        label: "Directions",
        data: filteredDirectionsData.data,
        backgroundColor: "#00C49F",
        borderColor: "#00C49F",
        borderWidth: 1,
      },
      {
        label: "Website Clicks",
        data: filteredWebsiteClicksData.data,
        backgroundColor: "#FFBB28",
        borderColor: "#FFBB28",
        borderWidth: 1,
      },
      {
        label: "Messaging",
        data: filteredMessagingData.data,
        backgroundColor: "#FF6B6B",
        borderColor: "#FF6B6B",
        borderWidth: 1,
      },
      {
        label: "Bookings",
        data: filteredBookingsData.data,
        backgroundColor: "#8884d8",
        borderColor: "#8884d8",
        borderWidth: 1,
      },
    ],
  };

  // Platform breakdown pie chart data
  const platformPieData = {
    labels: platformBreakdownData.map((item) => item.label),
    datasets: [
      {
        data: platformBreakdownData.map((item) => item.value),
        backgroundColor: [
          reportColors?.color1 || "#2E86AB", // Desktop Maps
          reportColors?.color2 || "#A23B72", // Desktop Search
          reportColors?.color3 || "#F18F01", // Mobile Maps
          reportColors?.color4 || "#C73E1D", // Mobile Search
        ],
        borderColor: [
          reportColors?.color1 || "#2E86AB",
          reportColors?.color2 || "#A23B72",
          reportColors?.color3 || "#F18F01",
          reportColors?.color4 || "#C73E1D",
        ],
        borderWidth: 1,
      },
    ],
  };

  // Messaging and bookings chart data
  const messagingBookingsData = {
    labels: filteredImpressionsData.labels,
    datasets: [
      {
        label: "Messaging",
        data: filteredMessagingData.data,
        backgroundColor: reportColors?.color6 || "#7209B7",
        borderColor: reportColors?.color6 || "#7209B7",
        borderWidth: 1,
      },
      {
        label: "Bookings",
        data: filteredBookingsData.data,
        backgroundColor: reportColors?.color7 || "#F77F00",
        borderColor: reportColors?.color7 || "#F77F00",
        borderWidth: 1,
      },
    ],
  };

  // Search queries chart data
  const searchQueriesChartData = {
    labels: searchQueriesData.slice(0, 10).map((item) => item.query),
    datasets: [
      {
        label: "Impressions",
        data: searchQueriesData.slice(0, 10).map((item) => item.impressions),
        backgroundColor: reportColors?.color1 || "#2E86AB",
        borderColor: reportColors?.color1 || "#2E86AB",
        borderWidth: 1,
      },
      {
        label: "Clicks",
        data: searchQueriesData.slice(0, 10).map((item) => item.clicks),
        backgroundColor: reportColors?.color2 || "#A23B72",
        borderColor: reportColors?.color2 || "#A23B72",
        borderWidth: 1,
      },
    ],
  };

  // Export functions - Use original data for exports, not filtered data
  const handleExportAllCharts = () => {
    const allChartsData: ChartExportData[] = [
      {
        chartTitle: "Business Impressions Over Time",
        data: impressionsData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "BUSINESS_IMPRESSIONS",
        daysDifference: impressionsData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Total Clicks Over Time",
        data: clicksData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "TOTAL_CLICKS",
        daysDifference: clicksData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Phone Calls Trend",
        data: callsData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "CALL_CLICKS",
        daysDifference: callsData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Direction Requests Trend",
        data: directionsData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "BUSINESS_DIRECTION_REQUESTS",
        daysDifference: directionsData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Website Clicks Trend",
        data: websiteClicksData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "WEBSITE_CLICKS",
        daysDifference: websiteClicksData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Messaging Trend",
        data: messagingData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "BUSINESS_CONVERSATIONS",
        daysDifference: messagingData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Bookings Trend",
        data: bookingsData,
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "BUSINESS_FOOD_ORDERS",
        daysDifference: bookingsData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
      {
        chartTitle: "Combined Metrics Overview",
        data: {
          labels: impressionsData.labels,
          data: impressionsData.data.map(
            (_, index) =>
              (impressionsData.data[index] || 0) +
              (clicksData.data[index] || 0) +
              (callsData.data[index] || 0) +
              (directionsData.data[index] || 0) +
              (websiteClicksData.data[index] || 0)
          ),
        },
        selectedLocationIds,
        availableLocations,
        locationDataMap,
        metricType: "COMBINED_METRICS",
        daysDifference: impressionsData.labels.length,
        isSameMonthYear: false,
        dateRange,
      },
    ];

    ExcelExportService.exportMultipleChartsToExcel(
      allChartsData,
      "Google_Analytics_Complete_Report"
    );
  };

  const createExportButton = (
    chartTitle: string,
    chartData: ChartData,
    metricType: string,
    originalData?: ChartData
  ) => {
    if (!showExport) return null;

    // Use original data for export if provided, otherwise use the chart data
    const dataToExport = originalData || chartData;

    const exportData: ChartExportData = {
      chartTitle,
      data: dataToExport,
      selectedLocationIds,
      availableLocations,
      locationDataMap,
      metricType,
      daysDifference: dataToExport.labels.length,
      isSameMonthYear: false,
      dateRange,
    };

    return (
      <Tooltip title={`Export ${chartTitle} to Excel`}>
        <IconButton
          onClick={() => ExcelExportService.exportChartToExcel(exportData)}
          size="small"
          color="primary"
          sx={{
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              transform: "scale(1.1)",
              backgroundColor: "rgba(25, 118, 210, 0.08)",
            },
          }}
        >
          <FileDownloadIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    );
  };

  return (
    <Box>
      <Grid container spacing={3}>
        {/* Business Impressions Trend */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Business Impressions Over Time
                </Typography>
                {createExportButton(
                  "Business Impressions Over Time",
                  filteredImpressionsData,
                  "BUSINESS_IMPRESSIONS",
                  impressionsData
                )}
              </Box>
              <Box height={300}>
                <Line
                  data={createLineChartData(
                    filteredImpressionsData,
                    "Impressions",
                    reportColors?.color1 || "#2E86AB",
                    reportColors?.color1 + "20" || "#2E86AB20"
                  )}
                  options={chartOptions}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Clicks Trend */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Total Clicks Over Time
                </Typography>
                {createExportButton(
                  "Total Clicks Over Time",
                  filteredClicksData,
                  "TOTAL_CLICKS",
                  clicksData
                )}
              </Box>
              <Box height={300}>
                <Line
                  data={createLineChartData(
                    filteredClicksData,
                    "Clicks",
                    reportColors?.color2 || "#A23B72"
                  )}
                  options={chartOptions}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Combined Metrics Overview */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Combined Metrics Overview
                </Typography>
                <Box display="flex" gap={1}>
                  {createExportButton(
                    "Combined Metrics Overview",
                    {
                      labels: filteredImpressionsData.labels,
                      data: filteredImpressionsData.data.map(
                        (_, index) =>
                          (filteredImpressionsData.data[index] || 0) +
                          (filteredClicksData.data[index] || 0) +
                          (filteredCallsData.data[index] || 0) +
                          (filteredDirectionsData.data[index] || 0) +
                          (filteredWebsiteClicksData.data[index] || 0)
                      ),
                    },
                    "COMBINED_METRICS",
                    {
                      labels: impressionsData.labels,
                      data: impressionsData.data.map(
                        (_, index) =>
                          (impressionsData.data[index] || 0) +
                          (clicksData.data[index] || 0) +
                          (callsData.data[index] || 0) +
                          (directionsData.data[index] || 0) +
                          (websiteClicksData.data[index] || 0)
                      ),
                    }
                  )}
                  {showExport && (
                    <Tooltip title="Export All Charts to Excel">
                      <IconButton
                        onClick={handleExportAllCharts}
                        size="small"
                        color="secondary"
                        sx={{
                          transition: "all 0.2s ease-in-out",
                          "&:hover": {
                            transform: "scale(1.1)",
                            backgroundColor: "rgba(156, 39, 176, 0.08)",
                          },
                        }}
                      >
                        <FileDownloadIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </Box>
              <Box height={400}>
                <Line
                  data={combinedChartData}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      legend: {
                        position: "top" as const,
                      },
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Customer Actions Breakdown */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Customer Actions Over Time
              </Typography>
              <Box height={350}>
                <Bar data={customerActionsData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Platform Breakdown */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Platform Breakdown
              </Typography>
              <Box height={350}>
                <Pie
                  data={platformPieData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: "bottom" as const,
                      },
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Calls Trend */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Phone Calls Trend
                </Typography>
                {createExportButton(
                  "Phone Calls Trend",
                  filteredCallsData,
                  "CALL_CLICKS",
                  callsData
                )}
              </Box>
              <Box height={300}>
                <Line
                  data={createLineChartData(
                    filteredCallsData,
                    "Phone Calls",
                    reportColors?.color3 || "#F18F01",
                    reportColors?.color3 + "20" || "#F18F0120"
                  )}
                  options={chartOptions}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Directions Trend */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Direction Requests Trend
                </Typography>
                {createExportButton(
                  "Direction Requests Trend",
                  filteredDirectionsData,
                  "BUSINESS_DIRECTION_REQUESTS",
                  directionsData
                )}
              </Box>
              <Box height={300}>
                <Line
                  data={createLineChartData(
                    filteredDirectionsData,
                    "Direction Requests",
                    reportColors?.color4 || "#C73E1D",
                    reportColors?.color4 + "20" || "#C73E1D20"
                  )}
                  options={chartOptions}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Website Clicks Trend */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Website Clicks Trend
                </Typography>
                {createExportButton(
                  "Website Clicks Trend",
                  filteredWebsiteClicksData,
                  "WEBSITE_CLICKS",
                  websiteClicksData
                )}
              </Box>
              <Box height={300}>
                <Line
                  data={createLineChartData(
                    filteredWebsiteClicksData,
                    "Website Clicks",
                    reportColors?.color5 || "#6A994E"
                  )}
                  options={chartOptions}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Messaging & Bookings */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                mb={1}
              >
                <Typography variant="h6" gutterBottom>
                  Messaging & Bookings Trend
                </Typography>
                {createExportButton(
                  "Messaging & Bookings Trend",
                  {
                    labels: filteredMessagingData.labels,
                    data: filteredMessagingData.data.map(
                      (_, index) =>
                        (filteredMessagingData.data[index] || 0) +
                        (filteredBookingsData.data[index] || 0)
                    ),
                  },
                  "MESSAGING_BOOKINGS",
                  {
                    labels: messagingData.labels,
                    data: messagingData.data.map(
                      (_, index) =>
                        (messagingData.data[index] || 0) +
                        (bookingsData.data[index] || 0)
                    ),
                  }
                )}
              </Box>
              <Box height={300}>
                <Bar data={messagingBookingsData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Search Queries Performance */}
        {searchQueriesData.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Top Search Queries Performance
                </Typography>
                <Box height={400}>
                  <Bar
                    data={searchQueriesChartData}
                    options={{
                      ...chartOptions,
                      scales: {
                        ...chartOptions.scales,
                        x: {
                          ticks: {
                            maxRotation: 45,
                            minRotation: 45,
                          },
                        },
                      },
                    }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default GoogleAnalyticsCharts;
