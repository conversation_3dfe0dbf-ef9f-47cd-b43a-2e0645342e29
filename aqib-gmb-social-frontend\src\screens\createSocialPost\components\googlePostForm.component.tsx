import React from "react";
import { Box, Typography, Button, TextField } from "@mui/material";
import { ImageOutlined as ImageOutlinedIcon } from "@mui/icons-material";
import { FormikErrors, FormikTouched } from "formik";
import { IGoogleCreatePost } from "../../../interfaces/request/IGoogleCreatePost";
import {
  TOPIC_TYPES,
  EVENT_TYPES,
} from "../../../constants/application.constant";

interface GooglePostFormProps {
  values: IGoogleCreatePost;
  errors: FormikErrors<IGoogleCreatePost>;
  touched: FormikTouched<IGoogleCreatePost>;
  setFieldValue: (field: string, value: any) => void;
  uploadedImages: any[];
  handleClick: () => void;
  handleGallerySelection: () => void;
  MAX_ALLOWED_CHARS: number;
  onSubmit: (values: IGoogleCreatePost) => void;
  isEditMode?: boolean;
  isBulkEditMode?: boolean;
}

const GooglePostForm: React.FC<GooglePostFormProps> = ({
  values,
  errors,
  touched,
  setFieldValue,
  uploadedImages,
  handleClick,
  handleGallerySelection,
  MAX_ALLOWED_CHARS,
  onSubmit,
  isEditMode = false,
  isBulkEditMode = false,
}) => {
  return (
    <Box sx={{ mt: 2 }}>
      {/* Topic Type Selection */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Select Topic Type
        </Typography>
        <Box
          sx={{
            display: "flex",
            gap: 2,
            flexWrap: "wrap",
          }}
        >
          {[
            { key: TOPIC_TYPES.Offer, value: "Offer" },
            { key: TOPIC_TYPES.WhatsNew, value: "What's New" },
            { key: TOPIC_TYPES.Event, value: "Event" },
          ].map((topic) => (
            <Button
              key={topic.key}
              variant={
                values.topicType === topic.key ? "contained" : "outlined"
              }
              onClick={() => setFieldValue("topicType", topic.key)}
              sx={{
                textTransform: "none",
                borderRadius: 2,
                minWidth: 120,
              }}
            >
              {topic.value}
            </Button>
          ))}
        </Box>
        {touched.topicType && errors.topicType && (
          <Typography color="error" variant="caption">
            {errors.topicType}
          </Typography>
        )}
      </Box>
      {/* Event/Offer Title */}
      {(values.topicType === TOPIC_TYPES.Event ||
        values.topicType === TOPIC_TYPES.Offer) && (
        <Box className="commonFormPart" sx={{ mb: 2 }}>
          <TextField
            fullWidth
            label={
              values.topicType === TOPIC_TYPES.Event
                ? "Event Title"
                : "Offer Title"
            }
            variant="outlined"
            value={values.event?.title || ""}
            onChange={(e) => setFieldValue("event.title", e.target.value)}
            error={Boolean(errors.event?.title && touched.event?.title)}
            helperText={
              errors.event?.title && touched.event?.title
                ? errors.event.title
                : ""
            }
          />
        </Box>
      )}
      {/* Summary */}
      <Box className="commonFormPart" sx={{ mb: 2 }}>
        <TextField
          fullWidth
          multiline
          rows={6}
          label="Summary"
          variant="outlined"
          placeholder="Write details about what's new with your business"
          value={values.summary}
          onChange={(e) => setFieldValue("summary", e.target.value)}
          error={Boolean(touched.summary && errors.summary)}
          helperText={
            touched.summary && errors.summary
              ? errors.summary
              : `${values.summary.length}/${MAX_ALLOWED_CHARS} characters`
          }
        />
      </Box>
      {/* Media Upload */}
      <Box className="commonFormPart" sx={{ mb: 2 }}>
        <Box
          onClick={handleGallerySelection}
          sx={{
            border: "2px dashed #e0e0e0",
            borderRadius: 2,
            p: 4,
            textAlign: "center",
            cursor: "pointer",
            transition: "all 0.3s ease",
            "&:hover": {
              borderColor: "#1976d2",
            },
          }}
        >
          <ImageOutlinedIcon
            sx={{
              fontSize: 48,
              color: "#9e9e9e",
              mb: 1,
            }}
          />
          <Typography variant="h6" sx={{ mb: 0.5, color: "#666" }}>
            Add/Edit Post Image
          </Typography>
          <Typography variant="caption" sx={{ color: "#999" }}>
            Maximum Size Photo - 35 MB, Format: JPG, PNG & Recommended Size -
            1200 x 900
          </Typography>
        </Box>

        {touched.media && errors.media && (
          <Typography color="error" variant="caption">
            {typeof errors.media === "string"
              ? errors.media
              : "Media is required"}
          </Typography>
        )}
      </Box>
      {/* Event/Offer Schedule */}
      {(values.topicType === TOPIC_TYPES.Event ||
        values.topicType === TOPIC_TYPES.Offer) && (
        <Box className="commonFormPart" sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Schedule
          </Typography>
          <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
            <TextField
              fullWidth
              label="Start Date & Time"
              type="datetime-local"
              value={values.event?.schedule?.startTime || ""}
              onChange={(e) =>
                setFieldValue("event.schedule.startTime", e.target.value)
              }
              error={Boolean(
                errors.event?.schedule?.startTime &&
                  touched.event?.schedule?.startTime
              )}
              helperText={
                errors.event?.schedule?.startTime &&
                touched.event?.schedule?.startTime
                  ? errors.event.schedule.startTime
                  : ""
              }
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              fullWidth
              label="End Date & Time"
              type="datetime-local"
              value={values.event?.schedule?.endTime || ""}
              onChange={(e) =>
                setFieldValue("event.schedule.endTime", e.target.value)
              }
              error={Boolean(
                errors.event?.schedule?.endTime &&
                  touched.event?.schedule?.endTime
              )}
              helperText={
                errors.event?.schedule?.endTime &&
                touched.event?.schedule?.endTime
                  ? errors.event.schedule.endTime
                  : ""
              }
              InputLabelProps={{ shrink: true }}
            />
          </Box>
        </Box>
      )}
      {/* Offer Details */}
      {values.topicType === TOPIC_TYPES.Offer && (
        <Box className="commonFormPart" sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Offer Details
          </Typography>
          <TextField
            fullWidth
            label="Coupon Code"
            variant="outlined"
            value={values.offer?.couponCode || ""}
            onChange={(e) => setFieldValue("offer.couponCode", e.target.value)}
            error={Boolean(
              errors.offer?.couponCode && touched.offer?.couponCode
            )}
            helperText={
              errors.offer?.couponCode && touched.offer?.couponCode
                ? errors.offer.couponCode
                : ""
            }
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            label="Redeem Online URL"
            variant="outlined"
            value={values.offer?.redeemOnlineUrl || ""}
            onChange={(e) =>
              setFieldValue("offer.redeemOnlineUrl", e.target.value)
            }
            error={Boolean(
              errors.offer?.redeemOnlineUrl && touched.offer?.redeemOnlineUrl
            )}
            helperText={
              errors.offer?.redeemOnlineUrl && touched.offer?.redeemOnlineUrl
                ? errors.offer.redeemOnlineUrl
                : ""
            }
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            multiline
            rows={3}
            label="Terms & Conditions"
            variant="outlined"
            value={values.offer?.termsConditions || ""}
            onChange={(e) =>
              setFieldValue("offer.termsConditions", e.target.value)
            }
            error={Boolean(
              errors.offer?.termsConditions && touched.offer?.termsConditions
            )}
            helperText={
              errors.offer?.termsConditions && touched.offer?.termsConditions
                ? errors.offer.termsConditions
                : ""
            }
          />
        </Box>
      )}
      {/* Call to Action */}
      {(values.topicType === TOPIC_TYPES.Event ||
        values.topicType === TOPIC_TYPES.WhatsNew) && (
        <Box className="commonFormPart" sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Call to Action
          </Typography>
          <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
            {[
              EVENT_TYPES.Book,
              EVENT_TYPES.OrderOnline,
              EVENT_TYPES.Buy,
              EVENT_TYPES.LearnMore,
              EVENT_TYPES.SignUp,
              EVENT_TYPES.CallNow,
            ].map((actionType) => (
              <Button
                key={actionType}
                variant={
                  values.callToAction?.actionType === actionType
                    ? "contained"
                    : "outlined"
                }
                onClick={() =>
                  setFieldValue("callToAction.actionType", actionType)
                }
                sx={{
                  textTransform: "none",
                  borderRadius: 1,
                  fontSize: "0.8rem",
                }}
              >
                {actionType.replace("_", " ")}
              </Button>
            ))}
          </Box>
          {values.callToAction?.actionType &&
            values.callToAction.actionType !== EVENT_TYPES.CallNow && (
              <TextField
                fullWidth
                label="Action URL"
                variant="outlined"
                value={values.callToAction?.url || ""}
                onChange={(e) =>
                  setFieldValue("callToAction.url", e.target.value)
                }
                error={Boolean(
                  errors.callToAction?.url && touched.callToAction?.url
                )}
                helperText={
                  errors.callToAction?.url && touched.callToAction?.url
                    ? errors.callToAction.url
                    : ""
                }
              />
            )}
        </Box>
      )}
      {/* Submit Button */}
      <Box sx={{ mt: 3, mb: 2 }}>
        {isEditMode ? (
          <Button
            className="updatesShapeBtn"
            onClick={() => onSubmit(values)}
            variant="contained"
            style={{ textTransform: "capitalize" }}
            fullWidth
          >
            {isBulkEditMode ? "Update All Google Posts" : "Update Google Post"}
          </Button>
        ) : (
          <Button
            className="updatesShapeBtn"
            onClick={() => onSubmit(values)}
            variant="contained"
            style={{ textTransform: "capitalize" }}
            fullWidth
          >
            Create Google Post
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default GooglePostForm;
