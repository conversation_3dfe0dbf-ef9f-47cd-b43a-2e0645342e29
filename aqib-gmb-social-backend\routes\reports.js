const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  getReviewsReports,
  exportReviewsReport,
  getPerformanceReports,
  exportPerformanceReport,
} = require("../controllers/reports.controller");

// Test endpoint (no auth required)
router.get("/test", (req, res) => {
  res.json({
    success: true,
    message: "Reports API is working",
    timestamp: new Date().toISOString(),
  });
});

// Get reviews reports with aggregated data
router.post("/reviews", isAuthenticated, getReviewsReports);

// Export reviews report
router.post("/reviews/export", isAuthenticated, exportReviewsReport);

// Get performance reports with trend analysis
router.post("/performance", isAuthenticated, getPerformanceReports);

// Export performance report
router.post("/performance/export", isAuthenticated, exportPerformanceReport);

module.exports = router;
