import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  AppBar,
  Toolbar,
  InputAdornment,
  Chip,
  CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AddIcon from "@mui/icons-material/Add";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckIcon from "@mui/icons-material/Check";
import { useDispatch } from "react-redux";
import BusinessProfileService from "../../../services/businessProfile/businessProfile.service";

interface AddServicesModalProps {
  open: boolean;
  onClose: () => void;
  categoryName: string;
  isPrimary: boolean;
  onAddService: (serviceName: string) => void;
}

const AddServicesModal: React.FC<AddServicesModalProps> = ({
  open,
  onClose,
  categoryName,
  isPrimary,
  onAddService,
  googleSuggestedServices = [],
}) => {
  const dispatch = useDispatch();
  const _businessProfileService = new BusinessProfileService(dispatch);

  const [showCustomServiceInput, setShowCustomServiceInput] = useState(false);
  const [customServiceName, setCustomServiceName] = useState("");
  const [error, setError] = useState("");
  const [aiGeneratedServices, setAiGeneratedServices] = useState<string[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [isLoadingServices, setIsLoadingServices] = useState(false);

  // Load AI-generated services when modal opens
  useEffect(() => {
    if (open && categoryName) {
      loadAIServices();
    }
  }, [open, categoryName]);

  const loadAIServices = async () => {
    try {
      setIsLoadingServices(true);
      const response = await _businessProfileService.generateServices({
        categoryName,
      });

      if (response.success && response.data?.services) {
        setAiGeneratedServices(response.data.services);
      }
    } catch (error) {
      console.error("Error loading AI services:", error);
    } finally {
      setIsLoadingServices(false);
    }
  };

  const handleServiceToggle = (serviceName: string) => {
    setSelectedServices((prev) =>
      prev.includes(serviceName)
        ? prev.filter((s) => s !== serviceName)
        : [...prev, serviceName]
    );
  };

  const handleAddCustomService = () => {
    setShowCustomServiceInput(true);
  };

  const handleCancelCustomService = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
  };

  const handleSubmit = () => {
    if (showCustomServiceInput) {
      if (!customServiceName.trim()) {
        setError("Please enter a service name");
        return;
      }
      onAddService(customServiceName);
      setCustomServiceName("");
      setError("");
      setShowCustomServiceInput(false);
    } else {
      // Add all selected services
      selectedServices.forEach((serviceName) => {
        onAddService(serviceName);
      });
    }

    // Reset state and close modal
    setSelectedServices([]);
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
    onClose();
  };

  const handleCancel = () => {
    setShowCustomServiceInput(false);
    setCustomServiceName("");
    setError("");
    setSelectedServices([]);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          bgcolor: "#ffffff",
          color: "#2F2F2F",
          borderRadius: "8px",
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
        },
      }}
      sx={{
        "& .MuiDialog-paper": {
          margin: { xs: "16px", sm: "32px" },
          width: { xs: "calc(100% - 32px)", sm: "auto" },
          maxHeight: { xs: "calc(100% - 32px)", sm: "auto" },
        },
      }}
    >
      <AppBar
        position="relative"
        elevation={0}
        sx={{
          bgcolor: "#EDEDED",
          color: "#2F2F2F",
          borderBottom: "1px solid #EBEBEB",
        }}
      >
        <Toolbar
          sx={{
            minHeight: { xs: "48px", sm: "56px" },
            px: { xs: 1, sm: 2 },
          }}
        >
          <IconButton
            edge="start"
            onClick={handleCancel}
            aria-label="back"
            sx={{
              mr: { xs: 0.5, sm: 1 },
              color: "#2F2F2F",
              "&:hover": {
                bgcolor: "rgba(47, 47, 47, 0.1)",
              },
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            component="div"
            sx={{
              flexGrow: 1,
              ml: 1,
              fontSize: { xs: "1rem", sm: "1.25rem" },
              color: "#2F2F2F",
              fontWeight: 600,
            }}
          >
            Add services
          </Typography>
          <IconButton
            edge="end"
            onClick={handleCancel}
            aria-label="close"
            sx={{
              color: "#2F2F2F",
              "&:hover": {
                bgcolor: "rgba(47, 47, 47, 0.1)",
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 1, sm: 1.5 } }}>
        <Box sx={{ mb: 1.5 }}>
          <Typography
            variant="h6"
            sx={{
              fontSize: { xs: "1rem", sm: "1.25rem" },
              wordBreak: "break-word",
              color: "#2F2F2F",
              fontWeight: 600,
              mb: 0.5,
            }}
          >
            {categoryName}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              fontSize: { xs: "0.7rem", sm: "0.75rem" },
              color: isPrimary ? "#309898" : "#757575",
              fontWeight: 500,
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            {isPrimary ? "Primary category" : "Additional category"}
          </Typography>
        </Box>

        <Typography
          variant="body2"
          sx={{
            color: "#2F2F2F",
            mb: 2,
            fontWeight: 500,
          }}
        >
          Add services you offer and get discovered by customers
        </Typography>

        {/* Google Suggested Services Section */}
        {googleSuggestedServices && googleSuggestedServices.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              sx={{
                fontSize: { xs: "0.8rem", sm: "0.875rem" },
                fontWeight: 600,
                color: "#309898",
                mb: 1.5,
                textTransform: "uppercase",
                letterSpacing: "0.5px",
              }}
            >
              Google Suggested
            </Typography>
            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.75 }}>
              {googleSuggestedServices.map((service, index) => (
                <Chip
                  key={`google-${index}`}
                  label={service.displayName}
                  onClick={() => handleServiceToggle(service.displayName)}
                  icon={
                    selectedServices.includes(service.displayName) ? (
                      <CheckIcon />
                    ) : (
                      <AddIcon />
                    )
                  }
                  variant={
                    selectedServices.includes(service.displayName)
                      ? "filled"
                      : "outlined"
                  }
                  size="small"
                  sx={{
                    bgcolor: selectedServices.includes(service.displayName)
                      ? "#309898"
                      : "transparent",
                    color: selectedServices.includes(service.displayName)
                      ? "#ffffff"
                      : "#2F2F2F",
                    borderColor: selectedServices.includes(service.displayName)
                      ? "#309898"
                      : "#309898",
                    "&:hover": {
                      bgcolor: selectedServices.includes(service.displayName)
                        ? "#267373"
                        : "#30989833",
                      borderColor: "#309898",
                    },
                    "& .MuiChip-icon": {
                      color: selectedServices.includes(service.displayName)
                        ? "#ffffff"
                        : "#309898",
                    },
                  }}
                />
              ))}
            </Box>
          </Box>
        )}

        {/* MyLocoBiz Suggestions Section */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="subtitle2"
            sx={{
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              fontWeight: 600,
              color: "#F4631E",
              mb: 1.5,
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            MyLocoBiz Suggestions
          </Typography>

          {isLoadingServices ? (
            <Box sx={{ display: "flex", justifyContent: "center", py: 2 }}>
              <CircularProgress size={32} sx={{ color: "#F4631E" }} />
            </Box>
          ) : (
            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.75 }}>
              {aiGeneratedServices.map((service, index) => (
                <Chip
                  key={`local-${index}`}
                  label={service}
                  onClick={() => handleServiceToggle(service)}
                  icon={
                    selectedServices.includes(service) ? (
                      <CheckIcon />
                    ) : (
                      <AddIcon />
                    )
                  }
                  variant={
                    selectedServices.includes(service) ? "filled" : "outlined"
                  }
                  size="small"
                  sx={{
                    bgcolor: selectedServices.includes(service)
                      ? "#F4631E"
                      : "transparent",
                    color: selectedServices.includes(service)
                      ? "#ffffff"
                      : "#2F2F2F",
                    borderColor: selectedServices.includes(service)
                      ? "#F4631E"
                      : "#F4631E",
                    "&:hover": {
                      bgcolor: selectedServices.includes(service)
                        ? "#E55A1B"
                        : "#F4631E33",
                      borderColor: "#F4631E",
                    },
                    "& .MuiChip-icon": {
                      color: selectedServices.includes(service)
                        ? "#ffffff"
                        : "#F4631E",
                    },
                  }}
                />
              ))}
            </Box>
          )}
        </Box>

        <Box sx={{ mt: 2, mb: 1.5 }}>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              color: "#757575",
            }}
          >
            Don't see a service you offer? Create your own
          </Typography>
        </Box>

        {showCustomServiceInput ? (
          <Box sx={{ mb: 1.5 }}>
            <TextField
              autoFocus
              margin="none"
              fullWidth
              variant="outlined"
              size="small"
              value={customServiceName}
              onChange={(e) => {
                // Limit to 120 characters
                if (e.target.value.length <= 120) {
                  setCustomServiceName(e.target.value);
                  if (e.target.value.trim()) {
                    setError("");
                  }
                }
              }}
              error={!!error}
              helperText={error}
              placeholder="Enter service name"
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleCancelCustomService}
                        edge="end"
                        size="small"
                        sx={{
                          color: "#2F2F2F",
                          "&:hover": {
                            bgcolor: "rgba(47, 47, 47, 0.1)",
                          },
                        }}
                      >
                        <CancelIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  bgcolor: "#ffffff",
                  color: "#2F2F2F",
                  borderRadius: "5px",
                  "& fieldset": {
                    borderColor: "#EBEBEB",
                  },
                  "&:hover fieldset": {
                    borderColor: "#309898",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#309898",
                  },
                },
                "& .MuiInputBase-input::placeholder": {
                  color: "#757575",
                  opacity: 1,
                },
                "& .MuiFormHelperText-root": {
                  color: "#F24245",
                  marginLeft: 0,
                  mt: 0.25,
                },
              }}
            />
            <Typography
              variant="caption"
              sx={{
                display: "block",
                textAlign: "right",
                fontSize: { xs: "0.65rem", sm: "0.75rem" },
                mt: 0.25,
                color: "#757575",
              }}
            >
              {customServiceName.length}/120
            </Typography>
          </Box>
        ) : (
          <Button
            startIcon={<AddIcon />}
            onClick={handleAddCustomService}
            size="small"
            sx={{
              color: "#309898",
              textTransform: "none",
              justifyContent: "flex-start",
              padding: "6px 0",
              fontSize: { xs: "0.8rem", sm: "0.875rem" },
              fontWeight: 500,
              minWidth: "auto",
              "&:hover": {
                bgcolor: "#30989833",
              },
              "& .MuiButton-startIcon": {
                mr: { xs: 0.5, sm: 1 },
              },
            }}
          >
            Add custom service
          </Button>
        )}
      </DialogContent>

      <Box sx={{ flexGrow: 1 }} />

      <DialogActions
        sx={{
          p: { xs: 1, sm: 1.5 },
          justifyContent: "space-between",
        }}
      >
        <Button
          onClick={handleCancel}
          size="small"
          sx={{
            color: "#757575",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            fontWeight: 500,
            border: "1px solid #EBEBEB",
            borderRadius: "5px",
            px: { xs: 1.5, sm: 2.5 },
            py: { xs: 0.5, sm: 0.75 },
            "&:hover": {
              backgroundColor: "#f4f4f4",
              borderColor: "#EBEBEB",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          size="small"
          disabled={
            (showCustomServiceInput && !customServiceName.trim()) ||
            (!showCustomServiceInput && selectedServices.length === 0)
          }
          sx={{
            bgcolor: "#309898",
            color: "#ffffff",
            textTransform: "none",
            fontSize: { xs: "0.8rem", sm: "0.875rem" },
            fontWeight: 500,
            px: { xs: 1.5, sm: 2.5 },
            py: { xs: 0.5, sm: 0.75 },
            borderRadius: "5px",
            "&:hover": {
              bgcolor: "#267373",
            },
            "&.Mui-disabled": {
              bgcolor: "#30989833",
              color: "rgba(255, 255, 255, 0.5)",
            },
          }}
        >
          {showCustomServiceInput
            ? "Save"
            : selectedServices.length > 0
            ? `Save (${selectedServices.length})`
            : "Save"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddServicesModal;
