import HttpHelperService from "../httpHelper.service";

// API endpoints
const GOOGLE_PLACES_BASE_URL = "google-places";
const SERVICE_AREA_SUGGESTIONS = `${GOOGLE_PLACES_BASE_URL}/service-area-suggestions`;
const PLACE_DETAILS = `${GOOGLE_PLACES_BASE_URL}/place-details`;

console.log("Google Places Service - Endpoint constants:", {
  GOOGLE_PLACES_BASE_URL,
  SERVICE_AREA_SUGGESTIONS,
  PLACE_DETAILS,
});

export interface ServiceAreaSuggestion {
  placeId: string;
  placeName: string;
  mainText: string;
  secondaryText: string;
  types: string[];
}

export interface PlaceDetails {
  placeId: string;
  placeName: string;
  formattedAddress: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  types: string[];
  addressComponents: any[];
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  error?: string;
}

class GooglePlacesService {
  private _httpHelperService: HttpHelperService;

  constructor(dispatch?: any) {
    // Create a dummy dispatch function if none provided
    const dummyDispatch = dispatch || (() => {});
    this._httpHelperService = new HttpHelperService(dummyDispatch);
  }

  /**
   * Get service area suggestions from Google Places API
   * Requires minimum 3 characters, returns max 5 suggestions
   */
  getServiceAreaSuggestions = async (
    query: string
  ): Promise<ApiResponse<ServiceAreaSuggestion[]>> => {
    try {
      if (!query || query.length < 3) {
        return {
          success: false,
          message: "Query must be at least 3 characters long",
          data: [],
        };
      }

      const endpoint = `${SERVICE_AREA_SUGGESTIONS}?query=${encodeURIComponent(
        query
      )}`;
      console.log("Google Places Service - Calling endpoint:", endpoint);
      console.log(
        "Google Places Service - Full URL will be:",
        `${import.meta.env.REACT_APP_BASE_URL}/${endpoint}`
      );

      const response = await this._httpHelperService.get(endpoint);

      return response;
    } catch (error: any) {
      console.error("Error fetching service area suggestions:", error);
      return {
        success: false,
        message: error.message || "Failed to fetch suggestions",
        data: [],
        error: error.message,
      };
    }
  };

  /**
   * Get place details by place ID
   */
  getPlaceDetails = async (
    placeId: string
  ): Promise<ApiResponse<PlaceDetails>> => {
    try {
      if (!placeId) {
        return {
          success: false,
          message: "Place ID is required",
          data: {} as PlaceDetails,
        };
      }

      const response = await this._httpHelperService.get(
        `${PLACE_DETAILS}/${placeId}`
      );

      return response;
    } catch (error: any) {
      console.error("Error fetching place details:", error);
      return {
        success: false,
        message: error.message || "Failed to fetch place details",
        data: {} as PlaceDetails,
        error: error.message,
      };
    }
  };

  /**
   * Validate if a place name is valid by searching for it
   */
  validatePlaceName = async (
    placeName: string
  ): Promise<ApiResponse<ServiceAreaSuggestion[]>> => {
    try {
      const suggestions = await this.getServiceAreaSuggestions(placeName);

      if (suggestions.success && suggestions.data.length > 0) {
        // Check if any suggestion matches exactly
        const exactMatch = suggestions.data.find(
          (suggestion) =>
            suggestion.placeName.toLowerCase() === placeName.toLowerCase() ||
            suggestion.mainText.toLowerCase() === placeName.toLowerCase()
        );

        return {
          success: true,
          message: exactMatch ? "Valid place name" : "Similar places found",
          data: exactMatch ? [exactMatch] : suggestions.data,
        };
      }

      return {
        success: false,
        message: "No matching places found",
        data: [],
      };
    } catch (error: any) {
      console.error("Error validating place name:", error);
      return {
        success: false,
        message: error.message || "Failed to validate place name",
        data: [],
        error: error.message,
      };
    }
  };
}

export default GooglePlacesService;
