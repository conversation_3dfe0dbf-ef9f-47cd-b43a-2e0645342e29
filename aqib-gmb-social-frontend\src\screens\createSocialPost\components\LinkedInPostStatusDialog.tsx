import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Box,
  LinearProgress,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  IconButton,
  Typography,
} from "@mui/material";
import {
  CheckCircleOutline as CheckCircleOutlineIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import {
  LinkedInPostStatusDialogProps,
  ILinkedInProfilePost,
} from "../interfaces/postStatusInterfaces";
import BlinkingText from "./shared/BlinkingText";

const LinkedInPostStatusDialog: React.FC<LinkedInPostStatusDialogProps> = ({
  open,
  onClose,
  selectedProfiles,
  postCreationProgress,
  allApiCallsCompleted,
  getProgressColor,
}) => {
  const navigate = useNavigate();

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={() => console.log("On Close")}
    >
      <DialogTitle>LinkedIn Post Upload Status</DialogTitle>
      <Box sx={{ position: "relative", width: "100%" }}>
        <LinearProgress
          variant="determinate"
          value={postCreationProgress.percent}
          color="secondary"
          sx={{
            height: "20px",
            backgroundColor: "#d3d3d3",
            "& .MuiLinearProgress-bar": {
              backgroundColor: getProgressColor(),
            },
          }}
        />
        <BlinkingText
          variant="body2"
          sx={{
            position: "absolute",
            top: 0,
            left: "13%",
            transform: "translateX(-50%)",
            fontWeight: "bold",
            color: "#ffffff",
          }}
        >
          {postCreationProgress.status}...
        </BlinkingText>
      </Box>
      <DialogContent>
        <DialogContentText>
          <Box
            noValidate
            component="form"
            sx={{
              display: "flex",
              flexDirection: "column",
              m: "auto",
            }}
          >
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Profile Name</b>
                    </TableCell>
                    <TableCell>
                      <b>Company</b>
                    </TableCell>
                    <TableCell>
                      <b>Profile ID</b>
                    </TableCell>
                    <TableCell>
                      <b>Status</b>
                    </TableCell>
                    <TableCell>
                      <b>Actions</b>
                    </TableCell>
                    <TableCell>
                      <b>Error Message</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedProfiles &&
                    selectedProfiles.map(
                      (profile: ILinkedInProfilePost, index: number) => (
                        <TableRow key={index}>
                          <TableCell>
                            {profile.profileInfo.profileName}
                          </TableCell>
                          <TableCell>
                            {profile.profileInfo.companyName || "Personal"}
                          </TableCell>
                          <TableCell>{profile.profileInfo.profileId}</TableCell>
                          <TableCell>
                            {profile.profileInfo.status == null ? (
                              <CircularProgress color="secondary" size="30px" />
                            ) : profile.profileInfo.status ? (
                              <CheckCircleOutlineIcon color="success" />
                            ) : (
                              <CancelIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell>
                            {profile.profileInfo.postUrl ? (
                              <IconButton
                                onClick={() =>
                                  window.open(
                                    profile.profileInfo.postUrl,
                                    "_blank"
                                  )
                                }
                                color="primary"
                                size="small"
                              >
                                <VisibilityIcon />
                              </IconButton>
                            ) : (
                              <BlockIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell>
                            {profile.profileInfo.errorMessage && (
                              <Typography variant="caption" color="error">
                                {profile.profileInfo.errorMessage}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          disabled={!allApiCallsCompleted}
          onClick={() => navigate("/post-management/posts")}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LinkedInPostStatusDialog;
