const pool = require("../config/db");
const logger = require("../utils/logger");

module.exports = class Twitter {
  /**
   * Save Twitter OAuth tokens
   * @param {Object} tokenData - Token data
   * @returns {Promise<Object>} Save result
   */
  static async saveOAuthTokens(tokenData) {
    try {
      const query = `
        INSERT INTO twitter_oauth_tokens
        (user_id, twitter_user_id, twitter_username, twitter_user_name, twitter_user_email, twitter_user_picture, access_token, refresh_token, expires_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        twitter_username = VALUES(twitter_username),
        twitter_user_name = VALUES(twitter_user_name),
        twitter_user_email = VALUES(twitter_user_email),
        twitter_user_picture = VALUES(twitter_user_picture),
        access_token = VALUES(access_token),
        refresh_token = VALUES(refresh_token),
        expires_at = VALUES(expires_at),
        updated_at = CURRENT_TIMESTAMP
      `;

      const values = [
        tokenData.userId,
        tokenData.twitterUserId,
        tokenData.twitterUsername,
        tokenData.twitterUserName || null,
        tokenData.twitterUserEmail || null,
        tokenData.twitterUserPicture || null,
        tokenData.accessToken,
        tokenData.refreshToken || null,
        tokenData.expiresAt || null,
      ];

      const result = await pool.query(query, values);

      logger.info("Twitter OAuth tokens saved successfully", {
        userId: tokenData.userId,
        businessId: tokenData.businessId,
        twitterUserId: tokenData.twitterUserId,
        twitterUsername: tokenData.twitterUsername,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving Twitter OAuth tokens:", {
        error: error.message,
        userId: tokenData.userId,
        businessId: tokenData.businessId,
        twitterUserId: tokenData.twitterUserId,
      });
      throw error;
    }
  }

  /**
   * Save Twitter accounts
   * @param {Array} accountsData - Array of account data
   * @returns {Promise<Object>} Save result
   */
  static async saveAccounts(accountsData) {
    try {
      if (!accountsData || accountsData.length === 0) {
        return { success: true, result: { affectedRows: 0 } };
      }

      const query = `
        INSERT INTO twitter_accounts
        (twitter_oauth_token_id, user_id, account_id, account_name, account_username, account_description, account_picture_url, followers_count, following_count, tweet_count, is_verified)
        VALUES ?
        ON DUPLICATE KEY UPDATE
        account_name = VALUES(account_name),
        account_username = VALUES(account_username),
        account_description = VALUES(account_description),
        account_picture_url = VALUES(account_picture_url),
        followers_count = VALUES(followers_count),
        following_count = VALUES(following_count),
        tweet_count = VALUES(tweet_count),
        is_verified = VALUES(is_verified),
        updated_at = CURRENT_TIMESTAMP
      `;

      const values = accountsData.map((account) => [
        account.twitterOAuthTokenId,
        account.userId,
        account.accountId,
        account.accountName,
        account.accountUsername,
        account.accountDescription || null,
        account.accountPictureUrl || null,
        account.followersCount || 0,
        account.followingCount || 0,
        account.tweetCount || 0,
        account.isVerified || 0,
      ]);

      const result = await pool.query(query, [values]);

      logger.info("Twitter accounts saved successfully", {
        accountCount: accountsData.length,
        userId: accountsData[0]?.userId,
        businessId: accountsData[0]?.businessId,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving Twitter accounts:", {
        error: error.message,
        accountCount: accountsData?.length || 0,
      });
      throw error;
    }
  }

  /**
   * Get Twitter accounts for user
   * @param {number} userId - User ID
   * @returns {Promise<Array>} Twitter accounts
   */
  static async getAccounts(userId) {
    try {
      const query = `
        SELECT ta.*, tot.twitter_username, tot.twitter_user_name, tot.access_token
        FROM twitter_accounts ta
        JOIN twitter_oauth_tokens tot ON ta.twitter_oauth_token_id = tot.id
        WHERE ta.user_id = ? AND ta.is_active = 1
        ORDER BY ta.account_name ASC
      `;

      const result = await pool.query(query, [userId]);

      logger.info("Twitter accounts retrieved successfully", {
        userId,
        accountCount: result.length,
      });

      return result;
    } catch (error) {
      logger.error("Error retrieving Twitter accounts:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Save Twitter post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Save result
   */
  static async savePost(postData) {
    try {
      const query = `
        INSERT INTO twitter_posts
        (user_id, account_id, twitter_post_id, post_content, post_response, tweet_text, media_urls, hashtags, mentions, reply_to_tweet_id, quote_tweet_id, published, scheduled_publish_time, status, twitter_url, retweet_count, like_count, reply_count, quote_count, impression_count)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        postData.userId,
        postData.accountId,
        postData.twitterPostId,
        JSON.stringify(postData.postContent),
        JSON.stringify(postData.postResponse),
        postData.tweetText,
        JSON.stringify(postData.mediaUrls || []),
        JSON.stringify(postData.hashtags || []),
        JSON.stringify(postData.mentions || []),
        postData.replyToTweetId || null,
        postData.quoteTweetId || null,
        postData.published,
        postData.scheduledPublishTime || null,
        postData.status,
        postData.twitterUrl,
        postData.retweetCount || 0,
        postData.likeCount || 0,
        postData.replyCount || 0,
        postData.quoteCount || 0,
        postData.impressionCount || 0,
      ];

      const result = await pool.query(query, values);

      logger.info("Twitter post saved successfully", {
        userId: postData.userId,
        businessId: postData.businessId,
        accountId: postData.accountId,
        twitterPostId: postData.twitterPostId,
        status: postData.status,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving Twitter post:", {
        error: error.message,
        userId: postData.userId,
        businessId: postData.businessId,
        accountId: postData.accountId,
      });
      throw error;
    }
  }

  /**
   * Get Twitter posts for user
   * @param {number} userId - User ID
   * @param {number} limit - Limit
   * @param {number} offset - Offset
   * @returns {Promise<Array>} Twitter posts
   */
  static async getPosts(userId, limit = 50, offset = 0) {
    try {
      const query = `
        SELECT tp.*, ta.account_name, ta.account_username
        FROM twitter_posts tp
        JOIN twitter_accounts ta ON tp.account_id = ta.account_id
        WHERE tp.user_id = ?
        ORDER BY tp.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const result = await pool.query(query, [userId, limit, offset]);

      logger.info("Twitter posts retrieved successfully", {
        userId,
        postCount: result.length,
        limit,
        offset,
      });

      return result;
    } catch (error) {
      logger.error("Error retrieving Twitter posts:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Check if user has Twitter connection
   * @param {number} userId - User ID
   * @returns {Promise<boolean>} Connection status
   */
  static async hasConnection(userId) {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM twitter_oauth_tokens
        WHERE user_id = ? AND status_id = 1
      `;

      const result = await pool.query(query, [userId]);
      return result[0].count > 0;
    } catch (error) {
      logger.error("Error checking Twitter connection:", {
        error: error.message,
        userId,
      });
      return false;
    }
  }
};
