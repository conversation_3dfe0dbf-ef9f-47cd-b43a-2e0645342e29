const express = require("express");
const router = express.Router();
const validateSchema = require("../middleware/validateRequest");
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  welcome,
  reviewList,
  refreshReviews,
  replyToReviews,
  getReplyFromAI,
  createReviewTags,
  getAllTags,
  updateReviewTagsToReviews,
  getExtenderReviewList,
  getUnansweredReviewsCount,
} = require("../controllers/reviews.controller");

const { gmbTokenMapping } = require("../middleware/gmbTokenMapping");

router.get("/", welcome);

router.get("/reviews-list/:userId", isAuthenticated, reviewList);

router.post(
  "/extended-reviews-list/:userId",
  isAuthenticated,
  getExtenderReviewList
);

router.get("/reviews-list", isAuthenticated, gmbTokenMapping, refreshReviews);

router.post(
  "/reviews-comment",
  isAuthenticated,
  gmbTokenMapping,
  replyToReviews
);
router.post("/reply-using-ai", isAuthenticated, getReplyFromAI);

router.post("/create-review-tags", isAuthenticated, createReviewTags);

router.get("/get-all-tags", isAuthenticated, getAllTags);

router.post("/review-update-tags", isAuthenticated, updateReviewTagsToReviews);

router.get(
  "/unanswered-count/:userId",
  isAuthenticated,
  getUnansweredReviewsCount
);

module.exports = router;
