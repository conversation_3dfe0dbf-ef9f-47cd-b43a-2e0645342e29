import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Too<PERSON>ip, CircularProgress, Box } from "@mui/material";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import {
  ExcelExportService,
  ChartExportData,
} from "../../services/excelExport.service";

interface ExportButtonProps {
  chartData: ChartExportData;
  isLoading?: boolean;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  color?: "primary" | "secondary" | "default";
  tooltipText?: string;
}

const ExportButton: React.FC<ExportButtonProps> = ({
  chartData,
  isLoading = false,
  disabled = false,
  size = "medium",
  color = "primary",
  tooltipText = "Export chart data to Excel",
}) => {
  const [exporting, setExporting] = React.useState(false);

  const handleExport = async () => {
    if (disabled || isLoading || exporting) {
      return;
    }

    try {
      setExporting(true);

      // Validate chart data before export
      if (
        !chartData ||
        !chartData.data ||
        !chartData.data.labels ||
        !chartData.data.data
      ) {
        console.error("Invalid chart data for export:", chartData);
        alert(
          "No data available to export. Please ensure you have selected data and try again."
        );
        return;
      }

      // Add a small delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Export the chart data
      ExcelExportService.exportChartToExcel(chartData);
    } catch (error) {
      console.error("Failed to export chart data:", error);
      alert(
        `Failed to export chart data: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setExporting(false);
    }
  };

  const isButtonDisabled = disabled || isLoading || exporting;
  const showLoading = isLoading || exporting;

  return (
    <Tooltip title={isButtonDisabled ? "Export not available" : tooltipText}>
      <Box sx={{ position: "relative", display: "inline-flex" }}>
        <IconButton
          onClick={handleExport}
          disabled={isButtonDisabled}
          size={size}
          color={color}
          sx={{
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              transform: !isButtonDisabled ? "scale(1.1)" : "none",
              backgroundColor: !isButtonDisabled
                ? "rgba(25, 118, 210, 0.08)"
                : "transparent",
            },
            "&:disabled": {
              opacity: 0.5,
            },
          }}
        >
          {showLoading ? (
            <CircularProgress
              size={size === "small" ? 16 : size === "large" ? 28 : 20}
              color="inherit"
            />
          ) : (
            <FileDownloadIcon
              fontSize={
                size === "small"
                  ? "small"
                  : size === "large"
                  ? "large"
                  : "medium"
              }
            />
          )}
        </IconButton>
      </Box>
    </Tooltip>
  );
};

export default ExportButton;
