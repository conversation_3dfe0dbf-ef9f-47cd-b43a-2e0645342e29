#!/usr/bin/env node

// Get environment from command line argument or default to development
const environment = process.argv[2] || process.env.NODE_ENV || 'development';
const envFile = `.env.${environment}`;

console.log(`Validating environment: ${environment.toUpperCase()}`);
console.log(`Loading configuration from: ${envFile}`);

// Load environment configuration
require('dotenv').config({ path: envFile });

// Import validation utilities
const { validateEnvironment, displayEnvironmentInfo } = require('../utils/validateEnvironment');

// Run validation
try {
    validateEnvironment(environment);
    displayEnvironmentInfo();
    console.log('\n🎉 Environment validation completed successfully!');
} catch (error) {
    console.error('\n💥 Environment validation failed:', error.message);
    process.exit(1);
}
