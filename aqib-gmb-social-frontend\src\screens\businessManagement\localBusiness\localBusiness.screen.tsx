import React, {
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import PageProps from "../../../models/PageProps.interface";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import Grid from "@mui/material/Grid";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import "./localBusiness.screen.style.css";
import {
  Divider,
  Drawer,
  FormControl,
  Grid2,
  InputLabel,
  MenuItem,
  Pagination,
  Paper,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import "../manageBusiness/manageBusiness.screen.style.css";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../../context/loading.context";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../../interfaces/response/IBusinessListResponseModel";
import BusinessService from "../../../services/business/business.service";
import AddEditBusinessComponent from "../../../components/addEditBusiness/addEditBusiness.component";
import { IBusinessCreationResponseModel } from "../../../interfaces/response/IBusinessCreationResponseModel";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { MessageConstants } from "../../../constants/message.constant";
import { ToastContext } from "../../../context/toast.context";
import { IAlertDialogConfig } from "../../../interfaces/IAlertDialogConfig";
import AlertDialog from "../../../components/alertDialog/alertDialog.component";
import { IAddBusinessRequestModel } from "../../../interfaces/request/IAddBusinessRequestModel";
import { IPaginationModel } from "../../../interfaces/IPaginationModel";
import { DEFAULT_PAGINATION } from "../../../constants/dbConstant.constant";
import { IPaginationResponseModel } from "../../../interfaces/IPaginationResponseModel";
import { Formik } from "formik";
import * as yup from "yup";
import { ILocationListRequestModel } from "../../../interfaces/request/ILocationListRequestModel";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../../interfaces/response/IBusinessGroupsResponseModel";
import LocationService from "../../../services/location/location.service";
import {
  ILocation,
  ILocationsListPaginatedResponseModel,
} from "../../../interfaces/response/ILocationsListResponseModel";
import ApplicationHelperService from "../../../services/ApplicationHelperService";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import RemoveRedEyeOutlinedIcon from "@mui/icons-material/RemoveRedEyeOutlined";
import { useNavigate } from "react-router-dom";
import NearMeOutlinedIcon from "@mui/icons-material/NearMeOutlined";
import EditIcon from "@mui/icons-material/Edit";
import { InputAdornment } from "@mui/material";
import TextField from "@mui/material/TextField";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import IconButton from "@mui/material/IconButton";
import NoRowsFound from "../../../components/noRowsFound/noRowsFound.component";

type IDeleteRecord = {
  isShow: boolean;
  data: any;
  businessId: number;
};

type IConfirmSync = {
  isShow: boolean;
  data: any;
};

const LocalBusiness: FunctionComponent<PageProps> = ({ title }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [openAddEdit, setOpenAddEdit] = React.useState<IDeleteRecord>({
    isShow: false,
    data: null,
    businessId: 0,
  });

  const [showConfirmPopup, setShowConfirmPopup] = useState<IDeleteRecord>({
    isShow: false,
    data: null,
    businessId: 0,
  });
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const _businessService = new BusinessService(dispatch);
  const _locationService = new LocationService(dispatch);
  const _applicationHelperService = new ApplicationHelperService({});
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const { setLoading } = useContext(LoadingContext);
  const [locationList, setLocationList] = useState<ILocation[]>([]);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [confirmSync, setConfirmSync] = useState<IConfirmSync>();
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [businessGroupsOnBusiness, setBusinessGroupsOnBusiness] = useState<
    IBusinessGroup[]
  >([]);
  const [paginationModel, setPaginationModel] =
    useState<IPaginationModel>(DEFAULT_PAGINATION);
  const [paginationResponseModel, setPaginationResponseModel] =
    useState<IPaginationResponseModel>();
  const [alertConfig, setAlertConfig] = useState<IAlertDialogConfig>({
    isShow: false,
    callBack: () => undefined,
  });
  const INITIAL_VALUES: ILocationListRequestModel = {
    businessId: 0,
    businessGroupId: 0,
    search: "",
  };
  const [initialValues, setInitialValues] =
    useState<ILocationListRequestModel>(INITIAL_VALUES);

  useEffect(() => {
    document.title = `${title}`;
  }, []);

  useEffect(() => {
    fetchLocationsPaginated();
  }, [paginationModel]);

  useEffect(() => {
    setPaginationModel({ ...DEFAULT_PAGINATION });
  }, [initialValues]);

  useEffect(() => {
    getBusiness();
    getBusinessGroups();
  }, []);

  const handleLocationsSync = async () => {
    var response = await _locationService.getAccountsCount(userInfo.id);
    if (response.result.rowCount > 0) {
      setConfirmSync({ isShow: true, data: response.result.rowCount });
    } else {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.SyncLocationsConditionFail,
        true
      );
    }
  };

  const syncAllAccounts = async () => {
    try {
      setLoading(true);
      var response = await _locationService.syncAllLocations(userInfo.id);
      setToastConfig(ToastSeverity.Success, response.message, true);
      setConfirmSync({ isShow: false, data: 0 });
    } catch (error) {
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  };

  const fetchLocationsPaginated = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListPaginatedResponseModel =
        await _locationService.getLocationsPaginated(
          userInfo.id,
          paginationModel,
          initialValues.businessId,
          initialValues.businessGroupId,
          initialValues.search || ""
        );
      console.log("Location List: ", locationListResponse.results);
      setLocationList([...locationListResponse.results]);
      setPaginationResponseModel({ ...locationListResponse.pagination });
    } catch (error) {}

    setLoading(false);
  };

  const LocationSchema = yup.object().shape({
    businessId: yup.number(),
    businessGroupId: yup.number(),
    search: yup.string(),
  });

  const _handleSaveAccount = async (values: ILocationListRequestModel) => {
    // try {
    //   const isValid = await UserSchema.isValid(values);
    //   if (isValid) {
    //     if (props.userId && props.userId > 0) {
    //       await _userService.updateUserLocation({
    //         userId: props.userId,
    //         statusId: 1,
    //         locationId: values.locationId,
    //       });
    //       props.callBack(undefined, undefined);
    //       setToastConfig(
    //         ToastSeverity.Success,
    //         MessageConstants.LocationsUpdatedSuccessfully,
    //         true
    //       );
    //     } else {
    //       var response: IUserCreationResponseModel =
    //         await _userService.createUser(values);
    //       if (response && response.userId > 0) {
    //         props.callBack(values, response);
    //       }
    //     }
    //   }
    // } catch (error: any) {
    //   if (error.response.data) {
    //     switch (error.response.data.error.code) {
    //       case "ER_DUP_ENTRY":
    //         setToastConfig(
    //           ToastSeverity.Error,
    //           error.response.data.error.sqlMessage.split("for")[0],
    //           true
    //         );
    //         break;
    //       default:
    //         setToastConfig(
    //           ToastSeverity.Error,
    //           error.response.data.error,
    //           true
    //         );
    //         break;
    //     }
    //   } else {
    //     setToastConfig(
    //       ToastSeverity.Error,
    //       MessageConstants.ApiErrorStandardMessage,
    //       true
    //     );
    //   }
    // }
  };

  const handleValidation = (values: ILocationListRequestModel) => {
    setInitialValues({
      businessId: values.businessId,
      businessGroupId: values.businessGroupId,
      search: values.search,
    });
  };

  const getBusiness = async () => {
    try {
      setLoading(true);
      let roles: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (roles.list.length > 0) {
        setBusinessList(roles.list);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      let businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {}

    setLoading(false);
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box>
              {confirmSync && confirmSync.isShow && (
                <Paper elevation={24}>
                  <Card sx={{ width: "100%" }}>
                    <CardContent>
                      <Typography gutterBottom variant="h5" component="div">
                        Sync Locations
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: "text.secondary" }}
                      >
                        We found {confirmSync.data} accounts linked to your
                        Google account. Click Continue to start syncing your
                        locations.
                      </Typography>
                    </CardContent>
                    <CardActions sx={{ justifyContent: "flex-end" }}>
                      <Button
                        className="updatesShapeBtn"
                        size="small"
                        variant="outlined"
                        onClick={() =>
                          setConfirmSync({ ...confirmSync, isShow: false })
                        }
                      >
                        Cancel
                      </Button>
                      <Button
                        className="updatesShapeBtn"
                        size="small"
                        variant="contained"
                        onClick={() => syncAllAccounts()}
                      >
                        Continue
                      </Button>
                    </CardActions>
                  </Card>
                </Paper>
              )}

              <Box className="commonTableHeader">
                <h3 className="pageTitle">Local Business</h3>
                <Button
                  onClick={() => handleLocationsSync()}
                  className="LocalBusinessSyncBtn"
                  sx={{
                    minHeight: "50px", // Set the desired height
                    width: "100px",
                  }}
                  startIcon={
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 48 48"
                      width="24px"
                      height="24px"
                    >
                      <path
                        fill="#fbc02d"
                        d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12 s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20 s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
                      />
                      <path
                        fill="#e53935"
                        d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039 l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
                      />
                      <path
                        fill="#4caf50"
                        d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36 c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
                      />
                      <path
                        fill="#1565c0"
                        d="M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571 c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
                      />
                    </svg>
                  }
                >
                  Sync
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
              <Formik
                enableReinitialize
                initialValues={{ ...initialValues }}
                validationSchema={LocationSchema}
                onSubmit={(values, { setSubmitting }) => {
                  _handleSaveAccount(values);
                }}
                validate={handleValidation}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  setFieldValue,
                  handleReset,
                  isSubmitting,
                  isValid,
                  /* and other goodies */
                }) => (
                  <form onSubmit={handleSubmit} onReset={handleReset}>
                    <Grid container spacing={2}>
                      <Grid
                        item
                        xs={12}
                        md={6}
                        lg={4}
                        className="customColSpacing"
                      >
                        <FormControl variant="filled" fullWidth>
                          <InputLabel id="outlined-country-dropdown-label">
                            Business
                          </InputLabel>

                          <Select
                            fullWidth
                            id="businessId"
                            label="Business"
                            value={values.businessId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setBusinessGroupsOnBusiness(
                                businessGroups.filter(
                                  (x: IBusinessGroup) =>
                                    x.businessId === +evt.target.value
                                )
                              );
                              setFieldValue("businessGroupId", 0);
                              setFieldValue("businessId", +evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={0}>All</MenuItem>
                            {businessList &&
                              businessList.map((business: IBusiness) => (
                                <MenuItem
                                  key={business.id}
                                  value={business.id.toString()}
                                >
                                  {business.businessName}
                                </MenuItem>
                              ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        md={6}
                        lg={4}
                        className="customColSpacing"
                      >
                        <FormControl variant="filled" fullWidth>
                          <InputLabel id="outlined-country-dropdown-label">
                            Account
                          </InputLabel>

                          <Select
                            fullWidth
                            id="businessGroupId"
                            label="Group"
                            value={values.businessGroupId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue(
                                "businessGroupId",
                                +evt.target.value
                              );

                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={0}>All</MenuItem>
                            {businessGroupsOnBusiness &&
                              businessGroupsOnBusiness.map(
                                (businessGroup: IBusinessGroup) => (
                                  <MenuItem
                                    key={businessGroup.id}
                                    value={businessGroup.id.toString()}
                                  >
                                    {businessGroup.accountName}
                                  </MenuItem>
                                )
                              )}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        md={6}
                        lg={4}
                        className="customColSpacing"
                      >
                        <TextField
                          id="search"
                          className="width100  text-box-border-radius"
                          label="Search Location"
                          type="text"
                          variant="filled"
                          onChange={(e) => {
                            if (e.target.value.length > 2) {
                              setFieldValue("search", e.target.value);
                            } else if (e.target.value.length === 0) {
                              setFieldValue("search", "");
                            }
                          }}
                          sx={{
                            backgroundColor: "#ffffff",
                            "& .MuiFilledInput-root": {
                              backgroundColor: "#ffffff",
                              borderRadius: "12px",
                              "&:hover": {
                                backgroundColor: "#ffffff",
                              },
                              "&.Mui-focused": {
                                backgroundColor: "#ffffff",
                              },
                            },
                          }}
                          InputProps={{
                            disableUnderline: true,
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton edge="end">
                                  <SearchOutlinedIcon />
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                    </Grid>
                    {/* <Grid2
                      spacing={0}
                      direction="row"
                      alignItems="center"
                      justifyContent="center"
                      columnSpacing={{ xs: 2, md: 3 }}
                      container
                      height={50}
                    >
                      <Grid2 size={3} justifyItems={"flex-start"}>
                        
                      </Grid2>
                      <Grid2 size={3} justifyItems={"flex-end"}>
                        
                      </Grid2>
                      <Grid2 size={3} justifyItems={"flex-end"}>
                        
                      </Grid2>

                      <Grid2 size={3} justifyItems={"flex-end"}></Grid2>
                    </Grid2> */}
                  </form>
                )}
              </Formik>
              <Divider style={{ margin: 10, height: 5 }} />
              <Box>
                <TableContainer className="commonTable">
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Sr.No</TableCell>
                        <TableCell>Location Name</TableCell>
                        <TableCell>Locality</TableCell>
                        <TableCell>Postal Code</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Business Stream</TableCell>
                        <TableCell>Updated On</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {locationList.length > 0 ? (
                        locationList.map(
                          (location: ILocation, index: number) => (
                            <TableRow key={index}>
                              <TableCell scope="row" data-label="Sr.No">
                                {index + 1}
                              </TableCell>
                              <TableCell data-label="Location Name">
                                {/* <Button
                                  className="padL32"
                                  variant="text"
                                  color="primary"
                                  onClick={() =>
                                    navigate(
                                      `/business-management/business-summary/${location.businessId}/${location.gmbAccountId}/${location.gmbLocationId}`
                                    )
                                  }
                                  sx={{
                                    textTransform: "none",
                                    padding: 0,
                                    minWidth: "auto",
                                    textAlign: "left",
                                    color: "var(--titleColor)",
                                  }}
                                > */}
                                {location.gmbLocationName}
                                {/* </Button> */}
                              </TableCell>
                              <TableCell data-label="Locality">
                                {location.locality}
                              </TableCell>
                              <TableCell data-label="Postal Code">
                                {location.postalCode}
                              </TableCell>
                              <TableCell data-label="Status">
                                {location.statusId === 1
                                  ? "Active"
                                  : "Inactive"}
                              </TableCell>
                              <TableCell data-label="Business Stream">
                                {location.businessStream}
                              </TableCell>
                              <TableCell data-label="Updated On">
                                {_applicationHelperService.getUserDateTimeFormat(
                                  location.updatedAt
                                )}
                              </TableCell>
                              <TableCell
                                align="right"
                                data-label="Actions"
                                sx={{
                                  whiteSpace: "nowrap",
                                  minWidth: 100,
                                }}
                              >
                                <Box className="commonTableActionBtns">
                                  <Box>
                                    <Button
                                      onClick={() => {
                                        console.log(location);
                                        navigate(
                                          `/business-management/business-summary/${location.businessId}/${location.gmbAccountId}/${location.gmbLocationId}`
                                        );
                                      }}
                                      variant="outlined"
                                      className={"emptyBtn editIconBtn"}
                                      startIcon={<RemoveRedEyeOutlinedIcon />}
                                      title="View Business Summary"
                                    ></Button>
                                    <Button
                                      onClick={() => {
                                        navigate(
                                          `/business-management/business-profile/${location.businessId}/${location.gmbAccountId}/${location.gmbLocationId}`
                                        );
                                      }}
                                      variant="outlined"
                                      className={"emptyBtn editIconBtn"}
                                      startIcon={<EditIcon />}
                                      title="Manage Business Profile"
                                      sx={{ ml: 1 }}
                                    ></Button>
                                    <Button
                                      onClick={() => {
                                        window.open(location.mapsUri);
                                      }}
                                      variant="outlined"
                                      className={"emptyBtn viewIconBtn"}
                                      startIcon={<NearMeOutlinedIcon />}
                                      disabled={!location.mapsUri}
                                      title="View on Google Maps"
                                      sx={{ ml: 1 }}
                                    ></Button>
                                  </Box>
                                </Box>
                              </TableCell>
                            </TableRow>
                          )
                        )
                      ) : (
                        <NoRowsFound />
                      )}
                    </TableBody>
                  </Table>
                  <Grid2
                    spacing={0}
                    direction="row"
                    alignItems="center"
                    justifyContent="center"
                    columnSpacing={{ xs: 2, md: 3 }}
                    container
                    height={50}
                  >
                    <Grid2 size={6} justifyItems={"flex-start"}>
                      {paginationResponseModel && (
                        <Typography className="pagination-Text">
                          {_applicationHelperService.getPaginationText(
                            paginationModel.pageNo,
                            paginationModel.offset,
                            paginationResponseModel.totalRecords
                          )}
                        </Typography>
                      )}
                    </Grid2>
                    <Grid2 size={6} justifyItems={"flex-end"}>
                      <Pagination
                        color="primary"
                        count={paginationResponseModel?.pageCount}
                        page={paginationModel.pageNo}
                        onChange={(
                          event: React.ChangeEvent<unknown>,
                          page: number
                        ) =>
                          setPaginationModel({
                            ...paginationModel,
                            pageNo: page,
                          })
                        }
                      />
                    </Grid2>
                  </Grid2>
                </TableContainer>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>

      {/* Modal */}
      <GenericDrawer
        component={
          <AddEditBusinessComponent
            businessId={openAddEdit.businessId}
            editData={openAddEdit.data}
            callBack={(
              request: IAddBusinessRequestModel | undefined,
              resp: IBusinessCreationResponseModel | undefined
            ) => {
              if (resp && resp.response.affectedRows > 0) {
                setToastConfig(
                  ToastSeverity.Success,
                  MessageConstants.BusinessCreatedSuccessfully,
                  true
                );
              }

              setOpenAddEdit({ isShow: false, data: null, businessId: 0 });
              setPaginationModel({ ...paginationModel, pageNo: 0 });
            }}
          />
        }
        isShow={openAddEdit.isShow}
        callback={() =>
          setOpenAddEdit({ isShow: false, data: null, businessId: 0 })
        }
      />

      {alertConfig && alertConfig.isShow && (
        <AlertDialog
          alertConfig={alertConfig}
          callBack={alertConfig.callBack}
        />
      )}
    </div>
  );
};

export default LocalBusiness;
