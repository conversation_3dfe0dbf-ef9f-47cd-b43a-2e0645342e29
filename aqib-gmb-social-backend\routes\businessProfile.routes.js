const express = require("express");
const router = express.Router();
const gmbService = require("../services/gmb.service");
const GMB_ACTIONS = require("../constants/gmb-actions");
const { isAuthenticated } = require("../middleware/isAuthenticated");
const { gmbTokenMapping } = require("../middleware/gmbTokenMapping");
const Reviews = require("../models/reviews.models");

/**
 * @route POST /api/business-profile/update-business-name
 * @desc Update business name for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-business-name",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, businessName } = req.body;

      if (!locationId || !businessName) {
        return res.status(400).json({
          success: false,
          message: "Location ID and business name are required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_business_name,
        req,
        reqBodyData: { locationId, businessName },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-business-name route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/update-phone-numbers
 * @desc Update phone numbers for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-phone-numbers",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, phoneNumbers } = req.body;

      if (!locationId || !Array.isArray(phoneNumbers)) {
        return res.status(400).json({
          success: false,
          message: "Location ID and phone numbers array are required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_phone_numbers,
        req,
        reqBodyData: { locationId, phoneNumbers },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-phone-numbers route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/update-website-url
 * @desc Update website URL for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-website-url",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, websiteUri } = req.body;

      if (!locationId) {
        return res.status(400).json({
          success: false,
          message: "Location ID is required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_website_url,
        req,
        reqBodyData: { locationId, websiteUri },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-website-url route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/update-business-hours
 * @desc Update business hours for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-business-hours",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, regularHours } = req.body;

      if (!locationId || !regularHours) {
        return res.status(400).json({
          success: false,
          message: "Location ID and regular hours are required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_business_hours,
        req,
        reqBodyData: { locationId, regularHours },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-business-hours route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/update-categories
 * @desc Update categories for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-categories",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, categories } = req.body;

      if (!locationId || !categories) {
        return res.status(400).json({
          success: false,
          message: "Location ID and categories are required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_categories,
        req,
        reqBodyData: { locationId, categories },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-categories route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/update-service-items
 * @desc Update service items for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-service-items",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, serviceItems } = req.body;

      if (!locationId || !Array.isArray(serviceItems)) {
        return res.status(400).json({
          success: false,
          message: "Location ID and service items array are required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_service_items,
        req,
        reqBodyData: { locationId, serviceItems },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-service-items route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/update-service-area
 * @desc Update service area for a Google Business Profile location
 * @access Private
 */
router.post(
  "/update-service-area",
  isAuthenticated,
  gmbTokenMapping,
  async (req, res) => {
    try {
      const { locationId, serviceArea } = req.body;

      if (!locationId || !serviceArea) {
        return res.status(400).json({
          success: false,
          message: "Location ID and service area are required",
        });
      }

      const response = await gmbService.reqGMBApi({
        action: GMB_ACTIONS.update_service_area,
        req,
        reqBodyData: { locationId, serviceArea },
      });

      if (response.success) {
        res.status(200).json(response);
      } else {
        res.status(response.status || 500).json(response);
      }
    } catch (error) {
      console.error("Error in update-service-area route:", error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

/**
 * @route POST /api/business-profile/generate-description
 * @desc Generate AI-powered business description
 * @access Private
 */
router.post("/generate-description", isAuthenticated, async (req, res) => {
  try {
    const { businessName, categories, serviceItems } = req.body;

    if (!businessName) {
      return res.status(400).json({
        success: false,
        message: "Business name is required",
      });
    }

    // Build context for AI generation
    let prompt = `Generate a professional business description for "${businessName}"`;

    if (categories && categories.length > 0) {
      const categoryNames = categories
        .map((cat) => cat.displayName || cat.name)
        .filter(Boolean);
      if (categoryNames.length > 0) {
        prompt += ` which is a ${categoryNames.join(", ")} business`;
      }
    }

    if (serviceItems && serviceItems.length > 0) {
      const services = serviceItems
        .map(
          (item) =>
            item.structuredServiceItem?.serviceTypeId ||
            item.freeFormServiceItem?.label
        )
        .filter(Boolean);
      if (services.length > 0) {
        prompt += ` offering services like ${services.slice(0, 5).join(", ")}`;
      }
    }

    prompt +=
      ". The description should be professional, engaging, and highlight what makes this business special. Keep it under 750 characters and focus on customer benefits.";

    // Generate description using AI
    const generatedDescription = await Reviews.getReplyFromAI(prompt, 5);

    res.status(200).json({
      success: true,
      message: "Business description generated successfully",
      data: {
        description: generatedDescription,
        businessName,
      },
    });
  } catch (error) {
    console.error("Error generating business description:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate business description",
      error: error.message,
    });
  }
});

/**
 * @route POST /api/business-profile/generate-services
 * @desc Generate AI-powered services list based on category
 * @access Private
 */
router.post("/generate-services", isAuthenticated, async (req, res) => {
  try {
    const { categoryName } = req.body;

    if (!categoryName) {
      return res.status(400).json({
        success: false,
        message: "Category name is required",
      });
    }

    // Build prompt for AI service generation
    let prompt = `Generate a comprehensive list of services that a "${categoryName}" business typically offers.

    Requirements:
    - Provide 15-20 relevant services
    - Each service should be a short, professional name (2-4 words)
    - Services should be specific and commonly offered by this type of business
    - Focus on services that customers would search for
    - Return only the service names, one per line
    - No descriptions, just the service names
    - Make them practical and realistic services

    Example format:
    Service Name 1
    Service Name 2
    Service Name 3

    Generate services for: ${categoryName}`;

    // Generate services using AI
    const generatedServices = await Reviews.getReplyFromAI(prompt, 5);

    // Parse the AI response into an array of services
    const servicesList = generatedServices
      .split("\n")
      .map((service) => service.trim())
      .filter((service) => service.length > 0 && !service.includes(":"))
      .slice(0, 20); // Limit to 20 services

    res.status(200).json({
      success: true,
      message: "Services generated successfully",
      data: {
        services: servicesList,
        categoryName,
      },
    });
  } catch (error) {
    console.error("Error generating services:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate services",
      error: error.message,
    });
  }
});

module.exports = router;
