import React, { useEffect, useRef, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import ThumbUpAltRoundedIcon from "@mui/icons-material/ThumbUpAltRounded";
import StarRoundedIcon from "@mui/icons-material/StarRounded";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { IReviewsResponse } from "../../interfaces/response/IReviewsListResponseModel";
import ColorPalette from "../colorPalette/colorPalette.component";
import EditElements from "../editElements/editElements.component";
import FeedbackTemplate from "../feedbackTemplate/feedbackTemplate.component";
import FeedbackCard from "../feedbackCard/feedbackCard.component";
import { CssBaseline, Container, Grid2, Divider } from "@mui/material";
import ImageBackgroundCard from "../imageBackgroundCard/imageBackgroundCard.component";
import TestimonialCard1 from "./createPostTemplates/cards/testimonialCard1/testimonialCard1.component";
import { IPostTemplateConfig } from "../../types/IPostTemplateConfig";
import UserAvatarWithName from "../userAvatarWIthName/userAvatarWIthName.component";
import { POST_TEMPLATE_CONFIG } from "../../constants/application.constant";
import html2canvas from "html2canvas";
import TestimonialCard2 from "./createPostTemplates/cards/testimonialCard2/testimonialCard2.component";
import TestimonialCard3 from "./createPostTemplates/cards/testimonialCard3/testimonialCard3.component";
import TestimonialCard4 from "./createPostTemplates/cards/testimonialCard4/testimonialCard4.component";

//Css
import "../createPost/createPost.component.style.css";

// Image imports
import testimonialCard1Image from "../../assets/feedbackBackgrouns/TestimonialCard1.png";
import testimonialCard2Image from "../../assets/feedbackBackgrouns/TestimonialCard2.png";
import testimonialCard3Image from "../../assets/feedbackBackgrouns/TestimonialCard3.png";
import testimonialCard4Image from "../../assets/feedbackBackgrouns/TestimonialCard4.jpg";
import testimonialCard5Image from "../../assets/feedbackBackgrouns/TestimonialCard5.png";
import testimonialCard6Image from "../../assets/feedbackBackgrouns/6.jpg";

import TestimonialCard5 from "./createPostTemplates/cards/testimonialCard5/testimonialCard5.component";
import TestimonialCard6 from "./createPostTemplates/cards/testimonialCard6/testimonialCard6.component";
import { styled } from "@mui/material/styles";
import RatingsStar from "../ratingsStar/ratingsStar.component";

const CreatePostComponent = (props: {
  review: IReviewsResponse;
  closeDrawer: () => null | void | undefined;
}) => {
  const [postTemplateConfig, setPostTemplateConfig] =
    useState<IPostTemplateConfig>();

  useEffect(() => {
    setPostTemplateConfig({
      ...POST_TEMPLATE_CONFIG,
      comment: props.review.review ? props.review.review : "",
      reviewerName: props.review.reviewerName ? props.review.reviewerName : "",
      starRating: props.review.starRating ? props.review.starRating : "",
      reviewerImage: props.review.reviewerProfilePic
        ? props.review.reviewerProfilePic
        : "",
    });
  }, []);

  const [selectedTab, setSelectedTab] = React.useState("editInfo");

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
  };

  const divRef = useRef();
  const [activeTemplate, setActiveTemplate] =
    useState<string>("TestimonialCard1");

  const handleDownload = () => {
    if (divRef.current) {
      html2canvas(divRef.current, {
        scale: 2, // Increase scale for higher resolution
        useCORS: true, // Enable CORS if external resources are used
      }).then((canvas) => {
        // Create an image from the canvas
        const image = canvas.toDataURL("image/png");

        // Create a temporary link to trigger the download
        const link = document.createElement("a");
        link.href = image;
        link.download = "Testimonial Post.png"; // Set the filename
        link.click(); // Trigger the download
      });
    }
  };

  const imagesArray = [
    {
      image: testimonialCard1Image,
      key: "TestimonialCard1",
    },
    {
      image: testimonialCard2Image,
      key: "TestimonialCard2",
    },
    {
      image: testimonialCard3Image,
      key: "TestimonialCard3",
    },
    {
      image: testimonialCard4Image,
      key: "TestimonialCard4",
    },
    {
      image: testimonialCard5Image,
      key: "TestimonialCard5",
    },
    {
      image: testimonialCard6Image,
      key: "TestimonialCard6",
    },
    // {
    //   image: require("../../assets/feedbackBackgrouns/7.jpg"),
    //   key: "TestimonialCard6",
    // },
  ];

  const ImageButton = styled(Button)({
    padding: 0,
    minWidth: "auto",
    borderRadius: "8px",
    overflow: "hidden",
    "&:hover": {
      opacity: 0.8,
    },
  });

  return (
    <Box className="commonModal createpost">
      <Typography>
        <Grid container>
          <Grid item xs={12} md={12} lg={7}>
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title responsiveHide"
            >
              Testimonial Post
            </Typography>
          </Grid>
          <Grid item xs={12} md={12} lg={5}>
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title"
            >
              <span className="responsiveHide">Post Preview</span>
              <span className="responsiveShow">Testimonial & Preview Post</span>
            </Typography>
          </Grid>
        </Grid>
      </Typography>
      <Box id="modal-modal-description" className="modal-modal-description">
        <Box className="height100 responsivePostModal">
          <Grid container className="height100">
            <Grid item xs={12} md={12} lg={7} className="height100">
              <Box className="height100 createPostLeft">
                <Box className="postPreviewInfo postPreviewInfoLeftTopCard">
                  <Box>
                    <Box>
                      <Typography>{props.review.gmbLocationName}</Typography>
                    </Box>
                  </Box>
                  <Box>
                    <UserAvatarWithName fullname={props.review.reviewerName} />
                    <Box>
                      <RatingsStar
                        starRating={props.review.starRating}
                        size={20}
                      />
                    </Box>
                  </Box>
                </Box>
                <Box className="postPreviewEdit">
                  <Tabs
                    value={selectedTab}
                    onChange={handleChange}
                    variant="standard"
                    allowScrollButtonsMobile
                    aria-label="Info"
                  >
                    <Tab label="Info" value="editInfo" />
                    <Tab label="Templates" value="templates" />
                    <Tab label="Background" value="editBackground" />
                    {/* <Tab label="Elements" value="editElements" /> */}
                  </Tabs>

                  <Box sx={{ p: 2 }}>
                    {selectedTab === "editInfo" && postTemplateConfig && (
                      <Box>
                        <Box className="commonInput">
                          <TextField
                            label="Comment"
                            multiline
                            rows={4}
                            variant="outlined"
                            fullWidth
                            placeholder="Testimonial Text"
                            value={props.review.review}
                          />
                        </Box>
                        <EditElements
                          templateConfig={postTemplateConfig}
                          callBack={(tempConf) =>
                            setPostTemplateConfig(tempConf)
                          }
                        />
                      </Box>
                    )}
                    {selectedTab === "templates" && (
                      <Grid2 container spacing={1} sx={{ padding: 3 }}>
                        {imagesArray.map((template, index) => (
                          <Grid item xs={12} sm={6} md={4} lg={3}>
                            {/* <Box
                              sx={{
                                backgroundImage: `url(${require(template.image)})`,
                                backgroundSize: "cover",
                                backgroundPosition: "center",
                                backgroundRepeat: "no-repeat",
                                borderRadius: 2,
                                color: "#fff",
                                padding: 4,
                                textAlign: "center",
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "center",
                                alignItems: "center",
                                height: 200,
                                width: 200,
                                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.3)",
                              }}
                            ></Box> */}
                            <ImageButton
                              className="postTemplateBgBtn"
                              onClick={() => setActiveTemplate(template.key)}
                            >
                              <img
                                className="postTemplateBg"
                                src={template.image}
                                alt="Button"
                              />
                            </ImageButton>
                          </Grid>
                        ))}
                      </Grid2>
                    )}
                    {selectedTab === "editBackground" && postTemplateConfig && (
                      <ColorPalette
                        templateConfig={postTemplateConfig}
                        callBack={(tempConf: IPostTemplateConfig) =>
                          setPostTemplateConfig({ ...tempConf })
                        }
                      />
                    )}
                    {/* {selectedTab === "editElements" && <EditElements />} */}
                  </Box>
                </Box>
                {/* <FeedbackTemplate /> */}
                {/* <Container>
                  <CssBaseline />
                  <FeedbackCard />
                </Container> */}
              </Box>
            </Grid>
            {/* <Divider
              orientation="vertical"
              flexItem
              sx={{
                margin: "0 16px", // Add spacing around the divider if needed
              }}
            /> */}
            <Grid
              item
              xs={12}
              md={12}
              lg={5}
              className="responsivePostModalRight"
            >
              <Box className="postPreview">
                <Box
                  ref={divRef}
                  className="postPreviewInner1"
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Box className="postPreviewInner2">
                    {postTemplateConfig &&
                      activeTemplate === "TestimonialCard1" && (
                        <TestimonialCard1
                          divRef={divRef}
                          templateConfig={postTemplateConfig}
                        />
                      )}

                    {postTemplateConfig &&
                      activeTemplate === "TestimonialCard2" && (
                        <TestimonialCard2
                          divRef={divRef}
                          templateConfig={postTemplateConfig}
                        />
                      )}

                    {postTemplateConfig &&
                      activeTemplate === "TestimonialCard3" && (
                        <TestimonialCard3
                          divRef={divRef}
                          templateConfig={{ ...postTemplateConfig }}
                        />
                      )}

                    {postTemplateConfig &&
                      activeTemplate === "TestimonialCard4" && (
                        <TestimonialCard4
                          divRef={divRef}
                          templateConfig={{ ...postTemplateConfig }}
                        />
                      )}

                    {postTemplateConfig &&
                      activeTemplate === "TestimonialCard5" && (
                        <TestimonialCard5
                          divRef={divRef}
                          templateConfig={{ ...postTemplateConfig }}
                        />
                      )}

                    {postTemplateConfig &&
                      activeTemplate === "TestimonialCard6" && (
                        <TestimonialCard6
                          divRef={divRef}
                          templateConfig={{ ...postTemplateConfig }}
                        />
                      )}
                  </Box>
                </Box>
                <Box className="postPreviewActions">
                  <Grid container spacing={1}>
                    <Grid item xs={7}>
                      <Button
                        variant="contained"
                        sx={{
                          textTransform: "capitalize",
                        }}
                        className="shadowCase button-border-radius"
                      >
                        Create an Instant Post
                      </Button>
                    </Grid>
                    <Grid item xs={5}>
                      <Button
                        variant="contained"
                        sx={{
                          textTransform: "capitalize",
                        }}
                        className="shadowCase button-border-radius"
                        onClick={handleDownload}
                      >
                        Download Image
                      </Button>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box className="">
        <Stack direction="row" className="commonFooter">
          <Button
            variant="outlined"
            className="secondaryOutlineBtn"
            onClick={props.closeDrawer}
          >
            Cancel
          </Button>
          <Button variant="contained" className="primaryFillBtn">
            Save Changes
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default CreatePostComponent;
