import React from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Box,
  Typography,
  SelectChangeEvent,
} from "@mui/material";
import { IFacebookPageData } from "../../interfaces/response/IFacebookCreatePostResponse";

interface FacebookPageSelectorProps {
  pages: IFacebookPageData[];
  selectedPageId: string;
  onPageChange: (pageId: string, page: IFacebookPageData | null) => void;
  error?: string;
  loading?: boolean;
}

const FacebookPageSelector: React.FC<FacebookPageSelectorProps> = ({
  pages,
  selectedPageId,
  onPageChange,
  error,
  loading = false,
}) => {
  const handlePageChange = (event: SelectChangeEvent) => {
    const pageId = event.target.value;
    const selectedPage = pages.find((page) => page.page_id === pageId) || null;
    onPageChange(pageId, selectedPage);
  };

  return (
    <FormControl variant="filled" fullWidth error={!!error}>
      <InputLabel id="facebook-page-select-label">
        Select Facebook Page
      </InputLabel>
      <Select
        labelId="facebook-page-select-label"
        value={selectedPageId}
        onChange={handlePageChange}
        disabled={loading || pages.length === 0}
        sx={{
          backgroundColor: "var(--whiteColor)",
          borderRadius: "5px",
        }}
      >
        <MenuItem value="">
          <em>Select a Facebook Page</em>
        </MenuItem>
        {pages.map((page) => (
          <MenuItem key={page.page_id} value={page.page_id}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {page.page_picture_url && (
                <Avatar
                  src={page.page_picture_url}
                  sx={{ width: 24, height: 24 }}
                />
              )}
              <Box>
                <Typography variant="body2">{page.page_name}</Typography>
                {page.page_category && (
                  <Typography variant="caption" color="text.secondary">
                    {page.page_category}
                  </Typography>
                )}
              </Box>
            </Box>
          </MenuItem>
        ))}
      </Select>
      {error && (
        <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
          {error}
        </Typography>
      )}
      {pages.length === 0 && !loading && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
          No Facebook pages found. Please connect your Facebook account first.
        </Typography>
      )}
    </FormControl>
  );
};

export default FacebookPageSelector;
