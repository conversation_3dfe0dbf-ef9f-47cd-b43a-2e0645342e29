const database = require("../config/database");
const logger = require("../utils/logger");

/**
 * Migration to add account_id and location_id columns to auto_reply_log table
 */
async function migrateAutoReplyLogTable() {
  try {
    logger.info("Starting auto_reply_log table migration");

    // Check if table exists
    const [tables] = await database.query("SHOW TABLES LIKE 'auto_reply_log'");

    if (tables.length === 0) {
      logger.info(
        "auto_reply_log table does not exist, will be created by model"
      );
      return;
    }

    // Check if account_id column exists
    const [accountIdColumns] = await database.query(
      "SHOW COLUMNS FROM auto_reply_log LIKE 'account_id'"
    );

    if (accountIdColumns.length === 0) {
      logger.info("Adding account_id column to auto_reply_log table");
      await database.query(
        "ALTER TABLE auto_reply_log ADD COLUMN account_id INT NULL AFTER id"
      );
    }

    // Check if location_id column exists
    const [locationIdColumns] = await database.query(
      "SHOW COLUMNS FROM auto_reply_log LIKE 'location_id'"
    );

    if (locationIdColumns.length === 0) {
      logger.info("Adding location_id column to auto_reply_log table");
      await database.query(
        "ALTER TABLE auto_reply_log ADD COLUMN location_id VARCHAR(255) NULL AFTER account_id"
      );
    }

    logger.info("auto_reply_log table migration completed successfully");
  } catch (error) {
    logger.error("Migration failed:", error);
    throw error;
  }
}

module.exports = {
  migrateAutoReplyLogTable,
};
