const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  welcome,
  getServiceAreaSuggestions,
  getPlaceDetails,
} = require("../controllers/googlePlaces.controller");

// Welcome endpoint
router.get("/", welcome);

// Get service area suggestions from Google Places API
// Requires minimum 3 characters, returns max 5 suggestions
router.get("/service-area-suggestions", isAuthenticated, getServiceAreaSuggestions);

// Get place details by place ID
router.get("/place-details/:placeId", isAuthenticated, getPlaceDetails);

module.exports = router;
