import React, { useState } from "react";
import {
  Box,
  TextField,
  FormControlLabel,
  Switch,
  Button,
  <PERSON>pography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import FacebookMultiPageSelector from "../../../components/facebook/FacebookMultiPageSelector";
import {
  IFacebookCreatePost,
  IFacebookSelectedPage,
} from "../../../interfaces/request/IFacebookCreatePost";
import { IFacebookPageData } from "../../../interfaces/response/IFacebookCreatePostResponse";

interface FacebookPostFormProps {
  formData: IFacebookCreatePost;
  onFormChange: (data: IFacebookCreatePost) => void;
  uploadedImages: any[];
  onImageUpload: () => void;
  onGalleryOpen: () => void;
  onImageRemove: (index: number) => void;
  errors?: any;
  facebookPages: IFacebookPageData[];
  loading?: boolean;
  selectedPages?: IFacebookSelectedPage[];
  onSelectedPagesChange?: (pages: IFacebookSelectedPage[]) => void;
  onSubmit?: () => void;
  isFacebookConnected?: boolean;
}

const FacebookPostForm: React.FC<FacebookPostFormProps> = ({
  formData,
  onFormChange,
  uploadedImages,
  onImageUpload,
  onGalleryOpen,
  onImageRemove,
  errors,
  facebookPages,
  loading = false,
  selectedPages = [],
  onSelectedPagesChange,
  onSubmit,
  isFacebookConnected = true,
}) => {
  const [scheduleForLater, setScheduleForLater] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Dayjs | null>(
    dayjs().add(1, "hour")
  );

  const handleMultiPagesChange = (pages: IFacebookSelectedPage[]) => {
    if (onSelectedPagesChange) {
      onSelectedPagesChange(pages);
    }
    // Update pageIds in formData for compatibility
    onFormChange({
      ...formData,
      pageIds: pages.map((p) => p.pageId),
    });
  };

  const handleMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFormChange({
      ...formData,
      message: event.target.value,
    });
  };

  const handleDescriptionChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    onFormChange({
      ...formData,
      description: event.target.value,
    });
  };

  const handleLinkChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFormChange({
      ...formData,
      link: event.target.value,
    });
  };

  const handleScheduleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isScheduled = event.target.checked;
    setScheduleForLater(isScheduled);

    onFormChange({
      ...formData,
      published: !isScheduled,
      scheduledPublishTime:
        isScheduled && scheduledDate ? scheduledDate.toISOString() : undefined,
    });
  };

  const handleDateChange = (newDate: Dayjs | null) => {
    setScheduledDate(newDate);
    if (scheduleForLater && newDate) {
      onFormChange({
        ...formData,
        scheduledPublishTime: newDate.toISOString(),
      });
    }
  };

  // Helper function to add {Page Name} placeholder
  const addPageNamePlaceholder = (field: "message" | "description") => {
    const currentValue =
      field === "message" ? formData.message : formData.description || "";
    const newValue = currentValue + (currentValue ? " " : "") + "{Page Name}";

    if (field === "message") {
      onFormChange({
        ...formData,
        message: newValue,
      });
    } else {
      onFormChange({
        ...formData,
        description: newValue,
      });
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Facebook Post Details
        </Typography>

        <Grid container spacing={3}>
          {/* Facebook Page Selection */}
          <Grid item xs={12}>
            <FacebookMultiPageSelector
              pages={facebookPages}
              selectedPages={selectedPages}
              onPagesChange={handleMultiPagesChange}
              error={errors?.pageId || errors?.pageIds}
              loading={loading}
            />
          </Grid>

          {/* Post Message */}
          <Grid item xs={12}>
            <Box sx={{ position: "relative" }}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Title"
                placeholder={
                  selectedPages.length > 1
                    ? "What's on your mind? Use {Page Name} to dynamically replace with each page's name."
                    : "What's on your mind?"
                }
                value={formData.message}
                onChange={handleMessageChange}
                error={!!errors?.message}
                helperText={
                  errors?.message ||
                  (selectedPages.length > 1
                    ? `${formData.message.length}/2200 characters. Tip: Use {Page Name} for dynamic replacement.`
                    : `${formData.message.length}/2200 characters`)
                }
                inputProps={{ maxLength: 2200 }}
                variant="filled"
                sx={{
                  backgroundColor: "var(--whiteColor)",
                  borderRadius: "5px",
                }}
                InputProps={{
                  endAdornment: selectedPages.length > 1 && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => addPageNamePlaceholder("message")}
                        size="small"
                        title="Add {Page Name} placeholder"
                        sx={{
                          color: "primary.main",
                          "&:hover": {
                            backgroundColor: "primary.light",
                            color: "white",
                          },
                        }}
                      >
                        <AutoFixHighIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Grid>

          {/* Description */}
          <Grid item xs={12}>
            <Box sx={{ position: "relative" }}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Description"
                placeholder="Add a description..."
                value={formData.description || ""}
                onChange={handleDescriptionChange}
                error={!!errors?.description}
                helperText={errors?.description}
                variant="filled"
                sx={{
                  backgroundColor: "var(--whiteColor)",
                  borderRadius: "5px",
                }}
                InputProps={{
                  endAdornment: selectedPages.length > 1 && (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => addPageNamePlaceholder("description")}
                        size="small"
                        title="Add {Page Name} placeholder"
                        sx={{
                          color: "primary.main",
                          "&:hover": {
                            backgroundColor: "primary.light",
                            color: "white",
                          },
                        }}
                      >
                        <AutoFixHighIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Grid>

          {/* Link (optional) */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Link (Optional)"
              placeholder="https://example.com"
              value={formData.link || ""}
              onChange={handleLinkChange}
              variant="filled"
              sx={{
                backgroundColor: "var(--whiteColor)",
                borderRadius: "5px",
              }}
            />
          </Grid>

          {/* Media Upload Section */}
          <Grid item xs={12}>
            <Box>
              <Box
                onClick={onGalleryOpen}
                sx={{
                  border: "2px dashed #e0e0e0",
                  borderRadius: 2,
                  p: 4,
                  textAlign: "center",
                  cursor: "pointer",
                  backgroundColor: "#fafafa",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    borderColor: "#1976d2",
                    backgroundColor: "#f5f5f5",
                  },
                }}
              >
                <CloudUploadIcon
                  sx={{
                    fontSize: 48,
                    color: "#9e9e9e",
                    mb: 1,
                  }}
                />
                <Typography variant="h6" sx={{ mb: 0.5, color: "#666" }}>
                  Add/Edit Post Media
                </Typography>
                <Typography variant="caption" sx={{ color: "#999" }}>
                  Supported: Images (JPG, PNG, GIF, WebP) and Videos (MP4, AVI,
                  MOV, WMV, FLV, WebM)
                </Typography>
              </Box>
            </Box>
          </Grid>

          {/* Schedule for Later */}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={scheduleForLater}
                  onChange={handleScheduleToggle}
                  color="primary"
                />
              }
              label="Schedule for later"
            />
          </Grid>

          {/* Date/Time Picker */}
          {scheduleForLater && (
            <Grid item xs={12}>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                  label="Schedule Date & Time"
                  value={scheduledDate}
                  onChange={handleDateChange}
                  minDateTime={dayjs()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: "filled",
                      sx: {
                        backgroundColor: "var(--whiteColor)",
                        borderRadius: "5px",
                      },
                    },
                  }}
                />
              </LocalizationProvider>
            </Grid>
          )}

          {/* Submit Button */}
          {onSubmit && isFacebookConnected && (
            <Grid item xs={12}>
              <Box sx={{ mt: 2 }}>
                <Button
                  className="updatesShapeBtn"
                  onClick={onSubmit}
                  variant="contained"
                  style={{ textTransform: "capitalize" }}
                  fullWidth
                  disabled={!selectedPages || selectedPages.length === 0}
                >
                  {selectedPages && selectedPages.length > 1
                    ? `Create Posts for ${selectedPages.length} Pages`
                    : "Create Facebook Post"}
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default FacebookPostForm;
