import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>Content,
  TextField,
  Typography,
  Box,
  Grid,
  FormControlLabel,
  Switch,
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
} from "@mui/material";
import { FILLED_INPUT_STYLES } from "../../../constants/styles.constant";
import {
  ImageOutlined,
  PhotoLibrary,
  Delete,
  CheckCircle,
  VideoLibrary,
} from "@mui/icons-material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import {
  IInstagramCreatePost,
  IInstagramSelectedAccount,
} from "../../../interfaces/request/IInstagramCreatePost";
import { IInstagramAccountData } from "../../../interfaces/response/IInstagramCreatePostResponse";
import InstagramMultiAccountSelector from "../../../components/instagram/InstagramMultiAccountSelector";

interface InstagramPostFormProps {
  formData: IInstagramCreatePost;
  onFormChange: (data: IInstagramCreatePost) => void;
  uploadedImages: any[];
  onImageUpload: () => void;
  onGalleryOpen: () => void;
  onImageRemove: (index: number) => void;
  errors?: any;
  instagramAccounts: IInstagramAccountData[];
  loading?: boolean;
  selectedAccounts?: IInstagramSelectedAccount[];
  onSelectedAccountsChange?: (accounts: IInstagramSelectedAccount[]) => void;
  onSubmit?: () => void;
  isInstagramConnected?: boolean;
}

const InstagramPostForm: React.FC<InstagramPostFormProps> = ({
  formData,
  onFormChange,
  uploadedImages,
  onImageUpload,
  onGalleryOpen,
  onImageRemove,
  errors,
  instagramAccounts,
  loading = false,
  selectedAccounts = [],
  onSelectedAccountsChange,
  onSubmit,
  isInstagramConnected = true,
}) => {
  const [scheduleForLater, setScheduleForLater] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Dayjs | null>(
    dayjs().add(1, "hour")
  );

  const handleMultiAccountsChange = (accounts: IInstagramSelectedAccount[]) => {
    if (onSelectedAccountsChange) {
      onSelectedAccountsChange(accounts);
    }
    // Update accountIds in formData for compatibility
    onFormChange({
      ...formData,
      accountIds: accounts.map((a) => a.accountId),
    });
  };

  const handleInputChange = (field: keyof IInstagramCreatePost, value: any) => {
    onFormChange({
      ...formData,
      [field]: value,
    });
  };

  const handleDateChange = (newDate: Dayjs | null) => {
    setScheduledDate(newDate);
    if (newDate) {
      handleInputChange("scheduledPublishTime", newDate.toISOString());
    } else {
      handleInputChange("scheduledPublishTime", undefined);
    }
  };

  const handleScheduleToggle = (checked: boolean) => {
    setScheduleForLater(checked);
    handleInputChange("published", !checked);
    if (!checked) {
      handleInputChange("scheduledPublishTime", undefined);
    } else if (scheduledDate) {
      handleInputChange("scheduledPublishTime", scheduledDate.toISOString());
    }
  };

  // Set media URL from uploaded images
  useEffect(() => {
    if (uploadedImages.length > 0) {
      const firstImage = uploadedImages[0];
      let mediaUrl = "";

      if ((firstImage as any).s3Url) {
        mediaUrl = (firstImage as any).s3Url;
      } else if (firstImage instanceof File) {
        mediaUrl = URL.createObjectURL(firstImage);
      }

      if (mediaUrl && mediaUrl !== formData.mediaUrl) {
        handleInputChange("mediaUrl", mediaUrl);
        // Determine media type based on file type
        const mediaType = firstImage.type?.startsWith("video/")
          ? "video"
          : "image";
        handleInputChange("mediaType", mediaType);
      }
    } else if (formData.mediaUrl) {
      handleInputChange("mediaUrl", "");
      handleInputChange("mediaType", "");
    }
  }, [uploadedImages]);

  return (
    <Card elevation={3} sx={{ borderRadius: 2 }}>
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
          Create Instagram Post
        </Typography>

        <Grid container spacing={3}>
          {/* Instagram Account Selection */}
          <Grid item xs={12}>
            <InstagramMultiAccountSelector
              accounts={instagramAccounts}
              selectedAccounts={selectedAccounts}
              onAccountsChange={handleMultiAccountsChange}
              error={errors?.accountId || errors?.accountIds}
              loading={loading}
            />
          </Grid>

          {/* Caption */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Caption *"
              placeholder="Write your Instagram caption here... Use {Account Name} for dynamic replacement in multi-account posts."
              value={formData.caption}
              onChange={(e) => handleInputChange("caption", e.target.value)}
              variant="filled"
              sx={FILLED_INPUT_STYLES}
              error={!!errors?.caption}
              helperText={
                errors?.caption ||
                (selectedAccounts.length > 1
                  ? "Use {Account Name} placeholder for dynamic account names"
                  : "")
              }
            />
          </Grid>

          {/* Media Upload */}
          <Grid item xs={12}>
            <Box>
              <Box
                onClick={onGalleryOpen}
                sx={{
                  border: "2px dashed #e0e0e0",
                  borderRadius: 2,
                  p: 4,
                  textAlign: "center",
                  cursor: "pointer",
                  backgroundColor: "#fafafa",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    borderColor: "#1976d2",
                    backgroundColor: "#f5f5f5",
                  },
                }}
              >
                <ImageOutlined
                  sx={{
                    fontSize: 48,
                    color: "#9e9e9e",
                    mb: 1,
                  }}
                />
                <Typography variant="h6" sx={{ mb: 0.5, color: "#666" }}>
                  Add/Edit Post Media
                </Typography>
                <Typography variant="caption" sx={{ color: "#999" }}>
                  Supported: Images (JPG, PNG, GIF, WebP) and Videos (MP4, AVI,
                  MOV, WMV, FLV, WebM)
                </Typography>
              </Box>

              {uploadedImages.length === 0 && (
                <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                  Instagram posts require at least one image or video
                </Typography>
              )}
            </Box>
          </Grid>

          {/* Schedule Toggle */}
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={scheduleForLater}
                  onChange={(e) => handleScheduleToggle(e.target.checked)}
                />
              }
              label="Schedule for later"
            />
          </Grid>

          {/* Date/Time Picker */}
          {scheduleForLater && (
            <Grid item xs={12}>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DateTimePicker
                  label="Schedule Date & Time"
                  value={scheduledDate}
                  onChange={handleDateChange}
                  minDateTime={dayjs()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      variant: "filled",
                      sx: {
                        backgroundColor: "var(--whiteColor)",
                        borderRadius: "5px",
                      },
                    },
                  }}
                />
              </LocalizationProvider>
            </Grid>
          )}

          {/* Submit Button */}
          {onSubmit && isInstagramConnected && (
            <Grid item xs={12}>
              <Box sx={{ mt: 2 }}>
                <Button
                  className="updatesShapeBtn"
                  onClick={onSubmit}
                  variant="contained"
                  style={{ textTransform: "capitalize" }}
                  fullWidth
                  disabled={!selectedAccounts || selectedAccounts.length === 0}
                >
                  {selectedAccounts && selectedAccounts.length > 1
                    ? `Create Posts for ${selectedAccounts.length} Accounts`
                    : "Create Instagram Post"}
                </Button>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};

export default InstagramPostForm;
