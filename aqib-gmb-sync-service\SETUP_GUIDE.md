# GMB Auto Reply Service - Setup Guide

## Quick Start

### 1. Prerequisites Check
- ✅ Windows operating system
- ✅ Node.js 16.0.0 or higher installed
- ✅ Administrator privileges
- ✅ Access to MySQL database (configured in backend project)

### 2. Installation Steps

1. **Navigate to the service directory**:
   ```bash
   cd node-sync-service
   ```

2. **Install dependencies** (already done):
   ```bash
   npm install
   ```

3. **Configure environment variables**:
   - Edit `.env` file with your database credentials
   - The default configuration uses the same database as your backend project

4. **Test the service**:
   ```bash
   node test-service.js
   ```

5. **Install as Windows service** (run as Administrator):
   ```bash
   npm run install-service
   ```

6. **Start the service**:
   ```bash
   sc start "GMB-AutoReply-Service"
   ```

### 3. Configuration Requirements

#### Database Setup
The service uses the existing database from your backend project. Ensure these tables exist:

- `auto_reply_settings` - Business auto-reply configurations
- `reply_templates` - Reply templates for different star ratings  
- `business_reply_templates` - Template-to-business mappings
- `gmb_reviews` - Google My Business reviews
- `auto_reply_log` - Auto-reply processing log (created automatically)

#### Auto Reply Settings
Configure auto-reply settings through your main application's web interface:

1. **Enable Auto Reply** for businesses
2. **Set Star Rating Filters** (which ratings to auto-reply to)
3. **Configure Delay** (minimum time before replying)
4. **Set Business Hours** (optional restriction)
5. **Create Reply Templates** for different star ratings

#### Reply Templates
Create templates in your main application with placeholders:

- `{reviewer_name}` - Name of the reviewer
- `{business_name}` - Name of the business/location
- `{star_rating}` - Star rating (1-5)
- `{current_date}` - Current date
- `{review_text}` - Original review text

Example template:
```
Thank you {reviewer_name} for your {star_rating}-star review! We appreciate your feedback about {business_name}. We're always working to improve our service.
```

### 4. Service Management

#### Start/Stop Service
```bash
# Start service
sc start "GMB-AutoReply-Service"

# Stop service
sc stop "GMB-AutoReply-Service"

# Check status
sc query "GMB-AutoReply-Service"
```

#### View Logs
Logs are stored in `./logs/` directory:
- `combined.log` - All log entries
- `error.log` - Error-level entries only

#### Uninstall Service
```bash
npm run uninstall-service
```

### 5. Scheduling Configuration

The service runs on a configurable schedule (default: every 5 minutes).

To change the schedule, edit `.env` file:
```env
CRON_SCHEDULE=*/10 * * * *  # Every 10 minutes
CRON_SCHEDULE=0 */1 * * *   # Every hour
CRON_SCHEDULE=0 9-17 * * *  # Every hour from 9 AM to 5 PM
```

### 6. Monitoring

#### Health Checks
The service performs automatic health checks every hour and logs:
- Service status and uptime
- Processing statistics
- Memory usage
- Database connection status

#### Processing Statistics
Monitor the logs for:
- Number of businesses processed
- Number of reviews processed
- Success/failure rates
- Error messages

### 7. Troubleshooting

#### Common Issues

1. **Service won't start**:
   - Run as Administrator
   - Check database connection in `.env`
   - Review logs for specific errors

2. **No reviews being processed**:
   - Verify auto-reply is enabled for businesses
   - Check star rating filters
   - Ensure reply templates exist
   - Verify delay settings

3. **Database connection errors**:
   - Check database credentials
   - Verify database server accessibility
   - Check firewall settings

#### Debug Mode
Run in development mode for detailed logging:
```bash
npm run dev
```

### 8. Production Deployment

#### Security Checklist
- ✅ Secure database credentials
- ✅ Run with minimal privileges
- ✅ Monitor logs regularly
- ✅ Keep dependencies updated

#### Performance Tuning
Adjust these settings in `.env` for your environment:
```env
BATCH_SIZE=50              # Reviews processed per batch
MAX_RETRY_ATTEMPTS=3       # Retry failed attempts
RETRY_DELAY_MINUTES=10     # Delay between retries
```

### 9. Integration with Main Application

The service integrates seamlessly with your existing GMB application:

1. **Uses same database** - No data duplication
2. **Respects user settings** - Honors auto-reply configurations
3. **Uses existing templates** - No separate template management
4. **Logs all activity** - Full audit trail

### 10. Support

For issues:
1. Check service logs in `./logs/`
2. Run test script: `node test-service.js`
3. Verify database connectivity
4. Review auto-reply settings in main application

The service is designed to work autonomously once configured, requiring minimal maintenance.
