const Service = require("node-windows").Service;
const path = require("path");

// Create a simple test service
const svc = new Service({
  name: "GMBAutoReplyService",
  description: "GMB Auto Reply Service",
  script: path.join(__dirname, "app.js"),
  execPath: "C:\\Users\\<USER>\\AppData\\Local\\nvm\\v20.9.0\\node.exe",
  nodeOptions: ["--max_old_space_size=4096"],
  workingDirectory: __dirname,
  allowServiceLogon: true,
});

svc.on("install", function () {
  console.log("Service installed successfully");
  console.log("Starting service...");
  svc.start();
});

svc.on("alreadyinstalled", function () {
  console.log("Service already exists");
});

svc.on("start", function () {
  console.log("Service started successfully");
});

svc.on("error", function (err) {
  console.error("Service error:", err);
});

console.log("Installing service...");
svc.install();
