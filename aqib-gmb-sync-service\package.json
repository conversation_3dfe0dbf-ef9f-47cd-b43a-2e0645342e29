{"name": "gmb-auto-reply-service", "version": "1.1.0", "description": "Windows service for automated Google My Business review replies, review synchronization, and analytics data synchronization", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "install-service": "node install-service.js", "uninstall-service": "node uninstall-service.js", "test": "node test-service.js", "test-review-sync": "node test-review-sync.js", "test-analytics-sync": "node test-analytics-sync.js", "status": "node status.js", "status-watch": "node status.js --watch"}, "keywords": ["windows-service", "auto-reply", "google-my-business", "reviews", "review-sync", "analytics-sync", "scheduling", "synchronization", "performance-metrics"], "author": "GMB Social Team", "license": "MIT", "dependencies": {"node-windows": "^1.0.0-beta.8", "node-cron": "^3.0.3", "mysql2": "^3.10.0", "dotenv": "^16.4.5", "moment": "^2.30.1", "winston": "^3.11.0", "axios": "^1.7.2"}, "devDependencies": {"nodemon": "^3.1.3"}, "engines": {"node": ">=16.0.0"}}