const pool = require("../config/db");

module.exports = class LocationAnalytics {
  constructor() {}

  /**
   * Insert or update daily analytics metrics for a location
   * @param {Object} metricsData - Object containing metrics data
   * @param {string} metricsData.gmbLocationId - GMB Location ID
   * @param {string} metricsData.gmbAccountId - GMB Account ID
   * @param {string} metricsData.metricDate - Date in YYYY-MM-DD format
   * @param {string} metricsData.metricType - Type of metric (e.g., WEBSITE_CLICKS)
   * @param {number} metricsData.metricValue - Metric value
   */
  static async insertOrUpdateDailyMetric(metricsData) {
    try {
      const result = await pool.query(
        `INSERT INTO location_analytics_daily
         (gmb_location_id, gmb_account_id, metric_date, metric_type, metric_value)
         VALUES (?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         metric_value = VALUES(metric_value),
         updated_at = CURRENT_TIMESTAMP`,
        [
          metricsData.gmbLocationId,
          metricsData.gmbAccountId,
          metricsData.metricDate,
          metricsData.metricType,
          metricsData.metricValue,
        ]
      );
      return result;
    } catch (error) {
      console.error("Error inserting/updating daily metric:", error);
      throw error;
    }
  }

  /**
   * Batch insert or update multiple daily metrics
   * @param {Array} metricsArray - Array of metrics data objects
   */
  static async batchInsertDailyMetrics(metricsArray) {
    if (!metricsArray || metricsArray.length === 0) {
      return { affectedRows: 0 };
    }

    try {
      const values = metricsArray.map((metric) => [
        metric.gmbLocationId,
        metric.gmbAccountId,
        metric.metricDate,
        metric.metricType,
        metric.metricValue,
      ]);

      const placeholders = values.map(() => "(?, ?, ?, ?, ?)").join(", ");
      const flatValues = values.flat();

      const result = await pool.query(
        `INSERT INTO location_analytics_daily
         (gmb_location_id, gmb_account_id, metric_date, metric_type, metric_value)
         VALUES ${placeholders}
         ON DUPLICATE KEY UPDATE
         metric_value = VALUES(metric_value),
         updated_at = CURRENT_TIMESTAMP`,
        flatValues
      );
      return result;
    } catch (error) {
      console.error("Error batch inserting daily metrics:", error);
      throw error;
    }
  }

  /**
   * Get analytics data for a location within a date range
   * @param {string} gmbLocationId - GMB Location ID
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} endDate - End date in YYYY-MM-DD format
   * @param {Array} metricTypes - Array of metric types to retrieve (optional)
   */
  static async getLocationAnalytics(
    gmbLocationId,
    startDate,
    endDate,
    metricTypes = null
  ) {
    try {
      let query = `
        SELECT metric_date, metric_type, metric_value
        FROM location_analytics_daily
        WHERE gmb_location_id = ?
        AND metric_date >= ?
        AND metric_date <= ?
      `;

      const params = [gmbLocationId, startDate, endDate];

      if (metricTypes && metricTypes.length > 0) {
        const placeholders = metricTypes.map(() => "?").join(", ");
        query += ` AND metric_type IN (${placeholders})`;
        params.push(...metricTypes);
      }

      query += ` ORDER BY metric_date ASC, metric_type ASC`;

      const results = await pool.query(query, params);
      return results;
    } catch (error) {
      console.error("Error fetching location analytics:", error);
      throw error;
    }
  }

  /**
   * Get aggregated analytics data for multiple locations
   * @param {Array} gmbLocationIds - Array of GMB Location IDs
   * @param {string} startDate - Start date in YYYY-MM-DD format
   * @param {string} endDate - End date in YYYY-MM-DD format
   * @param {Array} metricTypes - Array of metric types to retrieve (optional)
   */
  static async getMultiLocationAnalytics(
    gmbLocationIds,
    startDate,
    endDate,
    metricTypes = null
  ) {
    if (!gmbLocationIds || gmbLocationIds.length === 0) {
      return [];
    }

    try {
      let query = `
        SELECT gmb_location_id, metric_date, metric_type, metric_value
        FROM location_analytics_daily
        WHERE metric_date >= ?
        AND metric_date <= ?
      `;

      const params = [startDate, endDate];

      // Add location filter
      const locationPlaceholders = gmbLocationIds.map(() => "?").join(", ");
      query += ` AND gmb_location_id IN (${locationPlaceholders})`;
      params.push(...gmbLocationIds);

      if (metricTypes && metricTypes.length > 0) {
        const metricPlaceholders = metricTypes.map(() => "?").join(", ");
        query += ` AND metric_type IN (${metricPlaceholders})`;
        params.push(...metricTypes);
      }

      query += ` ORDER BY gmb_location_id ASC, metric_date ASC, metric_type ASC`;

      const results = await pool.query(query, params);
      return results;
    } catch (error) {
      console.error("Error fetching multi-location analytics:", error);
      throw error;
    }
  }

  /**
   * Log sync operation
   * @param {Object} syncData - Sync operation data
   */
  static async logSyncOperation(syncData) {
    try {
      const result = await pool.query(
        `INSERT INTO location_analytics_sync_log
         (gmb_location_id, gmb_account_id, sync_date, start_date, end_date,
          status, metrics_synced, error_message, api_calls_made, sync_duration_seconds)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          syncData.gmbLocationId,
          syncData.gmbAccountId,
          syncData.syncDate,
          syncData.startDate,
          syncData.endDate,
          syncData.status,
          syncData.metricsSynced || 0,
          syncData.errorMessage || null,
          syncData.apiCallsMade || 0,
          syncData.syncDurationSeconds || 0,
        ]
      );
      return result;
    } catch (error) {
      console.error("Error logging sync operation:", error);
      throw error;
    }
  }

  /**
   * Update sync log status
   * @param {number} syncLogId - Sync log ID
   * @param {Object} updateData - Data to update
   */
  static async updateSyncLog(syncLogId, updateData) {
    try {
      const setParts = [];
      const values = [];

      if (updateData.status !== undefined) {
        setParts.push("status = ?");
        values.push(updateData.status);
      }
      if (updateData.metricsSynced !== undefined) {
        setParts.push("metrics_synced = ?");
        values.push(updateData.metricsSynced);
      }
      if (updateData.errorMessage !== undefined) {
        setParts.push("error_message = ?");
        values.push(updateData.errorMessage);
      }
      if (updateData.syncDurationSeconds !== undefined) {
        setParts.push("sync_duration_seconds = ?");
        values.push(updateData.syncDurationSeconds);
      }

      if (setParts.length === 0) {
        return { affectedRows: 0 };
      }

      setParts.push("updated_at = CURRENT_TIMESTAMP");
      values.push(syncLogId);

      const result = await pool.query(
        `UPDATE location_analytics_sync_log SET ${setParts.join(
          ", "
        )} WHERE id = ?`,
        values
      );
      return result;
    } catch (error) {
      console.error("Error updating sync log:", error);
      throw error;
    }
  }

  /**
   * Get locations that need sync
   * @param {string} syncDate - Date to sync for (YYYY-MM-DD format)
   */
  static async getLocationsThatNeedSync(syncDate) {
    try {
      const result = await pool.query(
        `SELECT DISTINCT
           gl.gmbLocationId,
           gl.gmbAccountId,
           lac.last_sync_date,
           lac.sync_frequency_days
         FROM gmb_locations gl
         LEFT JOIN location_analytics_config lac ON gl.gmbLocationId = lac.gmb_location_id
         LEFT JOIN gmb_oauth_tokens got ON gl.gmbAccountId = got.gmbAccountId
         WHERE gl.statusId = 1
         AND got.statusId = 1
         AND (lac.sync_enabled IS NULL OR lac.sync_enabled = 1)
         AND (
           lac.last_sync_date IS NULL
           OR lac.last_sync_date < DATE_SUB(?, INTERVAL COALESCE(lac.sync_frequency_days, 1) DAY)
         )`,
        [syncDate]
      );
      return result;
    } catch (error) {
      console.error("Error getting locations that need sync:", error);
      throw error;
    }
  }

  /**
   * Update last sync date for a location
   * @param {string} gmbLocationId - GMB Location ID
   * @param {string} syncDate - Sync date (YYYY-MM-DD format)
   */
  static async updateLastSyncDate(gmbLocationId, syncDate) {
    try {
      const result = await pool.query(
        `INSERT INTO location_analytics_config
         (gmb_location_id, gmb_account_id, last_sync_date, sync_enabled)
         SELECT ?, gmbAccountId, ?, 1 FROM gmb_locations WHERE gmbLocationId = ?
         ON DUPLICATE KEY UPDATE
         last_sync_date = VALUES(last_sync_date),
         updated_at = CURRENT_TIMESTAMP`,
        [gmbLocationId, syncDate, gmbLocationId]
      );
      return result;
    } catch (error) {
      console.error("Error updating last sync date:", error);
      throw error;
    }
  }

  /**
   * Clean up old analytics data based on retention policy
   * @param {number} retentionDays - Number of days to retain data
   */
  static async cleanupOldData(retentionDays = 365) {
    try {
      const result = await pool.query(
        `DELETE FROM location_analytics_daily
         WHERE metric_date < DATE_SUB(CURDATE(), INTERVAL ? DAY)`,
        [retentionDays]
      );
      return result;
    } catch (error) {
      console.error("Error cleaning up old analytics data:", error);
      throw error;
    }
  }

  /**
   * Get analytics data in the format expected by the frontend
   * This method transforms database data to match the Google API response structure
   */
  static async getFormattedAnalyticsData(gmbLocationIds, startDate, endDate) {
    try {
      const rawData = await this.getMultiLocationAnalytics(
        gmbLocationIds,
        startDate,
        endDate
      );

      // Group data by location
      const locationDataMap = {};

      rawData.forEach((row) => {
        const locationId = row.gmb_location_id;
        if (!locationDataMap[locationId]) {
          locationDataMap[locationId] = {
            multiDailyMetricTimeSeries: [
              {
                dailyMetricTimeSeries: [],
              },
            ],
          };
        }

        // Find or create metric series for this metric type
        let metricSeries = locationDataMap[
          locationId
        ].multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.find(
          (series) => series.dailyMetric === row.metric_type
        );

        if (!metricSeries) {
          metricSeries = {
            dailyMetric: row.metric_type,
            timeSeries: {
              datedValues: [],
            },
          };
          locationDataMap[
            locationId
          ].multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.push(
            metricSeries
          );
        }

        // Add the dated value
        const date = new Date(row.metric_date);
        metricSeries.timeSeries.datedValues.push({
          date: {
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
          },
          value: row.metric_value.toString(),
        });
      });

      // Sort dated values by date for each metric series
      Object.values(locationDataMap).forEach((locationData) => {
        locationData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.forEach(
          (series) => {
            series.timeSeries.datedValues.sort((a, b) => {
              const dateA = new Date(a.date.year, a.date.month - 1, a.date.day);
              const dateB = new Date(b.date.year, b.date.month - 1, b.date.day);
              return dateA - dateB;
            });
          }
        );
      });

      return locationDataMap;
    } catch (error) {
      console.error("Error formatting analytics data:", error);
      throw error;
    }
  }
};
