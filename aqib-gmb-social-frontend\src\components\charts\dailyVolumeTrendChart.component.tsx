import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { Line } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface IDailyVolumeTrendChartProps {
  data: Record<string, { reviews: number; replies: number }>;
  title?: string;
}

const DailyVolumeTrendChart: React.FC<IDailyVolumeTrendChartProps> = ({
  data,
  title = "Daily Reviews & Replies Volume",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const dates = Object.keys(data).sort();
  const reviewsData = dates.map((date) => data[date]?.reviews || 0);
  const repliesData = dates.map((date) => data[date]?.replies || 0);

  const chartData = {
    labels: dates.map((date) => new Date(date).toLocaleDateString()),
    datasets: [
      {
        label: "Reviews",
        data: reviewsData,
        borderColor: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.main + "20",
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
      {
        label: "Replies",
        data: repliesData,
        borderColor: theme.palette.secondary.main,
        backgroundColor: theme.palette.secondary.main + "20",
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
      },
      tooltip: {
        mode: "index" as const,
        intersect: false,
        callbacks: {
          title: function (context: any) {
            return `Date: ${context[0].label}`;
          },
          label: function (context: any) {
            return `${context.dataset.label}: ${context.parsed.y}`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Date",
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Count",
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: "nearest" as const,
      axis: "x" as const,
      intersect: false,
    },
  };

  // Calculate summary statistics
  const totalReviews = reviewsData.reduce((sum, count) => sum + count, 0);
  const totalReplies = repliesData.reduce((sum, count) => sum + count, 0);
  const avgDailyReviews = dates.length > 0 ? totalReviews / dates.length : 0;
  const avgDailyReplies = dates.length > 0 ? totalReplies / dates.length : 0;
  const overallResponseRate = totalReviews > 0 ? (totalReplies / totalReviews) * 100 : 0;

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 4, mb: 2, flexWrap: "wrap" }}>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total Replies: <strong>{totalReplies}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Avg Daily Reviews: <strong>{avgDailyReviews.toFixed(1)}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Avg Daily Replies: <strong>{avgDailyReplies.toFixed(1)}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Response Rate: <strong>{overallResponseRate.toFixed(1)}%</strong>
          </Typography>
        </Box>
      </Box>

      {dates.length > 0 ? (
        <Line data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected period
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default DailyVolumeTrendChart;
