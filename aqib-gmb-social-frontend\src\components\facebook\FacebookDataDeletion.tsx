import React from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  <PERSON><PERSON>,
  <PERSON>,
  Card,
  CardContent,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import SecurityIcon from "@mui/icons-material/Security";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import InfoIcon from "@mui/icons-material/Info";
import WarningIcon from "@mui/icons-material/Warning";

const FacebookDataDeletion: React.FC = () => {
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: "center", mb: 4 }}>
          <SecurityIcon sx={{ fontSize: 48, color: "primary.main", mb: 2 }} />
          <Typography variant="h3" component="h1" gutterBottom>
            Facebook Data Deletion Instructions
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            How to request deletion of your data from our application
          </Typography>
        </Box>

        {/* Important Notice */}
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="body2">
            <strong>Important:</strong> This page provides instructions for users who want to delete their data that was collected through our Facebook integration. We respect your privacy and are committed to helping you manage your personal information.
          </Typography>
        </Alert>

        {/* What Data We Collect */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" component="h2" gutterBottom sx={{ display: "flex", alignItems: "center" }}>
              <InfoIcon sx={{ mr: 1 }} />
              What Data We Collect
            </Typography>
            <Typography variant="body1" paragraph>
              When you connect your Facebook account to our application, we may collect and store the following information:
            </Typography>
            <List>
              <ListItem>
                <ListItemText primary="Basic profile information (name, email)" />
              </ListItem>
              <ListItem>
                <ListItemText primary="Facebook Page information you manage" />
              </ListItem>
              <ListItem>
                <ListItemText primary="Posts and content you create through our platform" />
              </ListItem>
              <ListItem>
                <ListItemText primary="Access tokens for Facebook API integration" />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Data Deletion Process */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" component="h2" gutterBottom sx={{ display: "flex", alignItems: "center" }}>
              <DeleteIcon sx={{ mr: 1 }} />
              How to Request Data Deletion
            </Typography>
            <Typography variant="body1" paragraph>
              To request deletion of your data from our system, please follow these steps:
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>1.</Typography>
                </ListItemIcon>
                <ListItemText 
                  primary="Contact our support team"
                  secondary="Send an email to our data protection team with your deletion request"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>2.</Typography>
                </ListItemIcon>
                <ListItemText 
                  primary="Provide identification"
                  secondary="Include your email address and Facebook account information to help us locate your data"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>3.</Typography>
                </ListItemIcon>
                <ListItemText 
                  primary="Specify deletion scope"
                  secondary="Let us know if you want to delete all data or specific information"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>4.</Typography>
                </ListItemIcon>
                <ListItemText 
                  primary="Confirmation"
                  secondary="We will confirm your identity and process your request within 30 days"
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" component="h2" gutterBottom sx={{ display: "flex", alignItems: "center" }}>
              <ContactMailIcon sx={{ mr: 1 }} />
              Contact Information
            </Typography>
            <Typography variant="body1" paragraph>
              For data deletion requests or privacy-related inquiries, please contact us:
            </Typography>
            <Box sx={{ pl: 2 }}>
              <Typography variant="body1" paragraph>
                <strong>Email:</strong>{" "}
                <Link href="mailto:<EMAIL>" color="primary">
                  <EMAIL>
                </Link>
              </Typography>
              <Typography variant="body1" paragraph>
                <strong>Subject Line:</strong> Facebook Data Deletion Request
              </Typography>
              <Typography variant="body1" paragraph>
                <strong>Response Time:</strong> We will respond to your request within 30 days
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Important Notes */}
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="body2">
            <strong>Please Note:</strong>
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon>
                <WarningIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Data deletion is permanent and cannot be undone" />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <WarningIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="You will need to reconnect your Facebook account if you wish to use our services again" />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <WarningIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Some data may be retained for legal or security purposes as required by law" />
            </ListItem>
          </List>
        </Alert>

        {/* Footer */}
        <Divider sx={{ my: 3 }} />
        <Box sx={{ textAlign: "center" }}>
          <Typography variant="body2" color="text.secondary">
            This page complies with Facebook's Platform Policy requirements for data deletion instructions.
            <br />
            Last updated: {new Date().toLocaleDateString()}
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default FacebookDataDeletion;
