import React, { useState } from "react";
import { Box } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import dayjs, { Dayjs } from "dayjs";
import { INPUT_FIELD_STYLES } from "../../constants/styles.constant";

interface IPerformanceDateFilterProps {
  onDateChange: (range: {
    fromDate: string;
    toDate: string;
    isValid: boolean;
  }) => void;
  dateType?: "from" | "to";
}

export interface IPerformanceDateRange {
  fromDate: string;
  toDate: string;
  isValid: boolean;
}

const PerformanceDateFilter: React.FC<IPerformanceDateFilterProps> = ({
  onDateChange,
  dateType,
}) => {
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);
  const [fromDateError, setFromDateError] = useState<string>("");
  const [toDateError, setToDateError] = useState<string>("");

  const validateDates = (from: Dayjs | null, to: Dayjs | null) => {
    let fromError = "";
    let toError = "";
    let isValid = true;

    const today = dayjs().startOf("day");

    // Validate from date
    if (from) {
      if (from.isAfter(today)) {
        fromError = "From date cannot be in the future";
        isValid = false;
      }
    }

    // Validate to date
    if (to) {
      if (to.isAfter(today)) {
        toError = "To date cannot be in the future";
        isValid = false;
      }
    }

    // Validate date range
    if (from && to) {
      if (from.isAfter(to) || from.isSame(to)) {
        toError = "To date must be greater than From date";
        isValid = false;
      }
    }

    setFromDateError(fromError);
    setToDateError(toError);

    // Call parent callback
    onDateChange({
      fromDate: from ? from.format("YYYY-MM-DD") : "",
      toDate: to ? to.format("YYYY-MM-DD") : "",
      isValid: isValid && from !== null && to !== null,
    });

    return isValid;
  };

  const handleFromDateChange = (newValue: Dayjs | null) => {
    setFromDate(newValue);
    validateDates(newValue, toDate);
  };

  const handleToDateChange = (newValue: Dayjs | null) => {
    setToDate(newValue);
    validateDates(fromDate, newValue);
  };

  // If dateType is specified, show only that date picker
  if (dateType === "from") {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="From Date"
          value={fromDate}
          onChange={handleFromDateChange}
          maxDate={dayjs()}
          slotProps={{
            textField: {
              fullWidth: true,
              required: true,
              error: Boolean(fromDateError),
              helperText: fromDateError,
              variant: "outlined",
              sx: INPUT_FIELD_STYLES,
            },
          }}
        />
      </LocalizationProvider>
    );
  }

  if (dateType === "to") {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="To Date"
          value={toDate}
          onChange={handleToDateChange}
          minDate={fromDate || undefined}
          maxDate={dayjs()}
          slotProps={{
            textField: {
              fullWidth: true,
              required: true,
              error: Boolean(toDateError),
              helperText: toDateError,
              variant: "outlined",
              sx: INPUT_FIELD_STYLES,
            },
          }}
        />
      </LocalizationProvider>
    );
  }

  // Default: show both date pickers inline
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ display: "flex", gap: 2, flexDirection: "row" }}>
        <DatePicker
          label="From Date *"
          value={fromDate}
          onChange={handleFromDateChange}
          maxDate={dayjs()}
          slotProps={{
            textField: {
              required: true,
              error: Boolean(fromDateError),
              helperText: fromDateError,
              variant: "outlined",
              sx: { width: "180px", ...INPUT_FIELD_STYLES },
            },
          }}
        />

        <DatePicker
          label="To Date *"
          value={toDate}
          onChange={handleToDateChange}
          minDate={fromDate || undefined}
          maxDate={dayjs()}
          slotProps={{
            textField: {
              required: true,
              error: Boolean(toDateError),
              helperText: toDateError,
              variant: "outlined",
              sx: { width: "180px", ...INPUT_FIELD_STYLES },
            },
          }}
        />
      </Box>
    </LocalizationProvider>
  );
};

export default PerformanceDateFilter;
