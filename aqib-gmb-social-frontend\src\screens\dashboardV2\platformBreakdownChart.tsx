import React, { useEffect, useState } from "react";
import { Doughnut } from "react-chartjs-2";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Divider, Typography, Box } from "@mui/material";
import { theme } from "../../theme";
import ExportButton from "../../components/exportButton/exportButton.component";
import { ChartExportData } from "../../services/excelExport.service";
import { ILocation } from "../../interfaces/response/ILocationsListResponseModel";

import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Legend,
  ArcElement,
  ChartOptions,
} from "chart.js";

ChartJS.register(
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  ArcElement,
  Tooltip,
  Legend
);

interface DatedValue {
  date: {
    year: number;
    month: number;
    day: number;
  };
  value?: string;
}

interface TimeSeries {
  datedValues: DatedValue[];
}

interface DailyMetricTimeSeries {
  dailyMetric: string;
  timeSeries: TimeSeries;
}

interface MultiDailyMetricTimeSeries {
  dailyMetricTimeSeries: DailyMetricTimeSeries[];
}

interface RootObject {
  multiDailyMetricTimeSeries: MultiDailyMetricTimeSeries[];
}

interface PlatformDataInput {
  label: string;
  value: number;
}

interface Props {
  data: RootObject;
  // Export functionality props (only visible in analytics)
  showExport?: boolean;
  selectedLocationIds?: string[];
  availableLocations?: ILocation[];
  locationDataMap?: Record<string, any>;
  daysDifference?: number;
  isSameMonthYear?: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
}

const PlatformBreakdownChart: React.FC<Props> = ({
  data,
  showExport = false,
  selectedLocationIds = [],
  availableLocations = [],
  locationDataMap = {},
  daysDifference = 30,
  isSameMonthYear = false,
  dateRange,
}) => {
  const [platformData, setPlatformData] = useState<PlatformDataInput[]>([]);

  useEffect(() => {
    if (
      data &&
      data.multiDailyMetricTimeSeries &&
      data.multiDailyMetricTimeSeries[0]
    ) {
      const series = data.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries;
      const result = series
        .filter((x) => x.dailyMetric.includes("BUSINESS_IMPRESSIONS"))
        .map((metric) => {
          const label = metric.dailyMetric
            .replace("BUSINESS_IMPRESSIONS_", "")
            .replace(/_/g, " ")
            .toLowerCase()
            .replace(/(^\w|\s\w)/g, (m) => m.toUpperCase());

          const value =
            metric.timeSeries.datedValues &&
            metric.timeSeries.datedValues.reduce((acc, curr) => {
              return acc + (curr.value ? parseInt(curr.value) : 0);
            }, 0);

          return { label, value };
        });

      setPlatformData(result);
    } else {
      setPlatformData([]);
    }
  }, [data]);

  const total = platformData.reduce((sum, entry) => sum + entry.value, 0);

  // Prepare export data
  const exportData: ChartExportData = {
    chartTitle: "Platform Breakdown",
    data: {
      data: platformData.map((entry) => entry.value),
      labels: platformData.map((entry) => entry.label),
    },
    selectedLocationIds,
    availableLocations,
    locationDataMap,
    metricType: "BUSINESS_IMPRESSIONS",
    daysDifference,
    isSameMonthYear,
    dateRange,
  };

  const chartData = {
    labels: platformData.map(
      (entry) => `${entry.label} (${Math.round((entry.value / total) * 100)}%)`
    ),
    datasets: [
      {
        data: platformData.map((entry) => entry.value),
        backgroundColor: [
          theme.palette.primary?.main,
          "#66bb6a",
          "#ffa726",
          theme.palette.secondary?.main,
        ],
      },
    ],
  };

  const options: ChartOptions<"doughnut"> = {
    plugins: {
      legend: {
        position: "right",
        labels: {
          padding: 20,
        },
      },
      datalabels: {
        color: "#fff",
        formatter: (value: number, context: any) => {
          // const total = context.chart.data.datasets[0].data.reduce(
          //   (sum: number, val: number) => sum + val,
          //   0
          // );

          return `${value}`;
        },
        font: {
          weight: "lighter",
          size: 12,
        },
      },
    },
    responsive: true,
    maintainAspectRatio: false,
  };

  return (
    <div style={{ width: "100%", height: "100%" }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "flex-start",
          mb: 1,
        }}
      >
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          Platform Breakdown
        </Typography>
        {showExport && (
          <ExportButton
            chartData={exportData}
            size="small"
            tooltipText="Export Platform Breakdown data to Excel"
          />
        )}
      </Box>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        {total}
      </Typography>
      <Typography variant="subtitle2" sx={{ mb: 2 }}>
        People viewed your Business Profile
      </Typography>
      <div style={{ height: "300px", position: "relative" }}>
        <Doughnut
          data={chartData}
          options={options}
          plugins={[ChartDataLabels]}
        />
      </div>
    </div>
  );
};

export default PlatformBreakdownChart;
