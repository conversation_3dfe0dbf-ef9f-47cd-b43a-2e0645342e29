import { Dispatch, Action } from "redux";
import HttpHelperService from "../httpHelper.service";
import {
  LINKEDIN_AUTHENTICATE,
  LINKEDIN_CALLBACK_VALIDATION,
  LINKEDIN_GET_PAGES,
  LINKEDIN_CREATE_POST,
} from "../../constants/endPoints.constant";

interface ILinkedInAuthResponse {
  success: boolean;
  authUrl?: string;
  message: string;
}

interface ILinkedInCallbackResponse {
  success: boolean;
  message: string;
  data?: {
    userId: number;
    linkedinUserId: string;
    linkedinUserName: string;
    linkedinUserEmail: string;
  };
}

interface ILinkedInPagesResponse {
  success: boolean;
  pages?: ILinkedInPageData[];
  pagesCount?: number;
  message: string;
}

interface ILinkedInPageData {
  id: string;
  name: string;
  email: string;
  picture?: string;
  type: string;
}

interface ILinkedInCreatePost {
  profileId: string;
  text: string;
  media?: any[];
  published?: boolean;
  scheduledPublishTime?: string;
}

interface ILinkedInSelectedProfile {
  id: string;
  name: string;
  email: string;
  picture?: string;
  selected: boolean;
  status?: boolean | null;
  linkedinUrl?: string;
  error?: string;
}

interface ILinkedInCreatePostResponse {
  success: boolean;
  postId?: string;
  message: string;
  data?: any;
}

class LinkedInService {
  _httpHelperService: HttpHelperService | null;

  constructor(dispatch: Dispatch<Action> | null) {
    this._httpHelperService = dispatch ? new HttpHelperService(dispatch) : null;
  }

  /**
   * Initiate LinkedIn authentication
   * @param userId - User ID
   * @returns Authentication response with OAuth URL
   */
  authenticate = async (
    userId: number
  ): Promise<ILinkedInAuthResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.post(
        LINKEDIN_AUTHENTICATE,
        {
          userId,
        }
      );
      return response;
    } catch (error) {
      console.error("Error initiating LinkedIn authentication:", error);
      throw error;
    }
  };

  /**
   * Validate LinkedIn OAuth callback
   * @param code - Authorization code
   * @param state - State parameter
   * @returns Callback validation response
   */
  callbackValidation = async (
    code: string,
    state: string
  ): Promise<ILinkedInCallbackResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.post(
        LINKEDIN_CALLBACK_VALIDATION,
        {
          code,
          state,
        }
      );
      return response;
    } catch (error) {
      console.error("Error validating LinkedIn callback:", error);
      throw error;
    }
  };

  /**
   * Get LinkedIn pages/profiles for user
   * @param userId - User ID
   * @returns Pages response
   */
  getPages = async (userId: number): Promise<ILinkedInPagesResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.get(
        LINKEDIN_GET_PAGES(userId)
      );
      return response;
    } catch (error) {
      console.error("Error fetching LinkedIn pages:", error);
      throw error;
    }
  };

  /**
   * Create LinkedIn post
   * @param userId - User ID
   * @param postData - Post data
   * @returns Post creation response
   */
  createPost = async (
    userId: number,
    postData: ILinkedInCreatePost
  ): Promise<ILinkedInCreatePostResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.post(
        LINKEDIN_CREATE_POST(userId),
        postData
      );
      return response;
    } catch (error) {
      console.error("Error creating LinkedIn post:", error);
      throw error;
    }
  };

  /**
   * Create multiple LinkedIn posts
   * @param userId - User ID
   * @param postData - Post data with multiple profiles
   * @param selectedProfiles - Selected profiles
   * @returns Array of post creation responses
   */
  createMultiplePosts = async (
    userId: number,
    postData: Omit<ILinkedInCreatePost, "profileId">,
    selectedProfiles: ILinkedInSelectedProfile[]
  ): Promise<
    Array<{
      profileId: string;
      profileName: string;
      result: ILinkedInCreatePostResponse | null;
    }>
  > => {
    const results = [];

    for (const profile of selectedProfiles.filter((p) => p.selected)) {
      try {
        const result = await this.createPost(userId, {
          ...postData,
          profileId: profile.id,
        });

        results.push({
          profileId: profile.id,
          profileName: profile.name,
          result,
        });
      } catch (error) {
        console.error(
          `Error creating LinkedIn post for profile ${profile.id}:`,
          error
        );
        results.push({
          profileId: profile.id,
          profileName: profile.name,
          result: {
            success: false,
            message: `Failed to create post: ${error}`,
          },
        });
      }
    }

    return results;
  };
}

export default LinkedInService;
export type {
  ILinkedInAuthResponse,
  ILinkedInCallbackResponse,
  ILinkedInPagesResponse,
  ILinkedInPageData,
  ILinkedInCreatePost,
  ILinkedInSelectedProfile,
  ILinkedInCreatePostResponse,
};
