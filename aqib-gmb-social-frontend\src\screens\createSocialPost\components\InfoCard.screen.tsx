import React from "react";
import { Card, CardContent, Typography, Box } from "@mui/material";
import TipsAndUpdatesOutlinedIcon from "@mui/icons-material/TipsAndUpdatesOutlined";
import { theme } from "../../../theme";

const InfoCard = () => {
  const cards = [
    {
      title: "Adding Posts",
      description:
        "Frequency of Posts & content do impact your ranking & can drive upto 5% more impressions",
      key: 0,
    },
    {
      title: "Adding Offers",
      description:
        'Uploading posts as an offers gives more visibility as it comes under the "Deals" section on Google Search',
      key: 1,
    },
    {
      title: "Post Offers",
      description:
        "Recommended to add Offers as they have higher visibility on Profiles & drive more engagement",
      key: 2,
    },
    {
      title: "Measuring Post Clicks",
      description:
        "Google doesn't allow phone numbers & URLs in Post & Offer Descriptions. Read More",
      key: 3,
    },
    {
      title: "Reasons for Post Rejections",
      description:
        "Google doesn't allow phone numbers & URLs in Post & Offer Descriptions.",
      key: 4,
    },
    {
      title: "Competitor Comparison",
      description:
        "5 out of your top 10 Competitors have a Post in last 30 Days",
      key: 5,
    },
    {
      title: "Emojis In Title",
      description:
        "Using Emojis in the Post Title gives more engagement to your post/offer",
      key: 6,
    },
  ];

  const InfoCardTemplate = (props: {
    setKey: number;
    title: string;
    description: string;
  }) => {
    return (
      <Card
        key={props.setKey}
        elevation={1}
        sx={{
          borderRadius: 1.5,
          p: 1.5,
          mb: 1,
          display: "flex",
          alignItems: "flex-start",
          gap: 1,
          minHeight: "auto",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: 32,
            height: 20,
            borderRadius: "50%",
            backgroundColor: theme.palette.secondary?.light + "20",
            flexShrink: 0,
          }}
        >
          <TipsAndUpdatesOutlinedIcon
            sx={{
              color: theme.palette.secondary?.main,
              fontSize: 18,
            }}
          />
        </Box>
        <CardContent
          key={"Infocard" + props.setKey}
          sx={{ padding: 0, flex: 1 }}
          style={{ paddingBottom: 4 }}
        >
          <Typography
            key={"title" + props.setKey}
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              fontSize: "0.875rem",
              lineHeight: 1.3,
              mb: 0.5,
            }}
          >
            {props.title} {"1"}
          </Typography>
          <Typography
            key={"subtitle" + props.setKey}
            variant="caption"
            color="text.secondary"
            sx={{
              fontSize: "0.75rem",
              lineHeight: 1.4,
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
            }}
          >
            {props.description}
          </Typography>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box
      sx={{
        maxWidth: "100%",
        margin: "0 auto",
        padding: 0,
        marginTop: 1,
        "& > *:last-child": {
          marginBottom: 0,
        },
      }}
    >
      {cards.map((card) => (
        <InfoCardTemplate
          key={card.key}
          setKey={card.key}
          title={card.title}
          description={card.description}
        />
      ))}
    </Box>
  );
};

export default InfoCard;
