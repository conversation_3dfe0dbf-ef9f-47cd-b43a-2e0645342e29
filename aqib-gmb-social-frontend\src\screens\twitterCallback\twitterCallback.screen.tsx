import React, { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Card,
  CardContent,
} from "@mui/material";
import TwitterIcon from "@mui/icons-material/Twitter";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import TwitterService from "../../services/twitter/twitter.service";

const TwitterCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [message, setMessage] = useState("");

  const _twitterService = new TwitterService(dispatch);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get("code");
        const state = searchParams.get("state");
        const error = searchParams.get("error");

        if (error) {
          setStatus("error");
          setMessage(`Authentication failed: ${error}`);

          // Send error message to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "TWITTER_AUTH_ERROR",
                error: `Authentication failed: ${error}`,
              },
              window.location.origin
            );
          }
          return;
        }

        if (!code || !state) {
          setStatus("error");
          setMessage("Missing authorization code or state parameter");

          // Send error message to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "TWITTER_AUTH_ERROR",
                error: "Missing authorization code or state parameter",
              },
              window.location.origin
            );
          }
          return;
        }

        // Validate the callback with the backend
        const response = await _twitterService.validateCallback(code, state);

        if (response.success) {
          setStatus("success");
          setMessage("Twitter account connected successfully!");

          // Send success message to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "TWITTER_AUTH_SUCCESS",
                code,
                state,
                user: response.data.user,
              },
              window.location.origin
            );
          }

          // Redirect to create post page after a short delay
          setTimeout(() => {
            navigate(
              "/post-management/create-social-post?tab=4&twitter_connected=true"
            );
          }, 2000);
        } else {
          setStatus("error");
          setMessage(response.message || "Failed to connect Twitter account");

          // Send error message to parent window
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "TWITTER_AUTH_ERROR",
                error: response.message || "Failed to connect Twitter account",
              },
              window.location.origin
            );
          }
        }
      } catch (error: any) {
        console.error("Twitter callback error:", error);
        setStatus("error");
        setMessage(error.message || "An unexpected error occurred");

        // Send error message to parent window
        if (window.opener) {
          window.opener.postMessage(
            {
              type: "TWITTER_AUTH_ERROR",
              error: error.message || "An unexpected error occurred",
            },
            window.location.origin
          );
        }
      }
    };

    handleCallback();
  }, [searchParams, navigate]);

  const getStatusIcon = () => {
    switch (status) {
      case "loading":
        return <CircularProgress size={48} sx={{ color: "#1DA1F2" }} />;
      case "success":
        return <CheckCircleIcon sx={{ fontSize: 48, color: "#4caf50" }} />;
      case "error":
        return <ErrorIcon sx={{ fontSize: 48, color: "#f44336" }} />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "loading":
        return "#1DA1F2";
      case "success":
        return "#4caf50";
      case "error":
        return "#f44336";
      default:
        return "#1DA1F2";
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
        p: 2,
      }}
    >
      <Card sx={{ maxWidth: 500, width: "100%" }}>
        <CardContent sx={{ textAlign: "center", p: 4 }}>
          <Box sx={{ mb: 3 }}>
            <TwitterIcon sx={{ fontSize: 64, color: "#1DA1F2", mb: 2 }} />
            <Typography variant="h4" sx={{ mb: 1, color: getStatusColor() }}>
              Twitter Integration
            </Typography>
          </Box>

          <Box sx={{ mb: 3 }}>{getStatusIcon()}</Box>

          <Typography
            variant="h6"
            sx={{ mb: 2, color: getStatusColor(), fontWeight: 600 }}
          >
            {status === "loading" && "Connecting your Twitter account..."}
            {status === "success" && "Success!"}
            {status === "error" && "Connection Failed"}
          </Typography>

          <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
            {message ||
              "Please wait while we process your Twitter authentication."}
          </Typography>

          {status === "loading" && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                This may take a few moments. Please don't close this window.
              </Typography>
            </Alert>
          )}

          {status === "success" && (
            <Alert severity="success" sx={{ mt: 2 }}>
              <Typography variant="body2">
                You will be redirected to the create post page shortly.
              </Typography>
            </Alert>
          )}

          {status === "error" && (
            <Alert severity="error" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Please try connecting your Twitter account again. If the problem
                persists, contact support.
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default TwitterCallback;
