import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import { IReportsFilterData } from "../../components/reportsFilter/reportsFilter.component";
import { IPerformanceDateRange } from "../../components/performanceDateFilter/performanceDateFilter.component";

export interface IReviewsReportResponse {
  success: boolean;
  message: string;
  data: {
    reviews: any[];
    aggregated: {
      ratingsVsMonth: Record<string, Record<number, number>>;
      reviewsVsReplies: Record<string, { reviews: number; replies: number }>;
      ratingDistribution: Record<number, number>;
      reviewVolume: Record<string, number>;
      responseRate: { total: number; replied: number };
      avgResponseTime: number[];
    };
  };
}

export interface IPerformanceReportResponse {
  success: boolean;
  message: string;
  data: {
    performance: {
      currentPeriod: {
        totalReviews: number;
        totalReplies: number;
        responseRate: number;
        avgRating: number;
        avgResponseTime: number;
        ratingDistribution: Record<number, number>;
      };
      comparisonPeriod: {
        totalReviews: number;
        totalReplies: number;
        responseRate: number;
        avgRating: number;
        avgResponseTime: number;
        ratingDistribution: Record<number, number>;
      };
      trends: {
        reviewsChange: number;
        repliesChange: number;
        responseRateChange: number;
        avgRatingChange: number;
        avgResponseTimeChange: number;
      };
      charts: {
        dailyVolume: Record<string, { reviews: number; replies: number }>;
        responseRateTrend: Record<
          string,
          { total: number; replied: number; responseRate: number }
        >;
        ratingTrends: Record<
          string,
          {
            total: number;
            sum: number;
            avgRating: number;
            ratings: Record<number, number>;
          }
        >;
        responseTimeAnalysis: Record<string, number>;
        weeklyComparison: {
          current: Record<
            string,
            { total: number; replied: number; responseRate: number }
          >;
          comparison: Record<
            string,
            { total: number; replied: number; responseRate: number }
          >;
        };
      };
    };
    dateRange: { fromDate: string; toDate: string };
    totalRecords: number;
  };
}

export interface IReviewsPerformanceFilterData {
  businessId: string;
  accountId: string;
  locationId: string;
  fromDate: string;
  toDate: string;
}

export interface IGoogleAnalyticsFilterData {
  businessId: string;
  accountId: string;
  locationId: string;
  fromDate: string;
  toDate: string;
}

export interface IGoogleAnalyticsReportResponse {
  success: boolean;
  message: string;
  data: {
    currentPeriod: {
      totalImpressions: number;
      totalClicks: number;
      totalCalls: number;
      totalDirections: number;
      totalWebsiteClicks: number;
      totalMessaging: number;
      totalBookings: number;
      clickThroughRate: number;
    };
    comparisonPeriod: {
      totalImpressions: number;
      totalClicks: number;
      totalCalls: number;
      totalDirections: number;
      totalWebsiteClicks: number;
      totalMessaging: number;
      totalBookings: number;
      clickThroughRate: number;
    };
    trends: {
      impressionsChange: number;
      clicksChange: number;
      callsChange: number;
      directionsChange: number;
      websiteClicksChange: number;
      messagingChange: number;
      bookingsChange: number;
      ctrChange: number;
    };
    charts: {
      impressionsOverTime: { data: number[]; labels: string[] };
      clicksOverTime: { data: number[]; labels: string[] };
      callsOverTime: { data: number[]; labels: string[] };
      directionsOverTime: { data: number[]; labels: string[] };
      websiteClicksOverTime: { data: number[]; labels: string[] };
      messagingOverTime: { data: number[]; labels: string[] };
      bookingsOverTime: { data: number[]; labels: string[] };
      platformBreakdown: { label: string; value: number }[];
      searchQueries: { query: string; impressions: number; clicks: number }[];
    };
    dateRange: { fromDate: string; toDate: string };
    totalRecords: number;
  };
}

export default class ReportsService {
  _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  async getReviewsReport(
    filters: IReportsFilterData
  ): Promise<IReviewsReportResponse> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      startDate: filters.startDate || null,
      endDate: filters.endDate || null,
    };

    console.log("Reports service - sending request:", requestData);
    const response = await this._httpHelperService.post(
      "reports/reviews",
      requestData
    );
    console.log("Reports service - received response:", response);
    return response;
  }

  async exportReviewsReport(filters: IReportsFilterData): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      startDate: filters.startDate || null,
      endDate: filters.endDate || null,
    };

    return await this._httpHelperService.post(
      "reports/reviews/export",
      requestData
    );
  }

  async getPerformanceReport(
    filters: IReviewsPerformanceFilterData
  ): Promise<IPerformanceReportResponse> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    console.log("Performance reports service - sending request:", requestData);
    const response = await this._httpHelperService.post(
      "reports/performance",
      requestData
    );
    console.log("Performance reports service - received response:", response);
    return response;
  }

  async exportPerformanceReport(
    filters: IReviewsPerformanceFilterData
  ): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    return await this._httpHelperService.post(
      "reports/performance/export",
      requestData
    );
  }

  async getGoogleAnalyticsReport(
    filters: IGoogleAnalyticsFilterData
  ): Promise<IGoogleAnalyticsReportResponse> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    console.log(
      "Google Analytics reports service - sending request:",
      requestData
    );
    const response = await this._httpHelperService.post(
      "google-analytics/data",
      requestData
    );
    console.log(
      "Google Analytics reports service - received response:",
      response
    );
    return response;
  }

  async getGoogleAnalyticsCharts(
    filters: IGoogleAnalyticsFilterData
  ): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    const response = await this._httpHelperService.post(
      "google-analytics/charts",
      requestData
    );
    return response;
  }

  async getGoogleAnalyticsSummary(
    filters: IGoogleAnalyticsFilterData
  ): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    console.log(
      "Google Analytics summary service - sending request:",
      requestData
    );
    const response = await this._httpHelperService.post(
      "google-analytics/summary",
      requestData
    );
    console.log(
      "Google Analytics summary service - received response:",
      response
    );
    return response;
  }

  async exportGoogleAnalyticsReport(
    filters: IGoogleAnalyticsFilterData
  ): Promise<any> {
    const requestData = {
      businessId: filters.businessId !== "0" ? filters.businessId : null,
      accountId: filters.accountId !== "0" ? filters.accountId : null,
      locationId: filters.locationId !== "0" ? filters.locationId : null,
      fromDate: filters.fromDate || null,
      toDate: filters.toDate || null,
    };

    // Use downloadFilePost method for file downloads with POST request
    const response = await this._httpHelperService.downloadFilePost(
      "google-analytics/export",
      requestData
    );

    // Handle the file download
    const blob = new Blob([response.data], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    // Generate filename with current date
    const currentDate = new Date().toISOString().split("T")[0];
    const filename = `google-analytics-report-${currentDate}.xlsx`;

    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    link.style.display = "none";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    return response;
  }
}
