import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Button,
  Box,
  Modal,
  TextField,
  IconButton,
  Divider,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CloseIcon from "@mui/icons-material/Close";
import "./categoryDisplay.component.style.css";
import { Formik, Form, FormikHelpers } from "formik";
import * as yup from "yup";

interface CategoryFormValues {
  categoryName: string;
  isPrimary: boolean;
}

const CategoryDisplay = (props: {
  categories: any;
  onAddCategory?: (categoryName: string, isPrimary: boolean) => void;
}) => {
  const { primaryCategory, additionalCategories } = props.categories;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  return (
    <>
      {/* Additional Categories */}
      {additionalCategories && (
        <Card variant="outlined">
          <CardContent>
            <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
              <Chip
                key={primaryCategory.displayName}
                label={primaryCategory.displayName}
                variant="filled"
              />
              {additionalCategories.map((category: any, idx: number) => (
                <Chip
                  key={category.displayName}
                  label={category.displayName}
                  variant="outlined"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default CategoryDisplay;
