import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  TextField,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Checkbox,
  Grid,
  Link,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import { Formik, Form } from "formik";
import * as yup from "yup";

// Define the hours type
interface BusinessHour {
  day: string;
  closed: boolean;
  openTime: string;
  closeTime: string;
}

// Define the form values type
interface HoursFormValues {
  hoursType: string;
  hours: BusinessHour[];
}

interface BusinessHoursProps {
  onSave?: (values: HoursFormValues) => void;
  initialValues?: HoursFormValues;
  isOpen?: boolean;
  onClose?: () => void;
  hideButton?: boolean;
}

const BusinessHours: React.FC<BusinessHoursProps> = ({
  onSave,
  initialValues,
  isOpen: externalIsOpen,
  onClose: externalOnClose,
  hideButton = false,
}) => {
  // State for modal (only used when not externally controlled)
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  // Use external control if provided, otherwise use internal state
  const isModalOpen =
    externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const handleClose = externalOnClose || (() => setInternalIsOpen(false));

  // Default initial values
  const defaultValues: HoursFormValues = {
    hoursType: "OPEN_WITH_HOURS",
    hours: [
      { day: "MONDAY", closed: false, openTime: "10:00", closeTime: "13:00" },
      { day: "TUESDAY", closed: false, openTime: "10:00", closeTime: "13:00" },
      {
        day: "WEDNESDAY",
        closed: false,
        openTime: "10:00",
        closeTime: "13:00",
      },
      { day: "THURSDAY", closed: false, openTime: "10:00", closeTime: "13:00" },
      { day: "FRIDAY", closed: false, openTime: "10:00", closeTime: "13:00" },
      { day: "SATURDAY", closed: false, openTime: "10:00", closeTime: "13:00" },
      { day: "SUNDAY", closed: true, openTime: "10:00", closeTime: "13:00" },
    ],
  };

  // Transform initialValues to ensure proper structure
  const getInitialValues = (): HoursFormValues => {
    if (!initialValues) return defaultValues;

    // If initialValues has the expected structure, use it
    if (initialValues.hoursType && initialValues.hours) {
      return {
        ...initialValues,
        hours: initialValues.hours || defaultValues.hours,
      };
    }

    // If initialValues is from Google Business Profile format, transform it
    if (initialValues.periods) {
      // Transform Google Business Profile format to our format
      const transformedHours = defaultValues.hours.map((defaultHour) => {
        const period = initialValues.periods?.find(
          (p: any) =>
            p.openDay === defaultHour.day ||
            p.openDay === defaultHour.day.toUpperCase()
        );

        if (period) {
          return {
            day: defaultHour.day,
            closed: false,
            openTime: period.openTime?.hours
              ? `${period.openTime.hours.toString().padStart(2, "0")}:${(
                  period.openTime.minutes || 0
                )
                  .toString()
                  .padStart(2, "0")}`
              : defaultHour.openTime,
            closeTime: period.closeTime?.hours
              ? `${period.closeTime.hours.toString().padStart(2, "0")}:${(
                  period.closeTime.minutes || 0
                )
                  .toString()
                  .padStart(2, "0")}`
              : defaultHour.closeTime,
          };
        }

        return { ...defaultHour, closed: true };
      });

      return {
        hoursType: "OPEN_WITH_HOURS",
        hours: transformedHours,
      };
    }

    // Fallback to default values
    return defaultValues;
  };

  // Validation schema
  const hoursSchema = yup.object({
    hoursType: yup.string().required("Hours type is required"),
  });

  // Handle form submission
  const handleSubmit = (values: HoursFormValues) => {
    if (onSave) {
      onSave(values);
    }
    console.log("Hours saved:", values);
    handleClose();
  };

  return (
    <>
      {!hideButton && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            alignItems: "center",
          }}
        >
          <h4 style={{ marginRight: "10px" }}>Business Opening Hours:</h4>
          <Button
            className="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary emptyBtn css-1xqz8u5-MuiButtonBase-root-MuiButton-root"
            type="button"
            variant="contained"
            color="primary"
            onClick={() => setInternalIsOpen(true)}
            sx={{ textTransform: "none" }}
          >
            <svg
              className="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
              focusable="false"
              aria-hidden="true"
              viewBox="0 0 24 24"
              data-testid="DriveFileRenameOutlineIcon"
            >
              <path d="M18.41 5.8 17.2 4.59c-.78-.78-2.05-.78-2.83 0l-2.68 2.68L3 15.96V20h4.04l8.74-8.74 2.63-2.63c.79-.78.79-2.05 0-2.83M6.21 18H5v-1.21l8.66-8.66 1.21 1.21zM11 20l4-4h6v4z"></path>
            </svg>
          </Button>
        </Box>
      )}
      <Dialog
        open={isModalOpen}
        onClose={handleClose}
        fullWidth
        maxWidth="md"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px 24px",
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white",
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black",
            }}
          >
            Opening hours
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleClose}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 3, bgcolor: "white" }}>
          <Formik
            initialValues={getInitialValues()}
            validationSchema={hoursSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleSubmit, setFieldValue }) => (
              <Form onSubmit={handleSubmit}>
                <Typography variant="h6" sx={{ color: "black", mb: 1 }}>
                  Hours
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.7)",
                      display: "inline",
                    }}
                  >
                    Set main business hours or mark your business as closed.{" "}
                  </Typography>
                  {/* <Link
                    component="button"
                    type="button"
                    variant="body2"
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none"
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log("Learn more clicked");
                    }}
                  >
                    Learn more
                  </Link> */}
                </Box>

                <FormControl component="fieldset" sx={{ width: "100%" }}>
                  <RadioGroup
                    name="hoursType"
                    value={values.hoursType}
                    onChange={handleChange}
                  >
                    <FormControlLabel
                      value="OPEN_WITH_HOURS"
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography sx={{ color: "black", fontWeight: 500 }}>
                            Open with main hours
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: "rgba(0, 0, 0, 0.6)" }}
                          >
                            Show when your business is open
                          </Typography>
                        </Box>
                      }
                      sx={{ mb: 1 }}
                    />

                    <FormControlLabel
                      value="OPEN_WITHOUT_HOURS"
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography sx={{ color: "black", fontWeight: 500 }}>
                            Open with no main hours
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: "rgba(0, 0, 0, 0.6)" }}
                          >
                            Don't show any business hours
                          </Typography>
                        </Box>
                      }
                      sx={{ mb: 1 }}
                    />

                    <FormControlLabel
                      value="TEMPORARILY_CLOSED"
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography sx={{ color: "black", fontWeight: 500 }}>
                            Temporarily closed
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: "rgba(0, 0, 0, 0.6)" }}
                          >
                            Show that your business will open again in the
                            future
                          </Typography>
                        </Box>
                      }
                      sx={{ mb: 1 }}
                    />

                    <FormControlLabel
                      value="PERMANENTLY_CLOSED"
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography sx={{ color: "black", fontWeight: 500 }}>
                            Permanently closed
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ color: "rgba(0, 0, 0, 0.6)" }}
                          >
                            Show that your business no longer exists
                          </Typography>
                        </Box>
                      }
                    />
                  </RadioGroup>
                </FormControl>

                {values.hoursType === "OPEN_WITH_HOURS" && (
                  <Box sx={{ mt: 3 }}>
                    {(values.hours || []).map((hour, index) => (
                      <Grid
                        container
                        spacing={2}
                        key={index}
                        sx={{ mb: 2, alignItems: "center" }}
                      >
                        <Grid item xs={12} sm={2}>
                          <Typography sx={{ color: "black", fontWeight: 500 }}>
                            {hour.day.charAt(0) +
                              hour.day.slice(1).toLowerCase()}
                          </Typography>
                        </Grid>

                        <Grid item xs={12} sm={2}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={hour.closed}
                                onChange={(e) => {
                                  setFieldValue(
                                    `hours[${index}].closed`,
                                    e.target.checked
                                  );
                                }}
                              />
                            }
                            label={
                              <Typography sx={{ color: "black" }}>
                                Closed
                              </Typography>
                            }
                          />
                        </Grid>

                        {!hour.closed && (
                          <>
                            <Grid item xs={12} sm={3}>
                              <Typography
                                variant="caption"
                                sx={{ color: "rgba(0, 0, 0, 0.6)" }}
                              >
                                Opens at
                              </Typography>
                              <TextField
                                fullWidth
                                name={`hours[${index}].openTime`}
                                value={hour.openTime}
                                onChange={handleChange}
                                type="time"
                                size="small"
                                InputProps={{
                                  style: { color: "black" },
                                }}
                              />
                            </Grid>

                            <Grid item xs={12} sm={3}>
                              <Typography
                                variant="caption"
                                sx={{ color: "rgba(0, 0, 0, 0.6)" }}
                              >
                                Closes at
                              </Typography>
                              <TextField
                                fullWidth
                                name={`hours[${index}].closeTime`}
                                value={hour.closeTime}
                                onChange={handleChange}
                                type="time"
                                size="small"
                                InputProps={{
                                  style: { color: "black" },
                                }}
                              />
                            </Grid>

                            <Grid item xs={12} sm={2}>
                              <IconButton
                                aria-label="add hours"
                                sx={{ color: "#1976d2" }}
                                onClick={() => {
                                  console.log("Add hours clicked");
                                }}
                              >
                                <AddIcon />
                              </IconButton>
                            </Grid>
                          </>
                        )}
                      </Grid>
                    ))}
                  </Box>
                )}

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-start",
                    mt: 4,
                    gap: 2,
                  }}
                >
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: "none",
                      bgcolor: "#1976d2",
                      color: "white",
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setIsOpen(false)}
                    sx={{
                      textTransform: "none",
                      color: "#1976d2",
                      borderColor: "#e0e0e0",
                      bgcolor: "white",
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BusinessHours;
