import React, { useState } from "react";
import {
  Box,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Link,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Formik, Form } from "formik";
import * as yup from "yup";

// Define the form values type
interface BusinessAttributesFormValues {
  womenOwned: string;
  // Add more attributes as needed
  // blackOwned: string;
  // familyOwned: string;
  // veteranOwned: string;
  // lgbtqOwned: string;
}

interface FromTheBusinessProps {
  onSave?: (values: BusinessAttributesFormValues) => void;
  initialValues?: BusinessAttributesFormValues;
}

const FromTheBusiness: React.FC<FromTheBusinessProps> = ({ 
  onSave,
  initialValues
}) => {
  // State for modal
  const [isOpen, setIsOpen] = useState(false);

  // Default initial values
  const defaultValues: BusinessAttributesFormValues = {
    womenOwned: ""
  };

  // Validation schema
  const attributesSchema = yup.object({
    womenOwned: yup.string().required("Please select an option")
  });

  // Handle form submission
  const handleSubmit = (values: BusinessAttributesFormValues) => {
    if (onSave) {
      onSave(values);
    }
    console.log("Business attributes saved:", values);
    setIsOpen(false);
  };

  return (
    <>
      <Button
        variant="contained"
        color="primary"
        onClick={() => setIsOpen(true)}
        sx={{ textTransform: "none" }}
      >
        From The Business
      </Button>

      <Dialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          style: {
            backgroundColor: "white",
            borderRadius: "8px"
          }
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "16px 24px",
            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            bgcolor: "white"
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: "black"
            }}
          >
            More
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={() => setIsOpen(false)}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 3, bgcolor: "white" }}>
          <Formik
            initialValues={initialValues || defaultValues}
            validationSchema={attributesSchema}
            onSubmit={handleSubmit}
          >
            {({ values, handleChange, handleSubmit, errors, touched }) => (
              <Form onSubmit={handleSubmit}>
                <Typography variant="h6" sx={{ color: "black", mb: 1 }}>
                  From the business
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "rgba(0, 0, 0, 0.7)",
                      display: "inline"
                    }}
                  >
                    Let customers know more about your business by showing attributes on your Business Profile. 
                    These may appear publicly on Search, Maps and other Google services. If you're not the owner, 
                    confirm with the owner before adding an attribute.{" "}
                  </Typography>
                  <Link
                    component="button"
                    type="button"
                    variant="body2"
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none"
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log("Learn more clicked");
                    }}
                  >
                    Learn more
                  </Link>
                </Box>

                <Box sx={{ mb: 4 }}>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ 
                      color: "black", 
                      mb: 1,
                      fontWeight: 500
                    }}
                  >
                    Identifies as women-owned
                  </Typography>
                  
                  <RadioGroup
                    name="womenOwned"
                    value={values.womenOwned}
                    onChange={handleChange}
                    row
                  >
                    <FormControlLabel 
                      value="Yes" 
                      control={
                        <Radio 
                          sx={{
                            color: "#1976d2",
                            '&.Mui-checked': {
                              color: "#1976d2",
                            },
                          }}
                        />
                      } 
                      label={
                        <Typography sx={{ color: "black" }}>
                          Yes
                        </Typography>
                      }
                      sx={{ mr: 4 }}
                    />
                    <FormControlLabel 
                      value="No" 
                      control={
                        <Radio 
                          sx={{
                            color: "#1976d2",
                            '&.Mui-checked': {
                              color: "#1976d2",
                            },
                          }}
                        />
                      } 
                      label={
                        <Typography sx={{ color: "black" }}>
                          No
                        </Typography>
                      }
                    />
                  </RadioGroup>
                  
                  {errors.womenOwned && touched.womenOwned && (
                    <Typography color="error" variant="caption">
                      {errors.womenOwned}
                    </Typography>
                  )}
                </Box>

                {/* You can add more attributes here following the same pattern */}

                <Divider sx={{ my: 2 }} />

                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-start",
                    mt: 3,
                    gap: 2
                  }}
                >
                  <Button
                    variant="contained"
                    type="submit"
                    sx={{
                      textTransform: "none",
                      bgcolor: "#1976d2",
                      color: "white"
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setIsOpen(false)}
                    sx={{
                      textTransform: "none",
                      color: "#1976d2",
                      borderColor: "#e0e0e0",
                      bgcolor: "white"
                    }}
                  >
                    Cancel
                  </Button>
                </Box>
              </Form>
            )}
          </Formik>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FromTheBusiness;