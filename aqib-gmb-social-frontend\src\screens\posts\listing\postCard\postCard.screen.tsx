import { useContext, useState, useRef } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Chip,
  Avatar,
  Divider,
  Stack,
  Badge,
  Tooltip,
  Paper,
  LinearProgress,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Zoom,
  Menu,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  ArrowBackIos,
  ArrowForwardIos,
  Schedule as ScheduleIcon,
  Update as UpdateIcon,
  Language as LanguageIcon,
  Category as CategoryIcon,
  Link as LinkIcon,
  Groups as GroupsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Event as EventIcon,
  LocalOffer as OfferIcon,
  CallToAction as CallToActionIcon,
  Image as ImageIcon,
  Call as CallIcon,
  CalendarMonth as CalendarMonthIcon,
  ShoppingCart as ShoppingCartIcon,
  PersonAdd as PersonAddIcon,
  School as SchoolIcon,
} from "@mui/icons-material";
import ConfirmModel from "../../../../components/confirmModel/confirmModel.component";
import BulkPostEditDialog from "../../../../components/dialogs/BulkPostEditDialog";
import { LocalPost } from "../../../../interfaces/request/IGoogleCreatePostResponse";
import Dialog from "@mui/material/Dialog";
import PostsService from "../../../../services/posts/posts.service";
import { useDispatch, useSelector } from "react-redux";
import { IDeletePostResponseModel } from "../../../../interfaces/response/IDeletePostResponseModel";
import { ToastContext } from "../../../../context/toast.context";
import { ToastSeverity } from "../../../../constants/toastSeverity.constant";
import { MessageConstants } from "../../../../constants/message.constant";
import { TOPIC_TYPES } from "../../../../constants/application.constant";
import { LoadingContext } from "../../../../context/loading.context";
import ApplicationHelperService from "../../../../services/ApplicationHelperService";
import { useNavigate } from "react-router-dom";
import { IGoogleCreatePost } from "../../../../interfaces/request/IGoogleCreatePost";

const PostCard = (props: { post: LocalPost; refreshData: () => void }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const { post, refreshData } = props;
  const _postsService = new PostsService(dispatch);
  const [showMaximized, setShowMaximized] = useState<boolean>(false);
  const { setToastConfig } = useContext(ToastContext);
  const { setLoading } = useContext(LoadingContext);
  const [showConfirmDelete, setShowConfirmDelete] = useState<boolean>(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showBulkEditDialog, setShowBulkEditDialog] = useState<boolean>(false);
  const [bulkPostInfo, setBulkPostInfo] = useState<{
    isBulkPost: boolean;
    bulkPostId: string;
    totalPosts: number;
  } | null>(null);

  const checkBulkPostStatus = async () => {
    try {
      setLoading(true);
      const response = await _postsService.checkBulkPostStatus(post.name);

      if (response.isSuccess && response.data) {
        setBulkPostInfo({
          isBulkPost: response.data.isBulkPost,
          bulkPostId: response.data.bulkPostId,
          totalPosts: response.data.totalPosts,
        });

        if (response.data.isBulkPost) {
          setShowBulkEditDialog(true);
          return true; // Is bulk post
        }
      }
      return false; // Not a bulk post
    } catch (error) {
      console.error("Error checking bulk post status:", error);
      setToastConfig(ToastSeverity.Error, "Failed to check post status", true);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleEditClick = async () => {
    const isBulkPost = await checkBulkPostStatus();
    if (!isBulkPost) {
      // Proceed with normal edit for single post
      navigateToEditPost();
    }
    // If it's a bulk post, the dialog will handle the navigation
  };

  const navigateToEditPost = () => {
    const stateData: IGoogleCreatePost = {
      languageCode: post.languageCode,
      summary: post.summary,
      callToAction: post.callToAction,
      event: post.event,
      offer: post.offer,
      media: [],
      topicType: post.topicType,
    };

    if (post.media && post.media.length > 0) {
      for (let index = 0; index < post.media.length; index++) {
        const element = post.media[index];

        stateData.media.push({
          mediaFormat: element.mediaFormat,
          sourceUrl: element.googleUrl,
        });
      }
    }

    navigate("/post-management/create-social-post", {
      state: {
        createPost: stateData,
        title: "Edit Post",
        googlePostName: post.name,
        isSingleEdit: false,
      },
    });
  };

  const handleEditSinglePost = () => {
    setShowBulkEditDialog(false);

    const stateData: IGoogleCreatePost = {
      languageCode: post.languageCode,
      summary: post.summary,
      callToAction: post.callToAction,
      event: post.event,
      offer: post.offer,
      media: [],
      topicType: post.topicType,
    };

    if (post.media && post.media.length > 0) {
      for (let index = 0; index < post.media.length; index++) {
        const element = post.media[index];

        stateData.media.push({
          mediaFormat: element.mediaFormat,
          sourceUrl: element.googleUrl,
        });
      }
    }

    navigate("/post-management/create-social-post", {
      state: {
        createPost: stateData,
        title: "Edit Post (Single)",
        googlePostName: post.name,
        isSingleEdit: true, // This indicates it's a single edit from bulk
      },
    });
  };

  const handleEditAllPosts = () => {
    setShowBulkEditDialog(false);

    // Use the current post data as the template for bulk editing
    const stateData: IGoogleCreatePost = {
      languageCode: post.languageCode,
      summary: post.summary,
      callToAction: post.callToAction,
      event: post.event,
      offer: post.offer,
      media: [],
      topicType: post.topicType,
    };

    if (post.media && post.media.length > 0) {
      for (let index = 0; index < post.media.length; index++) {
        const element = post.media[index];

        stateData.media.push({
          mediaFormat: element.mediaFormat,
          sourceUrl: element.googleUrl,
        });
      }
    }

    // Navigate to bulk edit mode
    navigate("/post-management/create-social-post", {
      state: {
        createPost: stateData, // Include the post data for form initialization
        bulkEdit: true,
        bulkPostId: bulkPostInfo?.bulkPostId,
        totalPosts: bulkPostInfo?.totalPosts,
        title: `Edit Bulk Post (${bulkPostInfo?.totalPosts} posts)`,
      },
    });
  };

  const deletePost = async () => {
    setShowConfirmDelete(false);
    setLoading(true);
    const deleteResponse: IDeletePostResponseModel =
      await _postsService.deletePost(userInfo.id, post.name);

    if (deleteResponse.isSuccess) {
      setToastConfig(
        ToastSeverity.Success,
        MessageConstants.PostDeletedSuccessfully,
        true
      );
    } else {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.UnableToDeletePostAtThisMoment,
        true
      );
    }
    refreshData();
  };

  const handleNext = () => {
    if (post.media && post.media.length > 0) {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % post.media.length);
    }
  };

  const handlePrev = () => {
    if (post.media && post.media.length > 0) {
      setCurrentIndex(
        (prevIndex) => (prevIndex - 1 + post.media.length) % post.media.length
      );
    }
  };

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();
    event.stopPropagation();
    console.log("Menu button ref:", menuButtonRef.current);
    console.log("Button rect:", menuButtonRef.current?.getBoundingClientRect());
    setAnchorEl(menuButtonRef.current);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (state: string) => {
    switch (state) {
      case "LIVE":
        return { color: "#4caf50", bg: "#e8f5e8" };
      case "DRAFT":
        return { color: "#ff9800", bg: "#fff3e0" };
      case "SCHEDULED":
        return { color: "#2196f3", bg: "#e3f2fd" };
      default:
        return { color: "#757575", bg: "#f5f5f5" };
    }
  };

  const getTopicTypeIcon = (topicType: string) => {
    switch (topicType) {
      case TOPIC_TYPES.Event:
        return <EventIcon />;
      case TOPIC_TYPES.Offer:
        return <OfferIcon />;
      case "PRODUCT":
        return <CategoryIcon />;
      default:
        return <CategoryIcon />;
    }
  };

  const getCallToActionDisplay = (actionType: string) => {
    switch (actionType?.toUpperCase()) {
      case "CALL":
        return { icon: <CallIcon />, label: "Call" };
      case "BOOK":
        return { icon: <CalendarMonthIcon />, label: "Book Now" };
      case "ORDER":
        return { icon: <ShoppingCartIcon />, label: "Order" };
      case "SHOP":
        return { icon: <ShoppingCartIcon />, label: "Shop" };
      case "SIGN_UP":
        return { icon: <PersonAddIcon />, label: "Sign Up" };
      case "LEARN_MORE":
        return { icon: <SchoolIcon />, label: "Learn More" };
      default:
        return {
          icon: <CallToActionIcon />,
          label: actionType || "Call to Action",
        };
    }
  };

  const CardContentView = () => {
    const _applicationHelperService = new ApplicationHelperService({});
    const statusInfo = getStatusColor(post.state);

    return (
      <Card
        className="managementPostCard"
        sx={{
          boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
          minHeight: 500,
          overflow: "visible",
          borderRadius: 3,
          position: "relative",
          background: "linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)",
          border: "1px solid rgba(0,0,0,0.08)",
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          "&:hover": {
            boxShadow: "0 12px 48px rgba(0,0,0,0.15)",
            transform: "translateY(-2px)",
          },
        }}
      >
        {/* Header Section */}
        <Box
          sx={{
            position: "relative",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            color: "white",
            p: 2,
          }}
        >
          {showMaximized && (
            <IconButton
              sx={{
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 99,
                color: "white",
                backgroundColor: "rgba(255,255,255,0.2)",
                "&:hover": { backgroundColor: "rgba(255,255,255,0.3)" },
              }}
              onClick={() => setShowMaximized(false)}
            >
              <CloseIcon />
            </IconButton>
          )}

          <Stack direction="row" spacing={2} alignItems="center">
            <Avatar
              sx={{
                bgcolor: "rgba(255,255,255,0.2)",
                color: "white",
                width: 48,
                height: 48,
              }}
            >
              {getTopicTypeIcon(post.topicType)}
            </Avatar>

            <Box sx={{ flex: 1 }}>
              <Stack
                direction="row"
                spacing={1}
                alignItems="center"
                justifyContent="space-between"
                mb={1}
              >
                <Stack direction="row" spacing={1} alignItems="center">
                  <Chip
                    label={_applicationHelperService.toTitleCase(
                      post.topicType
                    )}
                    size="small"
                    sx={{
                      backgroundColor: "rgba(255,255,255,0.2)",
                      color: "white",
                      fontWeight: 600,
                      fontSize: "0.75rem",
                    }}
                  />

                  {post.isBulkPost && (
                    <Tooltip
                      title={`Part of bulk post (${post.totalBulkPosts} posts)`}
                    >
                      <Badge
                        badgeContent={post.totalBulkPosts}
                        color="secondary"
                        sx={{
                          "& .MuiBadge-badge": {
                            backgroundColor: "#ff4081",
                            color: "white",
                          },
                        }}
                      >
                        <GroupsIcon sx={{ color: "rgba(255,255,255,0.8)" }} />
                      </Badge>
                    </Tooltip>
                  )}
                </Stack>

                {/* Status and Language chips moved to top right */}
                <Stack direction="row" spacing={1} alignItems="center">
                  <Chip
                    icon={
                      post.state === "LIVE" ? (
                        <CheckCircleIcon sx={{ fontSize: 16 }} />
                      ) : (
                        <CancelIcon sx={{ fontSize: 16 }} />
                      )
                    }
                    label={post.state}
                    size="small"
                    sx={{
                      backgroundColor: statusInfo.bg,
                      color: statusInfo.color,
                      fontWeight: 600,
                      "& .MuiChip-icon": {
                        color: statusInfo.color,
                      },
                    }}
                  />

                  <Chip
                    icon={<LanguageIcon sx={{ fontSize: 16 }} />}
                    label={post.languageCode.toUpperCase()}
                    size="small"
                    sx={{
                      backgroundColor: "rgba(255,255,255,0.2)",
                      color: "white",
                    }}
                  />
                </Stack>
              </Stack>

              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {post.event?.title || "Social Media Post"}
              </Typography>
            </Box>
          </Stack>
        </Box>

        {/* Media Section */}
        {post.media && post.media.length > 0 ? (
          <Box sx={{ position: "relative" }}>
            <CardMedia
              component="img"
              height="280"
              image={post.media[currentIndex]?.googleUrl || ""}
              alt={`Post media ${currentIndex + 1}`}
              sx={{
                objectFit: "cover",
                transition: "all 0.3s ease",
              }}
            />

            {/* Media Counter */}
            {post.media.length > 1 && (
              <Chip
                label={`${currentIndex + 1} / ${post.media.length}`}
                size="small"
                sx={{
                  position: "absolute",
                  top: 12,
                  right: 12,
                  backgroundColor: "rgba(0,0,0,0.7)",
                  color: "white",
                  fontWeight: 600,
                }}
              />
            )}

            {/* Navigation Arrows */}
            {post.media.length > 1 && (
              <>
                <IconButton
                  onClick={handlePrev}
                  disabled={currentIndex === 0}
                  sx={{
                    position: "absolute",
                    left: 12,
                    top: "50%",
                    transform: "translateY(-50%)",
                    backgroundColor: "rgba(0,0,0,0.6)",
                    color: "white",
                    "&:hover": { backgroundColor: "rgba(0,0,0,0.8)" },
                    "&:disabled": {
                      backgroundColor: "rgba(0,0,0,0.3)",
                      color: "rgba(255,255,255,0.5)",
                    },
                  }}
                >
                  <ArrowBackIos />
                </IconButton>

                <IconButton
                  onClick={handleNext}
                  disabled={
                    !post.media || currentIndex === post.media.length - 1
                  }
                  sx={{
                    position: "absolute",
                    right: 12,
                    top: "50%",
                    transform: "translateY(-50%)",
                    backgroundColor: "rgba(0,0,0,0.6)",
                    color: "white",
                    "&:hover": { backgroundColor: "rgba(0,0,0,0.8)" },
                    "&:disabled": {
                      backgroundColor: "rgba(0,0,0,0.3)",
                      color: "rgba(255,255,255,0.5)",
                    },
                  }}
                >
                  <ArrowForwardIos />
                </IconButton>
              </>
            )}
          </Box>
        ) : (
          <Box
            sx={{
              height: 280,
              background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              color: "#666",
            }}
          >
            <ImageIcon sx={{ fontSize: 48, mb: 1, opacity: 0.5 }} />
            <Typography variant="body2" color="textSecondary">
              No Media Available
            </Typography>
          </Box>
        )}

        {/* Content Section */}
        <CardContent sx={{ p: 3 }}>
          {/* Post Summary */}
          <Box
            onClick={() =>
              showMaximized
                ? console.log("Already in Maximized View")
                : setShowMaximized(true)
            }
            sx={{ cursor: "pointer", mb: 3 }}
          >
            <Typography
              variant="body1"
              sx={{
                lineHeight: 1.6,
                color: "#333",
                display: "-webkit-box",
                WebkitLineClamp: showMaximized ? "none" : 3,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
                fontWeight: 400,
              }}
            >
              {post.summary || ""}
            </Typography>

            {!showMaximized && post.summary && post.summary.length > 150 && (
              <Typography
                variant="caption"
                sx={{
                  color: "primary.main",
                  fontWeight: 600,
                  mt: 1,
                  display: "block",
                }}
              >
                Click to read more...
              </Typography>
            )}
          </Box>

          {/* Special Content Sections */}
          {(post.event || post.offer || post.callToAction) && (
            <Box sx={{ mb: 3 }}>
              {post.event && (
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    backgroundColor: "#e3f2fd",
                    border: "1px solid #bbdefb",
                    borderRadius: 2,
                  }}
                >
                  <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                    <EventIcon sx={{ color: "#1976d2", fontSize: 20 }} />
                    <Typography
                      variant="subtitle2"
                      fontWeight={600}
                      color="#1976d2"
                    >
                      Event Details
                    </Typography>
                  </Stack>
                  <Typography variant="body2" fontWeight={600}>
                    {post.event?.title || "Event"}
                  </Typography>
                </Paper>
              )}

              {post.offer && (
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    backgroundColor: "#fff3e0",
                    border: "1px solid #ffcc02",
                    borderRadius: 2,
                  }}
                >
                  <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                    <OfferIcon sx={{ color: "#f57c00", fontSize: 20 }} />
                    <Typography
                      variant="subtitle2"
                      fontWeight={600}
                      color="#f57c00"
                    >
                      Special Offer
                    </Typography>
                  </Stack>
                  <Typography variant="body2" fontWeight={600}>
                    {post.offer?.redeemOnlineUrl
                      ? "Online Offer Available"
                      : "In-Store Offer"}
                  </Typography>
                </Paper>
              )}

              {/* Call to Action moved to footer */}
            </Box>
          )}

          {/* Metadata Section */}
          <Box sx={{ mb: 3 }}>
            <Divider sx={{ mb: 2 }} />

            <Stack spacing={2}>
              <Stack direction="row" spacing={3}>
                <Box sx={{ flex: 1 }}>
                  <Stack
                    direction="row"
                    spacing={1}
                    alignItems="center"
                    mb={0.5}
                  >
                    <ScheduleIcon sx={{ fontSize: 16, color: "#666" }} />
                    <Typography
                      variant="caption"
                      color="textSecondary"
                      fontWeight={600}
                    >
                      Created
                    </Typography>
                  </Stack>
                  <Typography variant="body2" fontWeight={500}>
                    {_applicationHelperService.getExpandedDateTimeFormat(
                      post.createTime
                    )}
                  </Typography>
                </Box>

                <Box sx={{ flex: 1 }}>
                  <Stack
                    direction="row"
                    spacing={1}
                    alignItems="center"
                    mb={0.5}
                  >
                    <UpdateIcon sx={{ fontSize: 16, color: "#666" }} />
                    <Typography
                      variant="caption"
                      color="textSecondary"
                      fontWeight={600}
                    >
                      Updated
                    </Typography>
                  </Stack>
                  <Typography variant="body2" fontWeight={500}>
                    {_applicationHelperService.getExpandedDateTimeFormat(
                      post.updateTime
                    )}
                  </Typography>
                </Box>
              </Stack>

              {/* Additional Metadata */}
              <Stack direction="row" spacing={2} flexWrap="wrap">
                {post.media && post.media.length > 0 && (
                  <Chip
                    icon={<ImageIcon sx={{ fontSize: 16 }} />}
                    label={`${post.media.length} ${
                      post.media.length === 1 ? "Image" : "Images"
                    }`}
                    size="small"
                    variant="outlined"
                    sx={{ borderColor: "#666", color: "#666" }}
                  />
                )}
              </Stack>
            </Stack>
          </Box>
        </CardContent>

        {/* Action Bar */}
        <Box
          sx={{
            borderTop: "1px solid rgba(0,0,0,0.08)",
            p: 2,
            backgroundColor: "#fafafa",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Stack direction="row" spacing={1}>
            <Tooltip title="View on Google">
              <IconButton
                onClick={() => window.open(post.searchUrl, "_blank")}
                sx={{
                  backgroundColor: "#e3f2fd",
                  color: "#1976d2",
                  "&:hover": { backgroundColor: "#bbdefb" },
                }}
              >
                <VisibilityIcon />
              </IconButton>
            </Tooltip>

            {Boolean(rbAccess && rbAccess.PostsEdit) && (
              <Tooltip title="Edit Post">
                <IconButton
                  onClick={handleEditClick}
                  sx={{
                    backgroundColor: "#e8f5e8",
                    color: "#4caf50",
                    "&:hover": { backgroundColor: "#c8e6c9" },
                  }}
                >
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}

            {Boolean(rbAccess && rbAccess.PostsDelete) && (
              <Tooltip title="Delete Post">
                <IconButton
                  onClick={() => setShowConfirmDelete(true)}
                  sx={{
                    backgroundColor: "#ffebee",
                    color: "#f44336",
                    "&:hover": { backgroundColor: "#ffcdd2" },
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>

          {/* Call to Action Display */}
          {post.callToAction && (
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {(() => {
                const ctaDisplay = getCallToActionDisplay(
                  post.callToAction.actionType
                );
                return (
                  <>
                    <Box
                      sx={{
                        color: "#4caf50",
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {ctaDisplay.icon}
                    </Box>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#4caf50",
                        fontWeight: 600,
                        textTransform: "none",
                      }}
                    >
                      {ctaDisplay.label}
                    </Typography>
                  </>
                );
              })()}
            </Box>
          )}
        </Box>

        <ConfirmModel
          isOpen={showConfirmDelete}
          title="Delete Post"
          description="Are you certain you want to delete this post? This action is irreversible."
          confirmText="Delete"
          cancelText="Cancel"
          cancelCallback={() => setShowConfirmDelete(false)}
          confirmCallback={() => deletePost()}
        />
      </Card>
    );
  };

  return (
    <Box>
      <Zoom in={true} timeout={300}>
        <Box>
          <CardContentView />
        </Box>
      </Zoom>

      <Dialog
        maxWidth="md"
        fullWidth
        open={showMaximized}
        onClose={() => setShowMaximized(false)}
        slotProps={{
          paper: {
            sx: {
              borderRadius: 3,
              maxHeight: "90vh",
            },
          },
        }}
      >
        <CardContentView />
      </Dialog>

      <BulkPostEditDialog
        open={showBulkEditDialog}
        onClose={() => setShowBulkEditDialog(false)}
        onEditSingle={handleEditSinglePost}
        onEditAll={handleEditAllPosts}
        totalPosts={bulkPostInfo?.totalPosts || 0}
        bulkPostId={bulkPostInfo?.bulkPostId || ""}
      />
    </Box>
  );
};

export default PostCard;
