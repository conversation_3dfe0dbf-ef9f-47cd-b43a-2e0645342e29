import React, { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Card,
  CardContent,
  LinearProgress,
  Fade,
  Zoom,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  Chip,
  Divider,
  keyframes,
} from "@mui/material";
import {
  CheckCircle,
  Error,
  Link as LinkIcon,
  AccountCircle,
  Done,
  ArrowForward,
  Refresh,
} from "@mui/icons-material";
import InstagramService from "../../services/instagram/instagram.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import LocoBizIcon from "../../assets/common/LocoBizIcon.png";
import InstagramLogo from "../../assets/common/instagram.png";

// Animation keyframes
const pulseAnimation = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const connectAnimation = keyframes`
  0% {
    transform: translateX(-50px);
    opacity: 0;
  }
  50% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(50px);
    opacity: 0;
  }
`;

const InstagramCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setToastConfig } = React.useContext(ToastContext);

  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accountsCount, setAccountsCount] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const _instagramService = new InstagramService(dispatch);

  // Connection steps
  const steps = [
    "Verifying authorization",
    "Connecting to Instagram",
    "Fetching account data",
    "Finalizing connection",
  ];

  // Progress simulation effect
  useEffect(() => {
    if (loading) {
      const progressTimer = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 15;
        });
      }, 300);

      const stepTimer = setInterval(() => {
        setCurrentStep((prev) => {
          if (prev < steps.length - 1) return prev + 1;
          return prev;
        });
      }, 800);

      return () => {
        clearInterval(progressTimer);
        clearInterval(stepTimer);
      };
    }
  }, [loading, steps.length]);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get("code");
        const state = searchParams.get("state");
        const errorParam = searchParams.get("error");
        const errorDescription = searchParams.get("error_description");

        // Handle OAuth errors
        if (errorParam) {
          setError(
            errorDescription || errorParam === "access_denied"
              ? "Instagram access was denied"
              : "Instagram authentication failed"
          );
          setLoading(false);
          return;
        }

        if (!code || !state) {
          setError("Missing authorization code or state parameter");
          setLoading(false);
          return;
        }

        // Step 1: Verifying authorization
        setCurrentStep(0);
        setProgress(25);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Try to decode state for debugging
        try {
          const decodedState = JSON.parse(atob(state));
          console.log("Decoded state:", decodedState);
        } catch (stateError) {
          console.error("Error decoding state:", stateError);
          setError("Invalid state parameter format");
          setLoading(false);
          return;
        }

        // Step 2: Connecting to Instagram
        setCurrentStep(1);
        setProgress(50);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Step 3: Fetching account data
        setCurrentStep(2);
        setProgress(75);

        // Validate the callback with backend
        const response = await _instagramService.validateCallback(code, state);

        if (response?.success) {
          // Step 4: Finalizing connection
          setCurrentStep(3);
          setProgress(100);
          await new Promise((resolve) => setTimeout(resolve, 500));

          setSuccess(true);
          setAccountsCount(response.data?.accountsCount || 0);

          setToastConfig(
            ToastSeverity.Success,
            `Instagram connected successfully! ${
              response.data?.accountsCount || 0
            } accounts found.`,
            true
          );

          console.log("Instagram authentication successful:", response.data);

          // Redirect to create posts page after a short delay
          setTimeout(() => {
            navigate("/post-management/create-social-post?tab=3");
          }, 2500);
        } else {
          setError(
            response?.message || "Failed to validate Instagram authentication"
          );
        }
      } catch (error: any) {
        console.error("Instagram callback error:", error);
        setError(error.message || "An unexpected error occurred");
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [searchParams, navigate, dispatch, userInfo?.id]);

  const handleRetry = () => {
    navigate("/post-management/create-social-post?tab=3");
  };

  const handleGoBack = () => {
    navigate("/post-management/create-social-post");
  };

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        minHeight: "100vh",
        p: 2,
        position: "relative",
      }}
    >
      <Fade in={true} timeout={800}>
        <Card
          sx={{
            maxWidth: 600,
            width: "100%",
            borderRadius: 4,
            boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
            overflow: "visible",
            position: "relative",
          }}
        >
          {/* Header with logos */}
          <Box
            sx={{
              background: "linear-gradient(45deg, #E1306C, #F56040, #FCAF45)",
              p: 3,
              textAlign: "center",
              position: "relative",
              overflow: "hidden",
            }}
          >
            {/* Background pattern */}
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: `url(${InstagramLogo})`,
                backgroundRepeat: "repeat",
                backgroundSize: "60px 60px",
                opacity: 0.1,
              }}
            />

            {/* Logo section */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: 3,
                mb: 2,
                position: "relative",
                zIndex: 1,
              }}
            >
              {/* App Logo */}
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: "white",
                  padding: "8px",
                  animation: loading ? `${pulseAnimation} 2s infinite` : "none",
                }}
              >
                <img
                  src={LocoBizIcon}
                  alt="GMB Social Logo"
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
              </Avatar>

              {/* Connection indicator */}
              <Box sx={{ position: "relative" }}>
                {loading && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: "50%",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                      animation: `${connectAnimation} 2s infinite`,
                    }}
                  >
                    <LinkIcon sx={{ color: "white", fontSize: 24 }} />
                  </Box>
                )}
                {!loading && (
                  <Zoom in={!loading} timeout={500}>
                    <ArrowForward sx={{ color: "white", fontSize: 32 }} />
                  </Zoom>
                )}
              </Box>

              {/* Instagram Logo */}
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: "white",
                  padding: "8px",
                  animation: loading
                    ? `${pulseAnimation} 2s infinite 0.5s`
                    : "none",
                }}
              >
                <img
                  src={InstagramLogo}
                  alt="Instagram Logo"
                  style={{
                    width: "80%",
                    height: "80%",
                    objectFit: "contain",
                  }}
                />
              </Avatar>
            </Box>

            <Typography
              variant="h5"
              sx={{
                color: "white",
                fontWeight: "bold",
                textShadow: "0 2px 4px rgba(0,0,0,0.3)",
              }}
            >
              {loading
                ? "Connecting to Instagram"
                : success
                ? "Connected Successfully!"
                : "Connection Failed"}
            </Typography>
          </Box>

          <CardContent sx={{ p: 4 }}>
            {loading && (
              <Fade in={loading} timeout={500}>
                <Box>
                  {/* Progress bar */}
                  <Box sx={{ mb: 3 }}>
                    <LinearProgress
                      variant="determinate"
                      value={progress}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: "#f0f0f0",
                        "& .MuiLinearProgress-bar": {
                          background:
                            "linear-gradient(45deg, #E1306C, #F56040)",
                          borderRadius: 4,
                        },
                      }}
                    />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mt: 1, textAlign: "center" }}
                    >
                      {Math.round(progress)}% Complete
                    </Typography>
                  </Box>

                  {/* Steps */}
                  <Stepper
                    activeStep={currentStep}
                    orientation="vertical"
                    sx={{ mb: 3 }}
                  >
                    {steps.map((label, index) => (
                      <Step key={label}>
                        <StepLabel
                          StepIconComponent={({ active, completed }) => (
                            <Box
                              sx={{
                                width: 24,
                                height: 24,
                                borderRadius: "50%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                bgcolor: completed
                                  ? "#4caf50"
                                  : active
                                  ? "#E1306C"
                                  : "#e0e0e0",
                                color: "white",
                                fontSize: 12,
                                fontWeight: "bold",
                              }}
                            >
                              {completed ? (
                                <Done sx={{ fontSize: 16 }} />
                              ) : (
                                index + 1
                              )}
                            </Box>
                          )}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight:
                                currentStep === index ? "bold" : "normal",
                            }}
                          >
                            {label}
                          </Typography>
                        </StepLabel>
                      </Step>
                    ))}
                  </Stepper>

                  <Box sx={{ textAlign: "center" }}>
                    <CircularProgress
                      size={40}
                      sx={{
                        color: "#E1306C",
                        mb: 2,
                      }}
                    />
                    <Typography variant="body1" color="text.secondary">
                      Please wait while we complete your Instagram
                      authentication...
                    </Typography>
                  </Box>
                </Box>
              </Fade>
            )}

            {success && !loading && (
              <Zoom in={success} timeout={800}>
                <Box sx={{ textAlign: "center" }}>
                  <CheckCircle
                    sx={{
                      fontSize: 80,
                      color: "success.main",
                      mb: 3,
                      animation: `${pulseAnimation} 1s ease-in-out`,
                    }}
                  />
                  <Typography
                    variant="h4"
                    sx={{ mb: 3, color: "success.main", fontWeight: "bold" }}
                  >
                    Successfully Connected!
                  </Typography>

                  {/* Connected logos display */}
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: 2,
                      mb: 3,
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 50,
                        height: 50,
                        bgcolor: "white",
                        padding: "6px",
                        border: "2px solid #4caf50",
                        boxShadow: "0 4px 8px rgba(76, 175, 80, 0.3)",
                      }}
                    >
                      <img
                        src={LocoBizIcon}
                        alt="GMB Social Logo"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "contain",
                        }}
                      />
                    </Avatar>
                    <ArrowForward
                      sx={{ color: "success.main", fontSize: 28 }}
                    />
                    <Avatar
                      sx={{
                        width: 50,
                        height: 50,
                        bgcolor: "white",
                        padding: "6px",
                        border: "2px solid #4caf50",
                        boxShadow: "0 4px 8px rgba(76, 175, 80, 0.3)",
                      }}
                    >
                      <img
                        src={InstagramLogo}
                        alt="Instagram Logo"
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "contain",
                        }}
                      />
                    </Avatar>
                  </Box>

                  <Typography
                    variant="body1"
                    sx={{ mb: 3, fontSize: "1.1rem" }}
                  >
                    Your Instagram account has been connected to GMB Social.
                  </Typography>

                  {accountsCount > 0 && (
                    <Chip
                      icon={<AccountCircle />}
                      label={`${accountsCount} Instagram account${
                        accountsCount !== 1 ? "s" : ""
                      } found`}
                      color="success"
                      variant="outlined"
                      sx={{ mb: 3, fontSize: "1rem", p: 2 }}
                    />
                  )}

                  <Divider sx={{ my: 3 }} />

                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: 1,
                      mb: 2,
                    }}
                  >
                    <CircularProgress size={20} sx={{ color: "#E1306C" }} />
                    <Typography variant="body2" color="text.secondary">
                      Redirecting to create posts page...
                    </Typography>
                  </Box>
                </Box>
              </Zoom>
            )}

            {error && !loading && (
              <Fade in={!!error} timeout={500}>
                <Box sx={{ textAlign: "center" }}>
                  <Error sx={{ fontSize: 80, color: "error.main", mb: 3 }} />
                  <Typography
                    variant="h4"
                    sx={{ mb: 2, color: "error.main", fontWeight: "bold" }}
                  >
                    Connection Failed
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 3 }}>
                    We couldn't connect your Instagram account.
                  </Typography>

                  <Alert
                    severity="error"
                    sx={{
                      mb: 3,
                      textAlign: "left",
                      borderRadius: 2,
                      "& .MuiAlert-message": {
                        fontSize: "1rem",
                      },
                    }}
                  >
                    {error}
                  </Alert>

                  <Box
                    sx={{
                      display: "flex",
                      gap: 2,
                      justifyContent: "center",
                      flexWrap: "wrap",
                    }}
                  >
                    <Button
                      variant="contained"
                      onClick={handleRetry}
                      startIcon={<Refresh />}
                      sx={{
                        minWidth: 140,
                        background: "linear-gradient(45deg, #E1306C, #F56040)",
                        "&:hover": {
                          background:
                            "linear-gradient(45deg, #C13584, #E1306C)",
                        },
                      }}
                    >
                      Try Again
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleGoBack}
                      sx={{
                        minWidth: 140,
                        borderColor: "#E1306C",
                        color: "#E1306C",
                        "&:hover": {
                          borderColor: "#C13584",
                          backgroundColor: "rgba(225, 48, 108, 0.04)",
                        },
                      }}
                    >
                      Go Back
                    </Button>
                  </Box>
                </Box>
              </Fade>
            )}
          </CardContent>
        </Card>
      </Fade>

      {/* Instagram Logo positioned at bottom right */}
      <Box
        sx={{
          position: "absolute",
          bottom: 20,
          right: 20,
          zIndex: 1000,
        }}
      >
        <img
          src={InstagramLogo}
          alt="Instagram"
          style={{
            width: "300px",
            height: "auto",
            opacity: 0.8,
          }}
        />
      </Box>
    </Box>
  );
};

export default InstagramCallback;
