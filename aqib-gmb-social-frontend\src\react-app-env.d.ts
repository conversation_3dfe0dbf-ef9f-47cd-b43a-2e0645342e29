/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_BASE_URL: string;
  readonly VITE_GOOGLE_MAPS_API_KEY: string;
  // more env variables...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Google Maps API types
declare global {
  interface Window {
    google: typeof google;
  }
}

declare namespace google {
  namespace maps {
    class Map {
      constructor(mapDiv: Element | null, opts?: MapOptions);
    }

    class Marker {
      constructor(opts?: MarkerOptions);
      addListener(eventName: string, handler: Function): void;
      setMap(map: Map | null): void;
    }

    class InfoWindow {
      constructor(opts?: InfoWindowOptions);
      open(map?: Map, anchor?: Marker): void;
    }

    class LatLng {
      constructor(lat: number, lng: number);
    }

    class Point {
      constructor(x: number, y: number);
    }

    namespace visualization {
      class HeatmapLayer {
        constructor(opts?: HeatmapLayerOptions);
        setMap(map: Map | null): void;
      }
    }

    enum MapTypeId {
      ROADMAP = "roadmap",
      SATELLITE = "satellite",
      HYBRID = "hybrid",
      TERRAIN = "terrain",
    }

    enum SymbolPath {
      CIRCLE = 0,
      FORWARD_CLOSED_ARROW = 1,
      FORWARD_OPEN_ARROW = 2,
      BACKWARD_CLOSED_ARROW = 3,
      BACKWARD_OPEN_ARROW = 4,
    }

    enum ControlPosition {
      TOP_CENTER = 1,
      TOP_LEFT = 2,
      TOP_RIGHT = 3,
      LEFT_TOP = 4,
      RIGHT_TOP = 5,
      LEFT_CENTER = 6,
      RIGHT_CENTER = 7,
      LEFT_BOTTOM = 8,
      RIGHT_BOTTOM = 9,
      BOTTOM_CENTER = 10,
      BOTTOM_LEFT = 11,
      BOTTOM_RIGHT = 12,
    }

    enum MapTypeControlStyle {
      DEFAULT = 0,
      HORIZONTAL_BAR = 1,
      DROPDOWN_MENU = 2,
    }

    interface MapOptions {
      center?: LatLng | LatLngLiteral;
      zoom?: number;
      mapTypeId?: MapTypeId | string;
      styles?: MapTypeStyle[];
      mapTypeControl?: boolean;
      mapTypeControlOptions?: MapTypeControlOptions;
      zoomControl?: boolean;
      zoomControlOptions?: ZoomControlOptions;
      scaleControl?: boolean;
      streetViewControl?: boolean;
      streetViewControlOptions?: StreetViewControlOptions;
      fullscreenControl?: boolean;
    }

    interface LatLngLiteral {
      lat: number;
      lng: number;
    }

    interface MarkerOptions {
      position?: LatLng | LatLngLiteral;
      map?: Map;
      icon?: string | Icon | Symbol;
      title?: string;
      label?: string | MarkerLabel;
    }

    interface InfoWindowOptions {
      content?: string | Element;
    }

    interface HeatmapLayerOptions {
      data: (LatLng | WeightedLocation)[];
      map?: Map;
      radius?: number;
      opacity?: number;
      gradient?: string[];
    }

    interface WeightedLocation {
      location: LatLng;
      weight: number;
    }

    interface Icon {
      url: string;
      size?: Size;
      origin?: Point;
      anchor?: Point;
      scaledSize?: Size;
    }

    interface Symbol {
      path: SymbolPath | string;
      anchor?: Point;
      fillColor?: string;
      fillOpacity?: number;
      labelOrigin?: Point;
      rotation?: number;
      scale?: number;
      strokeColor?: string;
      strokeOpacity?: number;
      strokeWeight?: number;
    }

    interface MarkerLabel {
      text: string;
      color?: string;
      fontFamily?: string;
      fontSize?: string;
      fontWeight?: string;
      className?: string;
    }

    interface Size {
      width: number;
      height: number;
    }

    interface MapTypeStyle {
      featureType?: string;
      elementType?: string;
      stylers?: MapTypeStyler[];
    }

    interface MapTypeStyler {
      visibility?: string;
      color?: string;
      weight?: number;
      gamma?: number;
      hue?: string;
      lightness?: number;
      saturation?: number;
    }

    interface MapTypeControlOptions {
      style?: MapTypeControlStyle;
      position?: ControlPosition;
    }

    interface ZoomControlOptions {
      position?: ControlPosition;
    }

    interface StreetViewControlOptions {
      position?: ControlPosition;
    }
  }
}
