.businessLogoPreview
{
    width: 90px;
    height: 90px;
    border-radius: 45px;
    background-color: #ffffff;
    border: 1px solid #cccccc; 
    margin-top: -60px;  
}
.missingCard
{
    box-shadow: 6px 6px 54px rgba(0, 0, 0, 0.05);
    border: 0px;
    border-radius: 16px;
}
.missingCard .missingCardInner
{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.EditIconBtn{
    border-radius: 50% !important;
}
/* .getPhoneNumberText{
display:"block" !important;
text-align:"center" !important;
} */
.ModalPopUpSaveBtn{
    background-color: var(--btnColor) !important;
    box-shadow: none !important;
    padding: 17px 16px !important;
    border-radius: 5px !important;
    text-transform: capitalize !important;
    font-size: 16px !important;
}
.ModalPopUpCancelBtn{
   background-color: var(--whiteColor) !important;
    border: 1px solid var(--borderColor) !important;
    color: var(--secondaryTextColor) !important;
    padding: 16px !important;
    border-radius: 5px !important;
    text-transform: capitalize;
}
    