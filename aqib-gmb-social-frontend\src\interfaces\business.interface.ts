// Make sure RoleType is properly exported
export enum RoleType {
  Admin = 1,
  Manager = 2,
  User = 3
}

// Add the instagramSyncStatus property to your IBusiness interface
export interface IBusiness {
  id: number;
  businessName: string;
  businessEmail: string;
  createdBy: number;
  statusId: number;
  updatedBy: number;
  createdAt: string;
  updatedAt: string;
  isGoogleSyncComplete?: boolean;
  instagramSyncStatus?: boolean;
  // other properties...
}

// Other constants...



