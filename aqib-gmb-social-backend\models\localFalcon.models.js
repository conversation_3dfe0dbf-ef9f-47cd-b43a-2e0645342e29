const pool = require("../config/db");
const logger = require("../utils/logger");
const util = require("util");

// Create a promisified query function to ensure it works properly
const queryAsync = util.promisify(pool.query.bind(pool));

module.exports = class LocalFalcon {
  /**
   * Save Local Falcon configuration to database
   */
  static async saveConfiguration(configData) {
    try {
      const {
        userId,
        name,
        keyword,
        businessName,
        placeId,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled,
        scheduleFrequency,
        alertThreshold,
        settings,
      } = configData;

      const query = `
        INSERT INTO local_falcon_configurations
        (user_id, name, keyword, business_name, place_id, center_lat, center_lng, 
         grid_size, radius, unit, is_schedule_enabled, schedule_frequency, 
         alert_threshold, settings, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      const values = [
        userId,
        name,
        keyword,
        businessName,
        placeId || null,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled || false,
        scheduleFrequency || null,
        alertThreshold || null,
        JSON.stringify(settings || {}),
      ];

      logger.info("Executing Local Falcon configuration save query", {
        query: query.replace(/\s+/g, " ").trim(),
        valuesCount: values.length,
        values: values,
      });

      const result = await queryAsync(query, values);

      logger.info("Query executed successfully", {
        resultType: typeof result,
        resultKeys: Object.keys(result || {}),
        insertId: result?.insertId,
      });

      logger.info("Local Falcon configuration saved successfully", {
        configId: result.insertId,
        userId,
        name,
      });

      return {
        id: result.insertId,
        ...configData,
      };
    } catch (error) {
      logger.error("Error saving Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configData,
      });
      throw error;
    }
  }

  /**
   * Get all configurations for a user
   */
  static async getConfigurations(userId) {
    try {
      const query = `
        SELECT id, user_id, name, keyword, business_name, place_id, center_lat, center_lng,
               grid_size, radius, unit, is_schedule_enabled, schedule_frequency,
               alert_threshold, settings, created_at, updated_at
        FROM local_falcon_configurations
        WHERE user_id = ?
        ORDER BY updated_at DESC
      `;

      const rows = await queryAsync(query, [userId]);

      logger.info("Raw configurations from database", {
        userId,
        rowCount: rows.length,
        sampleRow:
          rows.length > 0
            ? {
                id: rows[0].id,
                name: rows[0].name,
                settingsType: typeof rows[0].settings,
                settingsValue: rows[0].settings,
              }
            : null,
      });

      return rows.map((row) => ({
        ...row,
        settings:
          typeof row.settings === "string"
            ? JSON.parse(row.settings)
            : row.settings || {},
        centerLat: parseFloat(row.center_lat),
        centerLng: parseFloat(row.center_lng),
        radius: parseFloat(row.radius),
      }));
    } catch (error) {
      logger.error("Error getting Local Falcon configurations:", {
        error: error.message,
        stack: error.stack,
        userId,
      });
      throw error;
    }
  }

  /**
   * Get specific configuration by ID
   */
  static async getConfiguration(configId) {
    try {
      const query = `
        SELECT id, user_id, name, keyword, business_name, place_id, center_lat, center_lng,
               grid_size, radius, unit, is_schedule_enabled, schedule_frequency,
               alert_threshold, settings, created_at, updated_at
        FROM local_falcon_configurations
        WHERE id = ?
      `;

      const rows = await queryAsync(query, [configId]);

      if (rows.length === 0) {
        return null;
      }

      const config = rows[0];
      return {
        ...config,
        settings:
          typeof config.settings === "string"
            ? JSON.parse(config.settings)
            : config.settings || {},
        centerLat: parseFloat(config.center_lat),
        centerLng: parseFloat(config.center_lng),
        radius: parseFloat(config.radius),
      };
    } catch (error) {
      logger.error("Error getting Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configId,
      });
      throw error;
    }
  }

  /**
   * Update configuration
   */
  static async updateConfiguration(configId, updateData) {
    try {
      const {
        name,
        keyword,
        businessName,
        placeId,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled,
        scheduleFrequency,
        alertThreshold,
        settings,
      } = updateData;

      const query = `
        UPDATE local_falcon_configurations
        SET name = ?, keyword = ?, business_name = ?, place_id = ?, center_lat = ?, center_lng = ?,
            grid_size = ?, radius = ?, unit = ?, is_schedule_enabled = ?, schedule_frequency = ?,
            alert_threshold = ?, settings = ?, updated_at = NOW()
        WHERE id = ?
      `;

      const values = [
        name,
        keyword,
        businessName,
        placeId || null,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled || false,
        scheduleFrequency || null,
        alertThreshold || null,
        JSON.stringify(settings || {}),
        configId,
      ];

      const result = await queryAsync(query, values);

      logger.info("Local Falcon configuration updated successfully", {
        configId,
        affectedRows: result.affectedRows,
      });

      return result;
    } catch (error) {
      logger.error("Error updating Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configId,
        updateData,
      });
      throw error;
    }
  }

  /**
   * Delete configuration
   */
  static async deleteConfiguration(configId) {
    try {
      // First delete related scan results and alerts
      await queryAsync(
        "DELETE FROM local_falcon_scan_results WHERE configuration_id = ?",
        [configId]
      );
      await queryAsync(
        "DELETE FROM local_falcon_alerts WHERE configuration_id = ?",
        [configId]
      );

      // Then delete the configuration
      const query = "DELETE FROM local_falcon_configurations WHERE id = ?";
      const result = await queryAsync(query, [configId]);

      logger.info("Local Falcon configuration deleted successfully", {
        configId,
        affectedRows: result.affectedRows,
      });

      return result;
    } catch (error) {
      logger.error("Error deleting Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configId,
      });
      throw error;
    }
  }

  /**
   * Save scan result
   */
  static async saveScanResult(configId, scanData) {
    try {
      const {
        averagePosition,
        visibilityPercentage,
        totalSearches,
        foundInResults,
        rankings,
      } = scanData;

      const query = `
        INSERT INTO local_falcon_scan_results
        (configuration_id, average_position, visibility_percentage, total_searches,
         found_in_results, rankings_data, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `;

      const values = [
        configId,
        averagePosition,
        visibilityPercentage,
        totalSearches,
        foundInResults,
        JSON.stringify(rankings),
      ];

      const result = await queryAsync(query, values);

      logger.info("Local Falcon scan result saved successfully", {
        scanId: result.insertId,
        configId,
      });

      return result.insertId;
    } catch (error) {
      logger.error("Error saving Local Falcon scan result:", {
        error: error.message,
        stack: error.stack,
        configId,
        scanData,
      });
      throw error;
    }
  }

  /**
   * Get alerts for user
   */
  static async getAlerts(userId, unreadOnly = false) {
    try {
      let query = `
        SELECT a.id, a.configuration_id, a.alert_type, a.message, a.previous_position,
               a.current_position, a.created_at, a.is_read, c.name as configuration_name
        FROM local_falcon_alerts a
        JOIN local_falcon_configurations c ON a.configuration_id = c.id
        WHERE c.user_id = ?
      `;

      if (unreadOnly) {
        query += " AND a.is_read = false";
      }

      query += " ORDER BY a.created_at DESC LIMIT 50";

      const rows = await queryAsync(query, [userId]);

      return rows;
    } catch (error) {
      logger.error("Error getting Local Falcon alerts:", {
        error: error.message,
        stack: error.stack,
        userId,
        unreadOnly,
      });
      throw error;
    }
  }

  /**
   * Mark alert as read
   */
  static async markAlertAsRead(alertId) {
    try {
      const query =
        "UPDATE local_falcon_alerts SET is_read = true WHERE id = ?";
      const result = await queryAsync(query, [alertId]);

      logger.info("Local Falcon alert marked as read", {
        alertId,
        affectedRows: result.affectedRows,
      });

      return result;
    } catch (error) {
      logger.error("Error marking Local Falcon alert as read:", {
        error: error.message,
        stack: error.stack,
        alertId,
      });
      throw error;
    }
  }

  /**
   * Create alert
   */
  static async createAlert(configId, alertData) {
    try {
      const { alertType, message, previousPosition, currentPosition } =
        alertData;

      const query = `
        INSERT INTO local_falcon_alerts
        (configuration_id, alert_type, message, previous_position, current_position, created_at, is_read)
        VALUES (?, ?, ?, ?, ?, NOW(), false)
      `;

      const values = [
        configId,
        alertType,
        message,
        previousPosition || null,
        currentPosition || null,
      ];

      const result = await queryAsync(query, values);

      logger.info("Local Falcon alert created successfully", {
        alertId: result.insertId,
        configId,
        alertType,
      });

      return result.insertId;
    } catch (error) {
      logger.error("Error creating Local Falcon alert:", {
        error: error.message,
        stack: error.stack,
        configId,
        alertData,
      });
      throw error;
    }
  }
};
