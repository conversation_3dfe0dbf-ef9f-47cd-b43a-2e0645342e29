#!/usr/bin/env node

/**
 * Test script for Analytics Sync functionality
 * This script tests the analytics sync service independently
 */

require("dotenv").config();

const database = require("./config/database");
const analyticsSyncService = require("./services/analyticsSyncService");
const analyticsSyncModel = require("./models/analyticsSyncModel");
const logger = require("./utils/logger");

class AnalyticsSyncTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: [],
    };
  }

  /**
   * Run all analytics sync tests
   */
  async runTests() {
    console.log("🧪 Starting Analytics Sync Tests...\n");

    try {
      // Initialize database
      await this.testDatabaseConnection();

      // Test table creation
      await this.testTableCreation();

      // Test configuration initialization
      await this.testConfigInitialization();

      // Test API connectivity
      await this.testApiConnectivity();

      // Test location retrieval
      await this.testLocationRetrieval();

      // Test analytics stats
      await this.testAnalyticsStats();

      // Always perform full sync during test
      await this.testFullSync();

      // Print results
      this.printResults();
    } catch (error) {
      console.error("❌ Test suite failed:", error);
      process.exit(1);
    } finally {
      await database.close();
    }
  }

  /**
   * Test database connection
   */
  async testDatabaseConnection() {
    try {
      console.log("1️⃣ Testing database connection...");

      await database.initialize();

      // Test a simple query
      const [result] = await database.pool.execute("SELECT 1 as test");

      if (result[0].test === 1) {
        this.logSuccess("Database connection successful");
      } else {
        throw new Error("Database query returned unexpected result");
      }
    } catch (error) {
      this.logError("Database connection failed", error);
    }
  }

  /**
   * Test analytics table creation
   */
  async testTableCreation() {
    try {
      console.log("\n2️⃣ Testing analytics table creation...");

      await analyticsSyncModel.createAnalyticsTables();

      // Verify tables exist
      const [tables] = await database.pool.execute(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME IN ('location_analytics_daily', 'location_analytics_sync_log', 'location_analytics_config')
      `);

      if (tables.length === 3) {
        this.logSuccess(`All 3 analytics tables created/verified`);
      } else {
        throw new Error(`Expected 3 tables, found ${tables.length}`);
      }
    } catch (error) {
      this.logError("Table creation failed", error);
    }
  }

  /**
   * Test configuration initialization
   */
  async testConfigInitialization() {
    try {
      console.log("\n3️⃣ Testing configuration initialization...");

      const affectedRows = await analyticsSyncModel.initializeAnalyticsConfig();

      this.logSuccess(`Initialized config for ${affectedRows} locations`);
    } catch (error) {
      this.logError("Configuration initialization failed", error);
    }
  }

  /**
   * Test API connectivity
   */
  async testApiConnectivity() {
    try {
      console.log("\n4️⃣ Testing API connectivity...");
      console.log(
        `   Testing connection to: ${
          process.env.BACKEND_API_URL || "http://localhost:3000"
        }`
      );

      const isConnected = await analyticsSyncService.testConnection();

      if (isConnected) {
        this.logSuccess("API connectivity test passed");
      } else {
        this.logWarning("API connectivity test failed");
        console.log("   💡 Troubleshooting steps:");
        console.log(
          "   1. Make sure the backend API is running (aqib-gmb-social-backend)"
        );
        console.log("   2. Check BACKEND_API_URL in .env file");
        console.log(
          "   3. Verify the backend API is accessible on the configured port"
        );
        console.log("   4. Check firewall/network settings");
      }
    } catch (error) {
      this.logError("API connectivity test failed", error);
    }
  }

  /**
   * Test location retrieval
   */
  async testLocationRetrieval() {
    try {
      console.log("\n5️⃣ Testing location retrieval...");

      const locations = await analyticsSyncService.getLocationsThatNeedSync();

      console.log(`   Found ${locations.length} locations that need sync`);

      if (locations.length > 0) {
        console.log("   Sample locations:");
        locations.slice(0, 3).forEach((location) => {
          console.log(
            `   - ${location.gmbLocationName || location.gmbLocationId} (${
              location.gmbAccountId
            })`
          );
        });
        this.logSuccess("Location retrieval successful");
      } else {
        this.logWarning(
          "No locations need sync - this might be normal if sync was recently run"
        );
      }
    } catch (error) {
      this.logError("Location retrieval failed", error);
    }
  }

  /**
   * Test analytics statistics
   */
  async testAnalyticsStats() {
    try {
      console.log("\n6️⃣ Testing analytics statistics...");

      const stats = await analyticsSyncModel.getAnalyticsStats();

      console.log("   Analytics Statistics:");
      console.log(
        `   - Total locations with data: ${stats.totalLocationsWithData}`
      );
      console.log(`   - Metrics in last 30 days: ${stats.metricsLast30Days}`);
      console.log(`   - Latest sync date: ${stats.latestSyncDate || "Never"}`);
      console.log(
        `   - Successful syncs (7 days): ${stats.successfulSyncsLast7Days}`
      );
      console.log(`   - Failed syncs (7 days): ${stats.failedSyncsLast7Days}`);
      console.log(
        `   - Locations needing sync: ${stats.locationsThatNeedSync}`
      );

      // Show sync configuration
      const serviceStats = analyticsSyncService.getStats();
      console.log("   Sync Configuration:");
      console.log(
        `   - New locations: ${
          serviceStats.configuration.newLocationSyncDays
        } days (${Math.round(
          (serviceStats.configuration.newLocationSyncDays / 365) * 100
        )}% of year)`
      );
      console.log(`   - Existing locations: From last sync date to current`);
      console.log(
        `   - Batch size: ${serviceStats.configuration.batchSize} locations`
      );
      console.log(
        `   - Data retention: ${serviceStats.configuration.retentionDays} days`
      );

      this.logSuccess("Analytics statistics retrieved successfully");
    } catch (error) {
      this.logError("Analytics statistics test failed", error);
    }
  }

  /**
   * Test full analytics sync for all locations
   */
  async testFullSync() {
    try {
      console.log("\n7️⃣ Running full analytics sync for all locations...");
      console.log(
        "   ⚠️  This may take several minutes depending on the number of locations"
      );
      console.log("   📊 Syncing analytics data for all active locations");

      // Show current configuration for debugging
      const serviceStats = analyticsSyncService.getStats();
      console.log("\n   🔧 Current Configuration:");
      console.log(
        `   - Backend API URL: ${
          process.env.BACKEND_API_URL || "http://localhost:3000"
        }`
      );
      console.log(
        `   - Service User ID: ${process.env.SERVICE_USER_ID || "52"}`
      );
      console.log(
        `   - Has Auth Token: ${serviceStats.hasAuthToken ? "Yes" : "No"}`
      );
      console.log(
        `   - New Location Days: ${serviceStats.configuration.newLocationSyncDays}`
      );
      console.log(`   - Batch Size: ${serviceStats.configuration.batchSize}`);

      // Pre-sync validation
      console.log("\n   🔍 Pre-sync validation:");

      // Check if backend API is accessible
      const isApiConnected = await analyticsSyncService.testConnection();
      console.log(
        `   - API Connection: ${isApiConnected ? "✅ Connected" : "❌ Failed"}`
      );

      if (!isApiConnected) {
        console.log(
          "   ⚠️  Backend API is not accessible. This will cause all syncs to fail."
        );
        console.log(
          "   💡 Make sure the backend API is running and accessible."
        );
      } else {
        // Test the specific analytics endpoint
        console.log("   🔍 Testing analytics endpoint...");
        const endpointTest = await analyticsSyncService.testAnalyticsEndpoint();

        if (endpointTest.success) {
          console.log(
            `   ✅ Analytics endpoint responding (${endpointTest.status})`
          );
        } else {
          console.log(
            `   ❌ Analytics endpoint failed: ${
              endpointTest.error || endpointTest.code
            }`
          );
          console.log("   💡 This might be the cause of ECONNRESET errors");
        }
      }

      // Get locations that need sync
      const locations = await analyticsSyncService.getLocationsThatNeedSync();
      console.log(`   - Locations to sync: ${locations.length}`);

      if (locations.length === 0) {
        console.log("   ℹ️  No locations need sync at this time.");
        this.logWarning(
          "No locations need sync - this might be normal if sync was recently run"
        );
        return;
      }

      // Show sample location info
      if (locations.length > 0) {
        const sampleLocation = locations[0];
        console.log(
          `   - Sample location: ${
            sampleLocation.gmbLocationName || sampleLocation.gmbLocationId
          }`
        );
        console.log(
          `   - Has access token: ${sampleLocation.accessToken ? "Yes" : "No"}`
        );
        console.log(
          `   - Last sync date: ${sampleLocation.last_sync_date || "Never"}`
        );
      }

      const startTime = Date.now();

      // Run the complete analytics sync
      console.log("\n   🚀 Starting full sync...");
      const syncResults = await analyticsSyncService.syncAllAnalytics();

      const duration = Math.round((Date.now() - startTime) / 1000);

      console.log("\n   📈 Full Sync Results:");
      console.log(
        `   ⏱️  Total Duration: ${duration}s (${Math.round(duration / 60)}m ${
          duration % 60
        }s)`
      );
      console.log(
        `   📍 Locations Processed: ${syncResults.locationsProcessed}`
      );
      console.log(`   ✅ Successfully Synced: ${syncResults.totalSynced}`);
      console.log(`   ❌ Failed: ${syncResults.totalFailed}`);
      console.log(
        `   📊 Total Metrics Inserted: ${syncResults.metricsInserted}`
      );

      if (syncResults.errors && syncResults.errors.length > 0) {
        console.log(`   ⚠️  Errors encountered: ${syncResults.errors.length}`);
        console.log("   Detailed error analysis:");

        // Group errors by type
        const errorGroups = {};
        syncResults.errors.forEach((error) => {
          const errorType = error.type || "UNKNOWN";
          const errorMsg = error.error || "Unknown error";
          const key = `${errorType}: ${errorMsg}`;

          if (!errorGroups[key]) {
            errorGroups[key] = [];
          }
          errorGroups[key].push(error);
        });

        // Show grouped errors
        Object.entries(errorGroups).forEach(([errorKey, errors]) => {
          console.log(`   📍 ${errorKey} (${errors.length} locations)`);
          if (errors.length <= 3) {
            errors.forEach((error) => {
              console.log(`      - ${error.locationName || error.locationId}`);
            });
          } else {
            errors.slice(0, 2).forEach((error) => {
              console.log(`      - ${error.locationName || error.locationId}`);
            });
            console.log(`      - ... and ${errors.length - 2} more locations`);
          }
        });
      }

      // Show configuration used
      console.log("\n   ⚙️  Sync Configuration Used:");
      console.log(
        `   - New locations: ${syncResults.configuration.newLocationSyncDays} days of data`
      );
      console.log(`   - Existing locations: From last sync date to current`);
      console.log(
        `   - Batch size: ${syncResults.configuration.batchSize} locations`
      );
      console.log(
        `   - Supported metrics: ${syncResults.configuration.supportedMetrics} types`
      );

      if (syncResults.totalFailed === 0) {
        this.logSuccess(
          `Full sync completed successfully! ${syncResults.totalSynced} locations synced with ${syncResults.metricsInserted} metrics`
        );
      } else if (syncResults.totalSynced > 0) {
        this.logWarning(
          `Partial sync completed: ${syncResults.totalSynced} succeeded, ${syncResults.totalFailed} failed`
        );
      } else {
        this.logError(
          "Full sync failed",
          new Error(`All ${syncResults.totalFailed} locations failed to sync`)
        );

        // Provide troubleshooting guidance
        console.log("\n   🔧 Troubleshooting Guide:");
        console.log("   Common causes when ALL locations fail:");
        console.log("   1. Backend API not running or not accessible");
        console.log("   2. Invalid authentication token (SERVICE_AUTH_TOKEN)");
        console.log("   3. Database connection issues");
        console.log("   4. Network connectivity problems");
        console.log("   5. Google API quota exceeded or API disabled");
        console.log("   6. Invalid API endpoint configuration");
        console.log("\n   💡 Immediate steps to try:");
        console.log(
          "   1. Verify backend API is running: curl http://localhost:3000"
        );
        console.log("   2. Check .env file configuration");
        console.log(
          "   3. Test individual components with: npm run test-analytics-sync"
        );
        console.log("   4. Check logs for detailed error messages");
      }
    } catch (error) {
      this.logError("Full sync test failed", error);
    }
  }

  /**
   * Log success message
   */
  logSuccess(message) {
    console.log(`   ✅ ${message}`);
    this.testResults.passed++;
  }

  /**
   * Log warning message
   */
  logWarning(message) {
    console.log(`   ⚠️  ${message}`);
    this.testResults.passed++; // Warnings don't fail the test
  }

  /**
   * Log error message
   */
  logError(message, error) {
    console.log(`   ❌ ${message}: ${error.message}`);
    this.testResults.failed++;
    this.testResults.errors.push({
      message,
      error: error.message,
      stack: error.stack,
    });
  }

  /**
   * Print test results
   */
  printResults() {
    console.log("\n" + "=".repeat(50));
    console.log("📊 Test Results Summary");
    console.log("=".repeat(50));
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(
      `📈 Success Rate: ${Math.round(
        (this.testResults.passed /
          (this.testResults.passed + this.testResults.failed)) *
          100
      )}%`
    );

    if (this.testResults.failed > 0) {
      console.log("\n❌ Failed Tests:");
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.message}: ${error.error}`);
      });
    }

    if (this.testResults.failed === 0) {
      console.log(
        "\n🎉 All tests passed and full sync completed successfully!"
      );
      console.log(
        "\n📊 Analytics data has been synchronized for all locations."
      );
      console.log("\nNext steps:");
      console.log("1. Start the service for scheduled sync: npm start");
      console.log("2. Monitor logs for daily sync operations");
      console.log("3. Check database for analytics data");
      console.log("4. Verify frontend analytics dashboard");
      console.log("5. Analytics will now sync automatically daily at 1:00 AM");
    } else {
      console.log("\n⚠️  Some tests failed. Please review the errors above.");
      console.log(
        "Note: Even if sync partially failed, some data may have been synchronized."
      );
      process.exit(1);
    }
  }
}

// Run the tests
const tester = new AnalyticsSyncTester();
tester.runTests().catch((error) => {
  console.error("Test runner failed:", error);
  process.exit(1);
});
