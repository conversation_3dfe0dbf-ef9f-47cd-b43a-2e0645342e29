#!/usr/bin/env node

/**
 * Status monitoring script for GMB Auto Reply Service
 */

require('dotenv').config();

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class ServiceStatusMonitor {
  constructor() {
    this.serviceName = process.env.SERVICE_NAME || 'GMB-AutoReply-Service';
  }

  async checkStatus() {
    console.log('GMB Auto Reply Service - Status Monitor');
    console.log('======================================');
    console.log('');

    try {
      // Check Windows service status
      await this.checkWindowsServiceStatus();
      
      // Check log files
      await this.checkLogFiles();
      
      // Check recent activity
      await this.checkRecentActivity();

    } catch (error) {
      console.error('Error checking service status:', error.message);
    }
  }

  async checkWindowsServiceStatus() {
    console.log('1. Windows Service Status');
    console.log('------------------------');
    
    try {
      const output = execSync(`sc query "${this.serviceName}"`, { encoding: 'utf8' });
      
      // Parse service status
      const lines = output.split('\n');
      const serviceInfo = {};
      
      lines.forEach(line => {
        const trimmed = line.trim();
        if (trimmed.includes('SERVICE_NAME:')) {
          serviceInfo.name = trimmed.split(':')[1].trim();
        } else if (trimmed.includes('STATE')) {
          const stateLine = trimmed.split(':')[1].trim();
          serviceInfo.state = stateLine.split(' ')[1];
          serviceInfo.stateDescription = stateLine;
        } else if (trimmed.includes('WIN32_EXIT_CODE')) {
          serviceInfo.exitCode = trimmed.split(':')[1].trim();
        }
      });

      console.log(`   Service Name: ${serviceInfo.name || 'Unknown'}`);
      console.log(`   Status: ${serviceInfo.state || 'Unknown'}`);
      console.log(`   Description: ${serviceInfo.stateDescription || 'Unknown'}`);
      console.log(`   Exit Code: ${serviceInfo.exitCode || 'Unknown'}`);
      
      if (serviceInfo.state === 'RUNNING') {
        console.log('   ✓ Service is running');
      } else {
        console.log('   ⚠ Service is not running');
      }

    } catch (error) {
      console.log('   ✗ Service not found or not accessible');
      console.log('   Hint: Make sure the service is installed and you have proper permissions');
    }
  }

  async checkLogFiles() {
    console.log('');
    console.log('2. Log Files Status');
    console.log('------------------');
    
    const logsDir = path.join(__dirname, 'logs');
    const logFiles = ['combined.log', 'error.log'];
    
    if (!fs.existsSync(logsDir)) {
      console.log('   ⚠ Logs directory not found');
      return;
    }

    logFiles.forEach(logFile => {
      const logPath = path.join(logsDir, logFile);
      
      if (fs.existsSync(logPath)) {
        const stats = fs.statSync(logPath);
        const sizeKB = Math.round(stats.size / 1024);
        const lastModified = stats.mtime.toLocaleString();
        
        console.log(`   ${logFile}:`);
        console.log(`     - Size: ${sizeKB} KB`);
        console.log(`     - Last modified: ${lastModified}`);
        console.log(`     - ✓ File exists`);
      } else {
        console.log(`   ${logFile}: ✗ File not found`);
      }
    });
  }

  async checkRecentActivity() {
    console.log('');
    console.log('3. Recent Activity');
    console.log('-----------------');
    
    const logPath = path.join(__dirname, 'logs', 'combined.log');
    
    if (!fs.existsSync(logPath)) {
      console.log('   ⚠ Combined log file not found');
      return;
    }

    try {
      const logContent = fs.readFileSync(logPath, 'utf8');
      const lines = logContent.split('\n').filter(line => line.trim());
      
      if (lines.length === 0) {
        console.log('   ⚠ No log entries found');
        return;
      }

      // Get last 10 lines
      const recentLines = lines.slice(-10);
      
      console.log('   Last 10 log entries:');
      recentLines.forEach((line, index) => {
        if (line.trim()) {
          try {
            const logEntry = JSON.parse(line);
            const timestamp = new Date(logEntry.timestamp).toLocaleString();
            const level = logEntry.level.toUpperCase();
            const message = logEntry.message;
            
            console.log(`     ${index + 1}. [${timestamp}] ${level}: ${message}`);
          } catch (parseError) {
            // If not JSON, show raw line
            console.log(`     ${index + 1}. ${line.substring(0, 100)}...`);
          }
        }
      });

      // Check for recent errors
      const errorLines = lines.filter(line => line.includes('"level":"error"'));
      if (errorLines.length > 0) {
        console.log('');
        console.log('   ⚠ Recent errors detected:');
        const recentErrors = errorLines.slice(-3);
        recentErrors.forEach((line, index) => {
          try {
            const logEntry = JSON.parse(line);
            const timestamp = new Date(logEntry.timestamp).toLocaleString();
            console.log(`     ${index + 1}. [${timestamp}] ${logEntry.message}`);
          } catch (parseError) {
            console.log(`     ${index + 1}. ${line.substring(0, 100)}...`);
          }
        });
      } else {
        console.log('   ✓ No recent errors found');
      }

    } catch (error) {
      console.log('   ✗ Error reading log file:', error.message);
    }
  }

  async showServiceCommands() {
    console.log('');
    console.log('4. Service Management Commands');
    console.log('-----------------------------');
    console.log(`   Start service:    sc start "${this.serviceName}"`);
    console.log(`   Stop service:     sc stop "${this.serviceName}"`);
    console.log(`   Query status:     sc query "${this.serviceName}"`);
    console.log(`   Install service:  npm run install-service`);
    console.log(`   Uninstall:        npm run uninstall-service`);
    console.log('');
    console.log('   View logs:        type logs\\combined.log');
    console.log('   View errors:      type logs\\error.log');
    console.log('   Test service:     node test-service.js');
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('GMB Auto Reply Service - Status Monitor');
  console.log('=======================================');
  console.log('');
  console.log('Usage: node status.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --watch, -w    Watch mode (refresh every 30 seconds)');
  console.log('');
  process.exit(0);
}

const monitor = new ServiceStatusMonitor();

if (args.includes('--watch') || args.includes('-w')) {
  console.log('Starting watch mode (Ctrl+C to exit)...');
  console.log('');
  
  const runCheck = async () => {
    console.clear();
    await monitor.checkStatus();
    await monitor.showServiceCommands();
    console.log('');
    console.log('Refreshing in 30 seconds... (Ctrl+C to exit)');
  };
  
  runCheck();
  setInterval(runCheck, 30000);
} else {
  monitor.checkStatus().then(() => {
    monitor.showServiceCommands();
  });
}
