import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Divider,
  Chip,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useMediaQuery,
  useTheme,
  Checkbox,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  InputAdornment,
  CircularProgress,
} from "@mui/material";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Formik, Form } from "formik";
import * as yup from "yup";
import EditIcon from "@mui/icons-material/Edit";
import PhoneIcon from "@mui/icons-material/Phone";
import LanguageIcon from "@mui/icons-material/Language";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";

// Import existing components
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import EditBusinessNameModal from "../../../components/editBusinessName/editBusinessName.component";
import RegularHoursTable from "../../../components/regularHoursTable/regularHoursTable.component";
import CategoryDisplay from "../../../components/categoryDisplay/categoryDisplay.component";
import ServiceItemsDisplay from "../../../components/serviceItemsDisplay/serviceItemsDisplay.component";
import ServiceAreaList from "../../../components/serviceAreaList/serviceAreaList.component";
import MediaGallery from "../../../components/mediaGallery/mediaGallery.component";
import WebsiteUrlManagement from "../../../components/websiteUrlManagement/websiteUrlManagement.component";
import BusinessHours from "../../businessCategory/components/BusinessHours";
import GooglePlacesService, {
  ServiceAreaSuggestion,
} from "../../../services/googlePlaces/googlePlaces.service";
import AddBusinessCategoryModal from "../../businessCategory/components/addBusinessCategory.component";
import ServicesDisplay from "../../businessCategory/components/servicesDisplay.component";

// Import services
import LocationService from "../../../services/location/location.service";
import BusinessProfileService from "../../../services/businessProfile/businessProfile.service";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";

// Import interfaces
import { ILocation } from "../../../interfaces/response/ILocationsListResponseModel";

interface BusinessProfileData {
  businessName: string;
  phoneNumbers: Array<{
    number: string;
    type: string;
  }>;
  websiteUri: string;
  address: {
    addressLines: string[];
    locality: string;
    administrativeArea: string;
    postalCode: string;
    regionCode: string;
  };
  regularHours: any;
  categories: any;
  serviceItems: any[];
  serviceArea: any;
  photos: any[];
  description: string;
  attributes: any[];
}

const BusinessProfileManagement: React.FC = () => {
  const { businessId, accountId, locationId } = useParams<{
    businessId: string;
    accountId: string;
    locationId: string;
  }>();

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setToastConfig } = React.useContext(ToastContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // State management
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState<BusinessProfileData | null>(
    null
  );
  const [editBusinessNameOpen, setEditBusinessNameOpen] = useState(false);
  const [phoneNumbersDialogOpen, setPhoneNumbersDialogOpen] = useState(false);
  const [websiteDialogOpen, setWebsiteDialogOpen] = useState(false);
  const [isBusinessLocationModalOpen, setIsBusinessLocationModalOpen] =
    useState(false);
  const [isCategoriesModalOpen, setIsCategoriesModalOpen] = useState(false);
  const [isServicesModalOpen, setIsServicesModalOpen] = useState(false);
  const [isDescriptionModalOpen, setIsDescriptionModalOpen] = useState(false);
  const [isOpeningDateModalOpen, setIsOpeningDateModalOpen] = useState(false);
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);
  const [isServiceAreaModalOpen, setIsServiceAreaModalOpen] = useState(false);
  const [businessLocationData, setBusinessLocationData] = useState({
    showAddress: true,
    country: "India",
    streetAddress: "",
    streetAddressLine2: "",
    additionalAddressLines: [],
    city: "",
    pincode: "",
    state: "",
    latitude: 0,
    longitude: 0,
  });
  const [phoneNumbers, setPhoneNumbers] = useState<{
    primaryPhone: string;
    additionalPhones: string[];
  }>({
    primaryPhone: "",
    additionalPhones: [],
  });
  const [serviceAreas, setServiceAreas] = useState<string[]>([]);
  const [searchArea, setSearchArea] = useState("");
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [serviceAreaSuggestions, setServiceAreaSuggestions] = useState<
    ServiceAreaSuggestion[]
  >([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isBusinessHoursModalOpen, setIsBusinessHoursModalOpen] =
    useState(false);
  const [categories, setCategories] = useState<
    Array<{ name: string; isPrimary: boolean }>
  >([]);
  const [servicesCategories, setServicesCategories] = useState<
    Array<{
      name: string;
      isPrimary: boolean;
      services: Array<{ name: string; description: string }>;
    }>
  >([]);

  // Services
  const _locationService = new LocationService(dispatch);
  const _businessProfileService = new BusinessProfileService(dispatch);
  const _googlePlacesService = new GooglePlacesService(dispatch);

  // Initialize services categories from real data
  useEffect(() => {
    console.log("Processing profileData for services:", profileData);

    if (profileData) {
      const transformedCategories = [];

      // Create primary category with services
      if (profileData.categories && profileData.categories.primaryCategory) {
        const primaryCategory = profileData.categories.primaryCategory;
        const primaryCategoryName =
          primaryCategory.displayName ||
          primaryCategory.name ||
          primaryCategory;

        // Get Google suggested services from serviceTypes array
        const googleSuggestedServices = primaryCategory.serviceTypes
          ? primaryCategory.serviceTypes.map((serviceType: any) => ({
              name: serviceType.displayName || serviceType.serviceTypeId,
              description: `Professional ${
                serviceType.displayName || serviceType.serviceTypeId
              } services provided by our experienced team.`,
              isGoogleSuggested: true,
            }))
          : [];

        // Get MyLocoBiz services from actual serviceItems
        const myLocalBizServices = profileData.serviceItems
          ? profileData.serviceItems.map((item: any) => {
              console.log("Processing service item:", item);

              // Extract service name from different possible structures
              let serviceName = "Service";
              if (item.structuredServiceItem?.serviceTypeId) {
                // Format the service type ID to be more readable
                serviceName = item.structuredServiceItem.serviceTypeId
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (l: string) => l.toUpperCase());
              } else if (item.displayName) {
                serviceName = item.displayName;
              } else if (item.name) {
                serviceName = item.name;
              }

              return {
                name: serviceName,
                description:
                  item.structuredServiceItem?.description ||
                  item.description ||
                  `Professional ${serviceName.toLowerCase()} services provided by our experienced team.`,
                isGoogleSuggested: false,
              };
            })
          : [];

        // Combine both types of services for display
        const allServices = [...googleSuggestedServices, ...myLocalBizServices];

        console.log("Google suggested services:", googleSuggestedServices);
        console.log("MyLocoBiz  services:", myLocalBizServices);
        console.log("All services:", allServices);

        transformedCategories.push({
          name: primaryCategoryName,
          isPrimary: true,
          services: allServices, // Show both Google suggested and existing services
          googleSuggestedServices: primaryCategory.serviceTypes || [],
        });
      }

      // Add additional categories
      if (
        profileData.categories &&
        profileData.categories.additionalCategories
      ) {
        profileData.categories.additionalCategories.forEach((cat: any) => {
          transformedCategories.push({
            name: cat.displayName || cat.name || cat,
            isPrimary: false,
            services: [], // No services initially for additional categories
            googleSuggestedServices: cat.serviceTypes || [],
          });
        });
      }

      // If no categories but have service items, create a general category
      if (
        transformedCategories.length === 0 &&
        profileData.serviceItems &&
        profileData.serviceItems.length > 0
      ) {
        const myLocalBizServices = profileData.serviceItems.map((item: any) => {
          let serviceName = "Service";
          if (item.structuredServiceItem?.serviceTypeId) {
            serviceName = item.structuredServiceItem.serviceTypeId
              .replace(/_/g, " ")
              .replace(/\b\w/g, (l: string) => l.toUpperCase());
          } else if (item.displayName) {
            serviceName = item.displayName;
          } else if (item.name) {
            serviceName = item.name;
          }

          return {
            name: serviceName,
            description:
              item.structuredServiceItem?.description ||
              item.description ||
              `Professional ${serviceName.toLowerCase()} services.`,
            isGoogleSuggested: false,
          };
        });

        transformedCategories.push({
          name: profileData.businessName || "Business Services",
          isPrimary: true,
          services: myLocalBizServices,
          googleSuggestedServices: [],
        });
      }

      console.log("Transformed categories:", transformedCategories);
      setServicesCategories(transformedCategories);
    } else {
      // Set empty array if no real data
      setServicesCategories([]);
    }
  }, [profileData]);

  // Business Location validation schema
  const businessLocationValidationSchema = yup.object({
    country: yup.string().required("Country is required"),
    streetAddress: yup.string().required("Street address is required"),
    streetAddressLine2: yup.string(),
    additionalAddressLines: yup.array().of(yup.string()),
    city: yup.string().required("City is required"),
    pincode: yup.string().required("Pincode is required"),
    state: yup.string().required("State is required"),
  });

  // Phone number validation schema
  const phoneNumberValidationSchema = yup.object({
    primaryPhone: yup
      .string()
      .required("Primary phone number is required")
      .matches(
        /^[0-9\s]+$/,
        "Phone number should only contain digits and spaces"
      )
      .min(10, "Phone number must be at least 10 digits"),
    additionalPhones: yup.array().of(
      yup
        .string()
        .matches(
          /^[0-9\s]+$/,
          "Phone number should only contain digits and spaces"
        )
        .min(10, "Phone number must be at least 10 digits")
    ),
  });

  // Service area validation schema
  const serviceAreaValidationSchema = yup.object({
    serviceAreas: yup
      .array()
      .of(yup.string())
      .min(1, "At least one service area is required"),
  });

  // Fetch business profile data
  const fetchBusinessProfile = useCallback(async () => {
    if (!businessId || !accountId || !locationId) return;

    try {
      setLoading(true);
      const getLocationDetailsRequest = {
        "x-gmb-account-id": accountId,
        "x-gmb-business-id": businessId,
        "x-gmb-location-id": locationId,
      };

      const response = await _locationService.getLocationSummary(
        getLocationDetailsRequest
      );

      console.log("Location Summary Response:", response);

      if (response && response.success) {
        // Transform the response data to match our interface
        const locationData = response.data || response;
        // Transform phone numbers from the API format to the expected format
        let transformedPhoneNumbers = [];
        console.log("Raw phone numbers from API:", locationData.phoneNumbers);

        if (locationData.phoneNumbers) {
          if (Array.isArray(locationData.phoneNumbers)) {
            // If it's already an array, use it as is
            transformedPhoneNumbers = locationData.phoneNumbers;
          } else if (typeof locationData.phoneNumbers === "object") {
            // If it's an object with primaryPhone and additionalPhones
            const { primaryPhone, additionalPhones } =
              locationData.phoneNumbers;
            if (primaryPhone) {
              transformedPhoneNumbers.push({
                number: primaryPhone,
                type: "PRIMARY",
              });
            }
            if (additionalPhones && Array.isArray(additionalPhones)) {
              additionalPhones.forEach((phone) => {
                transformedPhoneNumbers.push({
                  number: phone,
                  type: "ADDITIONAL",
                });
              });
            }
          }
        }

        console.log("Transformed phone numbers:", transformedPhoneNumbers);

        const transformedData: BusinessProfileData = {
          businessName: locationData.title || locationData.name || "",
          phoneNumbers: transformedPhoneNumbers,
          websiteUri: locationData.websiteUri || "",
          address: locationData.storefrontAddress || locationData.address || {},
          regularHours: locationData.regularHours || {},
          categories: {
            primaryCategory: locationData.categories?.primaryCategory,
            additionalCategories:
              locationData.categories?.additionalCategories || [],
          },
          serviceItems: locationData.serviceItems || [],
          serviceArea: locationData.serviceArea || {},
          photos:
            locationData.mediaInfo?.mediaItems || locationData.photos || [],
          description:
            locationData.profile?.description || locationData.description || "",
          attributes: locationData.attributes || [],
        };

        setProfileData(transformedData);
      } else {
        console.error("API Response Error:", response);
        setToastConfig(
          ToastSeverity.Error,
          response?.message || "Failed to fetch business profile data",
          true
        );
      }
    } catch (error) {
      console.error("Error fetching business profile:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Error fetching business profile data",
        true
      );
      // Set empty profile data to stop loading
      setProfileData({
        businessName: "",
        phoneNumbers: [],
        websiteUri: "",
        address: {
          addressLines: [],
          locality: "",
          administrativeArea: "",
          postalCode: "",
          regionCode: "",
        },
        regularHours: {},
        categories: { primaryCategory: null, additionalCategories: [] },
        serviceItems: [],
        serviceArea: {},
        photos: [],
        description: "",
        attributes: [],
      });
    } finally {
      setLoading(false);
    }
  }, [businessId, accountId, locationId]);

  useEffect(() => {
    fetchBusinessProfile();
  }, [fetchBusinessProfile]);

  // Initialize business location data from profile data
  useEffect(() => {
    if (profileData && profileData.address) {
      setBusinessLocationData({
        showAddress: true,
        country: profileData.address.regionCode || "India",
        streetAddress: profileData.address.addressLines?.[0] || "",
        streetAddressLine2: profileData.address.addressLines?.[1] || "",
        additionalAddressLines:
          profileData.address.addressLines?.slice(2) || [],
        city: profileData.address.locality || "",
        pincode: profileData.address.postalCode || "",
        state: profileData.address.administrativeArea || "",
        latitude: 0,
        longitude: 0,
      });
    }
  }, [profileData]);

  // Initialize phone numbers from profile data
  useEffect(() => {
    if (profileData && profileData.phoneNumbers) {
      const phones = Array.isArray(profileData.phoneNumbers)
        ? profileData.phoneNumbers
        : [];
      setPhoneNumbers({
        primaryPhone:
          phones.length > 0
            ? typeof phones[0] === "string"
              ? phones[0]
              : phones[0]?.number || ""
            : "",
        additionalPhones:
          phones.length > 1
            ? phones
                .slice(1)
                .map((phone) =>
                  typeof phone === "string" ? phone : phone?.number || ""
                )
            : [],
      });
    }
  }, [profileData]);

  // Initialize service areas from profile data
  useEffect(() => {
    if (profileData?.serviceArea?.places?.placeInfos) {
      const areas = profileData.serviceArea.places.placeInfos
        .map(
          (place: any) =>
            place.placeName || place.name || place.displayName || ""
        )
        .filter(Boolean);
      setServiceAreas(areas);
    } else if (profileData?.serviceArea?.placeInfos) {
      // Fallback for old structure
      const areas = profileData.serviceArea.placeInfos
        .map(
          (place: any) =>
            place.placeName || place.name || place.displayName || ""
        )
        .filter(Boolean);
      setServiceAreas(areas);
    }
  }, [profileData]);

  // Initialize categories from profile data
  useEffect(() => {
    if (profileData && profileData.categories) {
      const categoriesArray = [];

      // Add primary category
      if (profileData.categories.primaryCategory) {
        categoriesArray.push({
          name:
            profileData.categories.primaryCategory.displayName ||
            profileData.categories.primaryCategory.name ||
            profileData.categories.primaryCategory,
          isPrimary: true,
        });
      }

      // Add additional categories
      if (profileData.categories.additionalCategories) {
        profileData.categories.additionalCategories.forEach((cat: any) => {
          categoriesArray.push({
            name: cat.displayName || cat.name || cat,
            isPrimary: false,
          });
        });
      }

      setCategories(categoriesArray);
    }
  }, [profileData]);

  // Handle business name update
  const handleBusinessNameUpdate = () => {
    setEditBusinessNameOpen(false);
    fetchBusinessProfile(); // Refresh data
  };

  // Handle phone number management
  const handleAddPhoneNumber = async (
    phoneNumber: string,
    phoneType: string
  ) => {
    if (!locationId || !profileData) return;

    try {
      await _businessProfileService.addPhoneNumber(
        locationId,
        phoneNumber,
        phoneType,
        profileData?.phoneNumbers || []
      );
      setPhoneNumbersDialogOpen(false);
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Phone number added successfully",
        true
      );
    } catch (error) {
      console.error("Error adding phone number:", error);
      setToastConfig(ToastSeverity.Error, "Failed to add phone number", true);
    }
  };

  const handleRemovePhoneNumber = async (phoneNumber: string) => {
    if (!locationId || !profileData) return;

    try {
      await _businessProfileService.removePhoneNumber(
        locationId,
        phoneNumber,
        profileData?.phoneNumbers || []
      );
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Phone number removed successfully",
        true
      );
    } catch (error) {
      console.error("Error removing phone number:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to remove phone number",
        true
      );
    }
  };

  const handleUpdatePhoneNumber = async (
    oldNumber: string,
    newNumber: string,
    phoneType: string
  ) => {
    if (!locationId || !profileData) return;

    try {
      await _businessProfileService.updatePhoneNumber(
        locationId,
        oldNumber,
        newNumber,
        phoneType,
        profileData?.phoneNumbers || []
      );
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Phone number updated successfully",
        true
      );
    } catch (error) {
      console.error("Error updating phone number:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update phone number",
        true
      );
    }
  };

  // Handle website URL update
  const handleUpdateWebsite = async (websiteUrl: string) => {
    if (!locationId) return;

    try {
      await _businessProfileService.updateWebsiteUrl({
        locationId,
        websiteUri: websiteUrl,
      });
      setWebsiteDialogOpen(false);
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        websiteUrl
          ? "Website URL updated successfully"
          : "Website URL removed successfully",
        true
      );
    } catch (error) {
      console.error("Error updating website URL:", error);
      setToastConfig(ToastSeverity.Error, "Failed to update website URL", true);
    }
  };

  // Handle business location management
  const handleOpenBusinessLocationModal = () => {
    setIsBusinessLocationModalOpen(true);
  };

  const handleCloseBusinessLocationModal = () => {
    setIsBusinessLocationModalOpen(false);
  };

  const handleSaveBusinessLocation = async (values: any) => {
    if (!locationId) return;

    try {
      // Transform the form data to Google Business Profile address format
      const addressData = {
        addressLines: [
          values.streetAddress,
          values.streetAddressLine2,
          ...values.additionalAddressLines,
        ].filter(Boolean),
        locality: values.city,
        administrativeArea: values.state,
        postalCode: values.pincode,
        regionCode: values.country,
      };

      // TODO: Implement API call to update address
      console.log("Updating business address:", addressData);

      setBusinessLocationData(values);
      handleCloseBusinessLocationModal();
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Business address updated successfully",
        true
      );
    } catch (error) {
      console.error("Error updating business address:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update business address",
        true
      );
    }
  };

  // Categories modal handlers
  const handleOpenCategoriesModal = () => {
    setIsCategoriesModalOpen(true);
  };

  const handleCloseCategoriesModal = () => {
    setIsCategoriesModalOpen(false);
  };

  const handleSaveCategories = async (categoryName: string) => {
    if (!locationId) return;

    try {
      // Add the new category to existing categories
      const newCategory = {
        name: categoryName,
        isPrimary: categories.length === 0, // First category is primary
      };

      // TODO: Implement API call to update categories
      console.log("Adding category:", newCategory);

      setCategories([...categories, newCategory]);
      handleCloseCategoriesModal();
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Business category added successfully",
        true
      );
    } catch (error) {
      console.error("Error adding category:", error);
      setToastConfig(ToastSeverity.Error, "Failed to add category", true);
    }
  };

  // Services modal handlers
  const handleOpenServicesModal = () => {
    console.log(
      "Opening services modal, servicesCategories:",
      servicesCategories
    );
    setIsServicesModalOpen(true);
  };

  const handleCloseServicesModal = () => {
    setIsServicesModalOpen(false);
  };

  const handleUpdateServicesCategories = async (
    updatedCategories: Array<{
      name: string;
      isPrimary: boolean;
      services: Array<{ name: string; description: string }>;
    }>
  ) => {
    if (!locationId) return;

    try {
      // TODO: Implement API call to update services categories
      console.log("Updating services categories:", updatedCategories);

      setServicesCategories(updatedCategories);
      setToastConfig(
        ToastSeverity.Success,
        "Services updated successfully",
        true
      );
    } catch (error) {
      console.error("Error updating services:", error);
      setToastConfig(ToastSeverity.Error, "Failed to update services", true);
    }
  };

  // Description modal handlers
  const handleOpenDescriptionModal = () => {
    setIsDescriptionModalOpen(true);
  };

  const handleCloseDescriptionModal = () => {
    setIsDescriptionModalOpen(false);
  };

  // Opening date modal handlers
  const handleOpenOpeningDateModal = () => {
    setIsOpeningDateModalOpen(true);
  };

  const handleCloseOpeningDateModal = () => {
    setIsOpeningDateModalOpen(false);
  };

  // Chat modal handlers
  const handleOpenChatModal = () => {
    setIsChatModalOpen(true);
  };

  const handleCloseChatModal = () => {
    setIsChatModalOpen(false);
  };

  // Service area modal handlers
  const handleOpenServiceAreaModal = () => {
    setIsServiceAreaModalOpen(true);
  };

  const handleCloseServiceAreaModal = () => {
    setIsServiceAreaModalOpen(false);
  };

  const handleSaveServiceAreas = async (values: { serviceAreas: string[] }) => {
    if (!locationId) return;

    try {
      // TODO: Implement API call to update service areas
      console.log("Updating service areas:", values.serviceAreas);

      setServiceAreas(values.serviceAreas);
      handleCloseServiceAreaModal();
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Service areas updated successfully",
        true
      );
    } catch (error) {
      console.error("Error updating service areas:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update service areas",
        true
      );
    }
  };

  const handleRemoveServiceArea = (
    areaToRemove: string,
    setFieldValue: any,
    values: { serviceAreas: string[] }
  ) => {
    const updatedAreas = values.serviceAreas.filter(
      (area) => area !== areaToRemove
    );
    setFieldValue("serviceAreas", updatedAreas);
  };

  const handleAddServiceArea = (
    newArea: string,
    setFieldValue: any,
    values: { serviceAreas: string[] }
  ) => {
    if (newArea && !values.serviceAreas.includes(newArea)) {
      setFieldValue("serviceAreas", [...values.serviceAreas, newArea]);
      setSearchArea("");
      setShowSuggestions(false);
      setServiceAreaSuggestions([]);
    }
  };

  // Handle service area search with autocomplete
  const handleServiceAreaSearch = async (query: string) => {
    setSearchArea(query);

    if (query.length < 3) {
      setServiceAreaSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setLoadingSuggestions(true);
    try {
      const response = await _googlePlacesService.getServiceAreaSuggestions(
        query
      );
      if (response.success) {
        setServiceAreaSuggestions(response.data);
        setShowSuggestions(true);
      } else {
        setServiceAreaSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      console.error("Error fetching service area suggestions:", error);
      setServiceAreaSuggestions([]);
      setShowSuggestions(false);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (
    suggestion: ServiceAreaSuggestion,
    setFieldValue: any,
    values: { serviceAreas: string[] }
  ) => {
    handleAddServiceArea(suggestion.placeName, setFieldValue, values);
  };

  // Phone number handlers
  const handleOpenPhoneNumberModal = () => {
    setPhoneNumbersDialogOpen(true);
  };

  const handleClosePhoneNumberModal = () => {
    setPhoneNumbersDialogOpen(false);
  };

  const handleSavePhoneNumbers = async (values: {
    primaryPhone: string;
    additionalPhones: string[];
  }) => {
    if (!locationId) return;

    try {
      // Transform the form data to Google Business Profile phone format
      const phoneNumbers = [values.primaryPhone, ...values.additionalPhones]
        .filter(Boolean)
        .map((phone, index) => ({
          number: phone,
          type: index === 0 ? "PRIMARY" : "ADDITIONAL",
        }));

      // Call the API to update phone numbers
      const response = await _businessProfileService.updatePhoneNumbers({
        locationId,
        phoneNumbers,
      });

      if (response.success) {
        setPhoneNumbers(values);
        handleClosePhoneNumberModal();
        fetchBusinessProfile();
        setToastConfig(
          ToastSeverity.Success,
          "Phone numbers updated successfully",
          true
        );
      } else {
        setToastConfig(
          ToastSeverity.Error,
          response.message || "Failed to update phone numbers",
          true
        );
      }
    } catch (error) {
      console.error("Error updating phone numbers:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update phone numbers",
        true
      );
    }
  };

  // Generate AI description handler
  const handleGenerateDescription = async (setFieldValue: any) => {
    if (!profileData) return;

    try {
      setIsGeneratingDescription(true);

      const response =
        await _businessProfileService.generateBusinessDescription({
          businessName: profileData?.businessName || "",
          categories: profileData?.categories || [],
          serviceItems: profileData?.serviceItems || [],
        });

      if (response.success && response.data?.description) {
        setFieldValue("description", response.data.description);
        setToastConfig(
          ToastSeverity.Success,
          "AI description generated successfully!",
          true
        );
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to generate description. Please try again.",
          true
        );
      }
    } catch (error) {
      console.error("Error generating AI description:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to generate description. Please try again.",
        true
      );
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  // Business hours modal handlers
  const handleOpenBusinessHoursModal = () => {
    setIsBusinessHoursModalOpen(true);
  };

  const handleCloseBusinessHoursModal = () => {
    setIsBusinessHoursModalOpen(false);
  };

  const handleSaveBusinessHours = async (values: any) => {
    if (!locationId) return;

    try {
      // TODO: Implement API call to update business hours
      console.log("Updating business hours:", values);

      handleCloseBusinessHoursModal();
      fetchBusinessProfile();
      setToastConfig(
        ToastSeverity.Success,
        "Business hours updated successfully",
        true
      );
    } catch (error) {
      console.error("Error updating business hours:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to update business hours",
        true
      );
    }
  };

  if (loading) {
    return (
      <LeftMenuComponent>
        <Box sx={{ p: 3, textAlign: "center" }}>
          <Typography>Loading business profile...</Typography>
        </Box>
      </LeftMenuComponent>
    );
  }

  if (!profileData) {
    return (
      <LeftMenuComponent>
        <Box sx={{ p: 3, textAlign: "center" }}>
          <Typography>No business profile data found</Typography>
          <Button onClick={() => navigate(-1)} sx={{ mt: 2 }}>
            Go Back
          </Button>
        </Box>
      </LeftMenuComponent>
    );
  }

  return (
    <LeftMenuComponent>
      <Box sx={{ p: 3 }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" className="pageTitle">
            Business Profile Management
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Manage your Google Business Profile information
          </Typography>
        </Box>

        {/* Basic Information Section */}
        <Card sx={{ mb: 2, boxShadow: 1 }}>
          <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontWeight: 600,
                color: "primary.main",
                fontSize: "1.1rem",
              }}
            >
              Basic Information
            </Typography>

            <Grid container spacing={2}>
              {/* Business Name */}
              <Grid item xs={12}>
                <Box
                  sx={{
                    p: 1.5,
                    border: "1px solid #e0e0e0",
                    borderRadius: 1,
                    bgcolor: "#fafafa",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mb: 1,
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      sx={{ fontWeight: 600, color: "#666" }}
                    >
                      Business Name
                    </Typography>
                    <IconButton
                      onClick={() => setEditBusinessNameOpen(true)}
                      size="small"
                      sx={{
                        bgcolor: "primary.main",
                        color: "white",
                        "&:hover": { bgcolor: "primary.dark" },
                        width: 28,
                        height: 28,
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 500, fontSize: "0.95rem" }}
                  >
                    {profileData?.businessName || "No business name set"}
                  </Typography>
                </Box>
              </Grid>

              {/* Phone Numbers */}
              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    p: 1.5,
                    border: "1px solid #e0e0e0",
                    borderRadius: 1,
                    bgcolor: "#fafafa",
                    height: "100%",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mb: 1,
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      sx={{ fontWeight: 600, color: "#666" }}
                    >
                      Phone Numbers
                    </Typography>
                    <IconButton
                      onClick={handleOpenPhoneNumberModal}
                      size="small"
                      sx={{
                        bgcolor: "primary.main",
                        color: "white",
                        "&:hover": { bgcolor: "primary.dark" },
                        width: 28,
                        height: 28,
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  {profileData?.phoneNumbers &&
                  profileData?.phoneNumbers.length > 0 ? (
                    profileData?.phoneNumbers
                      .slice(0, 2)
                      .map((phone, index) => (
                        <Box
                          key={index}
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <PhoneIcon
                            sx={{
                              mr: 1,
                              color: "primary.main",
                              fontSize: "1rem",
                            }}
                          />
                          <Typography sx={{ fontSize: "0.85rem", flex: 1 }}>
                            {typeof phone === "string" ? phone : phone.number}
                          </Typography>
                          {typeof phone === "object" && phone.type && (
                            <Chip
                              label={phone.type || "Primary"}
                              size="small"
                              sx={{ fontSize: "0.7rem", height: 20 }}
                            />
                          )}
                        </Box>
                      ))
                  ) : (
                    <Typography
                      color="text.secondary"
                      sx={{ fontStyle: "italic", fontSize: "0.85rem" }}
                    >
                      No phone numbers added
                    </Typography>
                  )}
                  {profileData?.phoneNumbers &&
                    profileData?.phoneNumbers.length > 2 && (
                      <Typography
                        color="primary.main"
                        sx={{ fontSize: "0.8rem", fontWeight: 500, mt: 0.5 }}
                      >
                        +{profileData.phoneNumbers.length - 2} more
                      </Typography>
                    )}
                </Box>
              </Grid>

              {/* Website URL */}
              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    p: 1.5,
                    border: "1px solid #e0e0e0",
                    borderRadius: 1,
                    bgcolor: "#fafafa",
                    height: "100%",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      mb: 1,
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      sx={{ fontWeight: 600, color: "#666" }}
                    >
                      Website
                    </Typography>
                    <IconButton
                      onClick={() => setWebsiteDialogOpen(true)}
                      size="small"
                      sx={{
                        bgcolor: "primary.main",
                        color: "white",
                        "&:hover": { bgcolor: "primary.dark" },
                        width: 28,
                        height: 28,
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  {profileData?.websiteUri ? (
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <LanguageIcon
                        sx={{
                          mr: 1,
                          color: "primary.main",
                          fontSize: "1rem",
                        }}
                      />
                      <Typography
                        component="a"
                        href={profileData?.websiteUri}
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: "primary.main",
                          textDecoration: "none",
                          "&:hover": { textDecoration: "underline" },
                          fontSize: "0.85rem",
                          wordBreak: "break-all",
                        }}
                      >
                        {profileData?.websiteUri.length > 30
                          ? `${profileData?.websiteUri.substring(0, 30)}...`
                          : profileData?.websiteUri}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography
                      color="text.secondary"
                      sx={{ fontStyle: "italic", fontSize: "0.85rem" }}
                    >
                      No website URL added
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Location Information Section */}
        <Card sx={{ mb: 2, boxShadow: 1 }}>
          <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontWeight: 600,
                color: "primary.main",
                fontSize: "1.1rem",
              }}
            >
              Location Information
            </Typography>

            <Box
              sx={{
                p: 1.5,
                border: "1px solid #e0e0e0",
                borderRadius: 1,
                bgcolor: "#fafafa",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 1,
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: 600, color: "#666" }}
                >
                  Business Address
                </Typography>
                <IconButton
                  onClick={handleOpenBusinessLocationModal}
                  size="small"
                  sx={{
                    bgcolor: "primary.main",
                    color: "white",
                    "&:hover": { bgcolor: "primary.dark" },
                    width: 28,
                    height: 28,
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                <LocationOnIcon
                  sx={{
                    mr: 1,
                    mt: 0.2,
                    color: "primary.main",
                    fontSize: "1rem",
                  }}
                />
                <Box>
                  {profileData?.address?.addressLines?.length > 0 ? (
                    <>
                      {profileData?.address?.addressLines?.map(
                        (line, index) => (
                          <Typography
                            key={index}
                            sx={{
                              fontSize: "0.85rem",
                              mb: 0.2,
                              lineHeight: 1.3,
                            }}
                          >
                            {line}
                          </Typography>
                        )
                      )}
                      <Typography
                        sx={{ fontSize: "0.85rem", mb: 0.2, lineHeight: 1.3 }}
                      >
                        {[
                          profileData?.address?.locality,
                          profileData?.address?.administrativeArea,
                          profileData?.address?.postalCode,
                        ]
                          .filter(Boolean)
                          .join(", ")}
                      </Typography>
                      {profileData?.address?.regionCode && (
                        <Typography
                          sx={{ fontSize: "0.85rem", lineHeight: 1.3 }}
                        >
                          {profileData?.address?.regionCode}
                        </Typography>
                      )}
                    </>
                  ) : (
                    <Typography
                      color="text.secondary"
                      sx={{ fontStyle: "italic", fontSize: "0.85rem" }}
                    >
                      No address information available
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Business Operations Section */}
        <Card sx={{ mb: 2, boxShadow: 1 }}>
          <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontWeight: 600,
                color: "primary.main",
                fontSize: "1.1rem",
              }}
            >
              Business Operations
            </Typography>

            <Box
              sx={{
                p: 1.5,
                border: "1px solid #e0e0e0",
                borderRadius: 1,
                bgcolor: "#fafafa",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 1,
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: 600, color: "#666" }}
                >
                  Business Hours
                </Typography>
                <IconButton
                  onClick={handleOpenBusinessHoursModal}
                  size="small"
                  sx={{
                    bgcolor: "primary.main",
                    color: "white",
                    "&:hover": { bgcolor: "primary.dark" },
                    width: 28,
                    height: 28,
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box sx={{ "& .MuiTable-root": { fontSize: "0.8rem" } }}>
                <RegularHoursTable
                  regularHours={profileData?.regularHours || []}
                  missingInformation={[]}
                  onSave={(values: any) => {
                    console.log("Saving business hours:", values);
                    // TODO: Implement API call to update business hours
                    fetchBusinessProfile();
                  }}
                />
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Business Details Section */}
        <Card sx={{ mb: 2, boxShadow: 1 }}>
          <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontWeight: 600,
                color: "primary.main",
                fontSize: "1.1rem",
              }}
            >
              Business Details
            </Typography>

            <Box
              sx={{
                display: "flex",
                gap: 2,
                flexWrap: "wrap",
                alignItems: "flex-start",
              }}
            >
              {/* Categories */}
              <Box
                sx={{
                  minWidth: 300,
                  p: 1.5,
                  border: "1px solid #e0e0e0",
                  borderRadius: 1,
                  bgcolor: "#fafafa",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 1,
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{ fontWeight: 600, color: "#666" }}
                  >
                    Business Categories
                  </Typography>
                  <IconButton
                    onClick={handleOpenCategoriesModal}
                    size="small"
                    sx={{
                      bgcolor: "primary.main",
                      color: "white",
                      "&:hover": { bgcolor: "primary.dark" },
                      width: 28,
                      height: 28,
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Box>
                <Box
                  sx={{
                    "& .MuiCard-root": { boxShadow: "none", border: "none" },
                  }}
                >
                  <CategoryDisplay
                    categories={profileData?.categories || {}}
                    onAddCategory={(categoryName, isPrimary) => {
                      console.log("Adding category:", categoryName, isPrimary);
                      // TODO: Implement API call to add category
                      fetchBusinessProfile();
                    }}
                  />
                </Box>
              </Box>

              {/* Service Items */}
              <Box
                sx={{
                  minWidth: 300,
                  p: 1.5,
                  border: "1px solid #e0e0e0",
                  borderRadius: 1,
                  bgcolor: "#fafafa",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 1,
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{ fontWeight: 600, color: "#666" }}
                  >
                    Services
                  </Typography>
                  <IconButton
                    onClick={handleOpenServicesModal}
                    size="small"
                    sx={{
                      bgcolor: "primary.main",
                      color: "white",
                      "&:hover": { bgcolor: "primary.dark" },
                      width: 28,
                      height: 28,
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Box>
                {profileData?.serviceItems &&
                profileData?.serviceItems.length > 0 ? (
                  <Box>
                    <ServiceItemsDisplay
                      serviceItems={profileData?.serviceItems || []}
                    />
                  </Box>
                ) : (
                  <Typography
                    color="text.secondary"
                    sx={{ fontStyle: "italic", fontSize: "0.85rem" }}
                  >
                    No services added
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Service Area and Description - Full Width Sections */}
            <Box sx={{ mt: 2 }}>
              {/* Service Area */}
              <Box
                sx={{
                  p: 1.5,
                  border: "1px solid #e0e0e0",
                  borderRadius: 1,
                  bgcolor: "#fafafa",
                  mb: 2,
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 1,
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{ fontWeight: 600, color: "#666" }}
                  >
                    Service Areas
                  </Typography>
                  <IconButton
                    onClick={handleOpenServiceAreaModal}
                    size="small"
                    sx={{
                      bgcolor: "primary.main",
                      color: "white",
                      "&:hover": { bgcolor: "primary.dark" },
                      width: 28,
                      height: 28,
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Box>
                {profileData?.serviceArea &&
                profileData?.serviceArea?.places?.placeInfos &&
                profileData?.serviceArea?.places?.placeInfos.length > 0 ? (
                  <Box>
                    <ServiceAreaList
                      placeInfos={
                        profileData?.serviceArea?.places?.placeInfos || []
                      }
                    />
                  </Box>
                ) : (
                  <Typography
                    color="text.secondary"
                    sx={{ fontStyle: "italic", fontSize: "0.85rem" }}
                  >
                    No service areas added
                  </Typography>
                )}
              </Box>

              {/* Description */}
              <Box
                sx={{
                  p: 1.5,
                  border: "1px solid #e0e0e0",
                  borderRadius: 1,
                  bgcolor: "#fafafa",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 1,
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{ fontWeight: 600, color: "#666" }}
                  >
                    Business Description
                  </Typography>
                  <IconButton
                    onClick={handleOpenDescriptionModal}
                    size="small"
                    sx={{
                      bgcolor: "primary.main",
                      color: "white",
                      "&:hover": { bgcolor: "primary.dark" },
                      width: 28,
                      height: 28,
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Box>
                {profileData?.description ? (
                  <Typography
                    sx={{
                      fontSize: "0.85rem",
                      lineHeight: 1.4,
                    }}
                  >
                    {profileData?.description}
                  </Typography>
                ) : (
                  <Typography
                    color="text.secondary"
                    sx={{ fontStyle: "italic", fontSize: "0.85rem" }}
                  >
                    No business description added
                  </Typography>
                )}
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Media Section */}
        <Card sx={{ mb: 2, boxShadow: 1 }}>
          <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
            <Typography
              variant="h6"
              sx={{
                mb: 2,
                fontWeight: 600,
                color: "primary.main",
                fontSize: "1.1rem",
              }}
            >
              Media Gallery
            </Typography>

            <Box
              sx={{
                p: 1.5,
                border: "1px solid #e0e0e0",
                borderRadius: 1,
                bgcolor: "#fafafa",
              }}
            >
              {profileData?.photos && profileData?.photos.length > 0 ? (
                <Box>
                  <MediaGallery mediaItems={profileData?.photos || []} />
                </Box>
              ) : (
                <Typography
                  color="text.secondary"
                  sx={{
                    fontStyle: "italic",
                    fontSize: "0.85rem",
                    textAlign: "center",
                    py: 2,
                  }}
                >
                  No media items available
                </Typography>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Edit Business Name Modal */}
        <EditBusinessNameModal
          open={editBusinessNameOpen}
          onClose={() => setEditBusinessNameOpen(false)}
          businessId={parseInt(businessId || "0")}
          currentBusinessName={profileData?.businessName || ""}
          onSuccess={handleBusinessNameUpdate}
        />

        {/* Phone Number Modal */}
        <Dialog
          open={phoneNumbersDialogOpen}
          onClose={handleClosePhoneNumberModal}
          fullWidth
          maxWidth="sm"
          PaperProps={{
            style: {
              backgroundColor: "#ffffff",
              color: "#2F2F2F",
              borderRadius: "8px",
              boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "20px 24px" },
              borderBottom: "1px solid #EBEBEB",
              bgcolor: "#EDEDED",
            }}
          >
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: "#2F2F2F",
                fontSize: { xs: "1.1rem", sm: "1.25rem" },
              }}
            >
              Contact information
            </Typography>
            <IconButton
              edge="end"
              onClick={handleClosePhoneNumberModal}
              aria-label="close"
              sx={{
                color: "#2F2F2F",
                "&:hover": {
                  bgcolor: "rgba(47, 47, 47, 0.1)",
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent
            sx={{
              px: { xs: 2, sm: 3 },
              py: { xs: 2, sm: 2 },
              bgcolor: "#ffffff",
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  color: "#2F2F2F",
                  fontSize: "1rem",
                  fontWeight: 600,
                  mb: 0.5,
                }}
              >
                Phone number
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: "#757575",
                  fontSize: { xs: "0.875rem", sm: "0.9rem" },
                  display: "inline",
                }}
              >
                Provide a number that connects directly to your business.{" "}
              </Typography>
            </Box>

            <Formik
              initialValues={phoneNumbers}
              validationSchema={phoneNumberValidationSchema}
              onSubmit={handleSavePhoneNumbers}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                isValid,
                handleSubmit,
                setFieldValue,
              }) => (
                <Form onSubmit={handleSubmit}>
                  {/* Primary Phone */}
                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 1,
                        color: "#2F2F2F",
                        fontWeight: 500,
                        fontSize: "0.9rem",
                      }}
                    >
                      Primary phone
                    </Typography>
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Box
                        sx={{
                          width: "80px",
                          border: "1px solid #EBEBEB",
                          borderRadius: "5px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          px: 1,
                          py: 0.5,
                          bgcolor: "#ffffff",
                        }}
                      >
                        <Box
                          component="img"
                          src="https://flagcdn.com/w20/in.png"
                          alt="India"
                          sx={{ width: 24, height: 16 }}
                        />
                        <Box
                          component="span"
                          sx={{
                            color: "#757575",
                            fontSize: "0.9rem",
                          }}
                        >
                          ▼
                        </Box>
                      </Box>
                      <TextField
                        fullWidth
                        name="primaryPhone"
                        value={values.primaryPhone}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={
                          touched.primaryPhone && Boolean(errors.primaryPhone)
                        }
                        helperText={touched.primaryPhone && errors.primaryPhone}
                        placeholder="Phone number"
                        sx={{
                          "& .MuiOutlinedInput-root": {
                            borderRadius: "5px",
                            bgcolor: "#ffffff",
                            "& fieldset": {
                              borderColor: "#EBEBEB",
                            },
                            "&:hover fieldset": {
                              borderColor: "#309898",
                            },
                            "&.Mui-focused fieldset": {
                              borderColor: "#309898",
                            },
                          },
                          "& .MuiInputBase-input": {
                            color: "#2F2F2F",
                          },
                          "& .MuiInputBase-input::placeholder": {
                            color: "#757575",
                            opacity: 1,
                          },
                        }}
                      />
                    </Box>
                  </Box>

                  {/* Additional Phones */}
                  {values.additionalPhones.map((phone, index) => (
                    <Box key={index} sx={{ mb: 3 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 1,
                          color: "black",
                          fontWeight: 400,
                          fontSize: "0.9rem",
                        }}
                      >
                        Additional phone
                      </Typography>
                      <Box
                        sx={{ display: "flex", gap: 1, alignItems: "center" }}
                      >
                        <Box
                          sx={{
                            width: "80px",
                            border: "1px solid rgba(0, 0, 0, 0.23)",
                            borderRadius: 1,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            px: 1,
                            py: 0.5,
                          }}
                        >
                          <Box
                            component="img"
                            src="https://flagcdn.com/w20/in.png"
                            alt="India"
                            sx={{ width: 24, height: 16 }}
                          />
                          <Box
                            component="span"
                            sx={{
                              color: "black",
                              fontSize: "0.9rem",
                            }}
                          >
                            ▼
                          </Box>
                        </Box>
                        <TextField
                          fullWidth
                          name={`additionalPhones[${index}]`}
                          value={phone}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={
                            touched.additionalPhones &&
                            Array.isArray(errors.additionalPhones) &&
                            Boolean(errors.additionalPhones[index])
                          }
                          helperText={
                            touched.additionalPhones &&
                            Array.isArray(errors.additionalPhones) &&
                            errors.additionalPhones[index]
                          }
                          placeholder="Phone number"
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              borderRadius: 1,
                              "& fieldset": {
                                borderColor: "rgba(0, 0, 0, 0.23)",
                              },
                            },
                          }}
                          slotProps={{
                            input: {
                              style: { color: "black" },
                            },
                          }}
                        />
                        <IconButton
                          onClick={() => {
                            const newPhones = [...values.additionalPhones];
                            newPhones.splice(index, 1);
                            setFieldValue("additionalPhones", newPhones);
                          }}
                          sx={{ color: "rgba(0, 0, 0, 0.54)" }}
                        >
                          <ClearIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  ))}

                  {/* Add Phone Button */}
                  <Button
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setFieldValue("additionalPhones", [
                        ...values.additionalPhones,
                        "",
                      ]);
                    }}
                    sx={{
                      mb: 3,
                      color: "#309898",
                      textTransform: "none",
                      fontWeight: "normal",
                      justifyContent: "flex-start",
                      pl: 0,
                      "&:hover": {
                        bgcolor: "#30989833",
                      },
                    }}
                  >
                    Add phone number
                  </Button>

                  <Divider sx={{ my: 2, borderColor: "#EBEBEB" }} />

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-start",
                      mt: 2,
                      flexWrap: "wrap",
                      gap: 1,
                    }}
                  >
                    <Button
                      variant="contained"
                      type="submit"
                      disabled={!isValid}
                      sx={{
                        textTransform: "none",
                        bgcolor: "#309898",
                        borderRadius: "5px",
                        px: 3,
                        color: "white",
                        fontWeight: 500,
                        "&:hover": {
                          bgcolor: "#267373",
                        },
                        "&.Mui-disabled": {
                          bgcolor: "#30989833",
                          color: "rgba(255, 255, 255, 0.5)",
                        },
                      }}
                    >
                      Save
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleClosePhoneNumberModal}
                      sx={{
                        textTransform: "none",
                        color: "#757575",
                        borderColor: "#EBEBEB",
                        borderRadius: "5px",
                        px: 3,
                        bgcolor: "white",
                        fontWeight: 500,
                        "&:hover": {
                          bgcolor: "#f4f4f4",
                          borderColor: "#EBEBEB",
                        },
                      }}
                    >
                      Cancel
                    </Button>
                  </Box>
                </Form>
              )}
            </Formik>
          </DialogContent>
        </Dialog>

        {/* Website URL Management */}
        <WebsiteUrlManagement
          open={websiteDialogOpen}
          onClose={() => setWebsiteDialogOpen(false)}
          currentWebsiteUrl={profileData?.websiteUri || ""}
          onUpdateWebsiteUrl={handleUpdateWebsite}
        />

        {/* Business Categories Modal */}
        <Dialog
          open={isCategoriesModalOpen}
          onClose={handleCloseCategoriesModal}
          fullWidth
          maxWidth="sm"
          PaperProps={{
            style: {
              backgroundColor: "white",
              color: "black",
              borderRadius: "8px",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "20px 24px" },
              borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            }}
          >
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: "black",
                fontSize: { xs: "1.1rem", sm: "1.25rem" },
              }}
            >
              Business category
            </Typography>
            <IconButton
              edge="end"
              color="inherit"
              onClick={handleCloseCategoriesModal}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 } }}>
            <Typography
              variant="body2"
              sx={{
                mb: 2,
                color: "rgba(0, 0, 0, 0.6)",
                fontSize: { xs: "0.875rem", sm: "1rem" },
              }}
            >
              Help customers find your business by industry.
            </Typography>

            <Formik
              initialValues={{
                primaryCategory:
                  profileData?.categories?.primaryCategory?.displayName || "",
                additionalCategories:
                  profileData?.categories?.additionalCategories?.map(
                    (cat) => cat.displayName
                  ) || [],
              }}
              onSubmit={(values) => {
                console.log("Categories updated:", values);
                handleCloseCategoriesModal();
                fetchBusinessProfile();
                setToastConfig(
                  ToastSeverity.Success,
                  "Business categories updated successfully",
                  true
                );
              }}
            >
              {({ values, setFieldValue }) => (
                <Form>
                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="subtitle2"
                      sx={{ mb: 1, fontWeight: 500 }}
                    >
                      Primary Category
                    </Typography>
                    <TextField
                      fullWidth
                      value={values.primaryCategory}
                      onChange={(e) =>
                        setFieldValue("primaryCategory", e.target.value)
                      }
                      placeholder="Enter primary business category"
                      sx={{ mb: 2 }}
                    />
                  </Box>

                  <Box sx={{ mb: 3 }}>
                    <Typography
                      variant="subtitle2"
                      sx={{ mb: 1, fontWeight: 500 }}
                    >
                      Additional Categories
                    </Typography>
                    {values.additionalCategories.map((category, index) => (
                      <Box
                        key={index}
                        sx={{ display: "flex", alignItems: "center", mb: 1 }}
                      >
                        <TextField
                          fullWidth
                          value={category}
                          onChange={(e) => {
                            const newCategories = [
                              ...values.additionalCategories,
                            ];
                            newCategories[index] = e.target.value;
                            setFieldValue(
                              "additionalCategories",
                              newCategories
                            );
                          }}
                          placeholder="Enter additional category"
                          sx={{ mr: 1 }}
                        />
                        <IconButton
                          onClick={() => {
                            const newCategories =
                              values.additionalCategories.filter(
                                (_, i) => i !== index
                              );
                            setFieldValue(
                              "additionalCategories",
                              newCategories
                            );
                          }}
                        >
                          <CloseIcon />
                        </IconButton>
                      </Box>
                    ))}
                    <Button
                      variant="outlined"
                      onClick={() => {
                        setFieldValue("additionalCategories", [
                          ...values.additionalCategories,
                          "",
                        ]);
                      }}
                      sx={{ mt: 1 }}
                    >
                      Add Category
                    </Button>
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 3,
                      borderTop: "1px solid rgba(0, 0, 0, 0.12)",
                      pt: 2,
                    }}
                  >
                    <Button
                      variant="outlined"
                      onClick={handleCloseCategoriesModal}
                      sx={{
                        textTransform: "none",
                        color: "#1976d2",
                        borderColor: "#1976d2",
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      type="submit"
                      sx={{
                        textTransform: "none",
                        bgcolor: "#1976d2",
                      }}
                    >
                      Save
                    </Button>
                  </Box>
                </Form>
              )}
            </Formik>
          </DialogContent>
        </Dialog>

        {/* Service Area Modal */}
        <Dialog
          open={isServiceAreaModalOpen}
          onClose={handleCloseServiceAreaModal}
          fullWidth
          maxWidth="md"
          PaperProps={{
            style: {
              backgroundColor: "#ffffff",
              color: "#2F2F2F",
              borderRadius: "8px",
              boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "20px 24px" },
              borderBottom: "1px solid #EBEBEB",
              bgcolor: "#EDEDED",
            }}
          >
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: "#2F2F2F",
                fontSize: { xs: "1.1rem", sm: "1.25rem" },
              }}
            >
              Service area
            </Typography>
            <IconButton
              edge="end"
              onClick={handleCloseServiceAreaModal}
              aria-label="close"
              sx={{
                color: "#2F2F2F",
                "&:hover": {
                  bgcolor: "rgba(47, 47, 47, 0.1)",
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent
            sx={{
              px: { xs: 2, sm: 3 },
              py: { xs: 2, sm: 2 },
              bgcolor: "#ffffff",
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="body2"
                sx={{
                  color: "#757575",
                  fontSize: { xs: "0.875rem", sm: "0.9rem" },
                  display: "inline",
                }}
              >
                Let customers know where your business provides deliveries or
                services.{" "}
              </Typography>
            </Box>

            <Formik
              initialValues={{ serviceAreas }}
              validationSchema={serviceAreaValidationSchema}
              onSubmit={handleSaveServiceAreas}
            >
              {({
                values,
                errors,
                touched,
                isValid,
                handleSubmit,
                setFieldValue,
              }) => (
                <Form onSubmit={handleSubmit}>
                  <Box sx={{ position: "relative", mb: 3 }}>
                    <TextField
                      fullWidth
                      placeholder="Search area (minimum 3 characters)"
                      value={searchArea}
                      onChange={(e) => handleServiceAreaSearch(e.target.value)}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "5px",
                          bgcolor: "#ffffff",
                          "& fieldset": {
                            borderColor: "#EBEBEB",
                          },
                          "&:hover fieldset": {
                            borderColor: "#309898",
                          },
                          "&.Mui-focused fieldset": {
                            borderColor: "#309898",
                          },
                        },
                        "& .MuiInputBase-input": {
                          color: "#2F2F2F",
                        },
                        "& .MuiInputBase-input::placeholder": {
                          color: "#757575",
                          opacity: 1,
                        },
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon sx={{ color: "#757575" }} />
                          </InputAdornment>
                        ),
                        endAdornment: loadingSuggestions && (
                          <InputAdornment position="end">
                            <CircularProgress
                              size={20}
                              sx={{ color: "#309898" }}
                            />
                          </InputAdornment>
                        ),
                      }}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          handleAddServiceArea(
                            searchArea,
                            setFieldValue,
                            values
                          );
                        }
                        if (e.key === "Escape") {
                          setShowSuggestions(false);
                        }
                      }}
                      onFocus={() => {
                        if (serviceAreaSuggestions.length > 0) {
                          setShowSuggestions(true);
                        }
                      }}
                    />

                    {/* Autocomplete Suggestions Dropdown */}
                    {showSuggestions && serviceAreaSuggestions.length > 0 && (
                      <Box
                        sx={{
                          position: "absolute",
                          top: "100%",
                          left: 0,
                          right: 0,
                          bgcolor: "#ffffff",
                          border: "1px solid #EBEBEB",
                          borderRadius: "5px",
                          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                          zIndex: 1000,
                          maxHeight: "200px",
                          overflowY: "auto",
                        }}
                      >
                        {serviceAreaSuggestions.map((suggestion, index) => (
                          <Box
                            key={suggestion.placeId}
                            onClick={() =>
                              handleSuggestionSelect(
                                suggestion,
                                setFieldValue,
                                values
                              )
                            }
                            sx={{
                              p: 2,
                              cursor: "pointer",
                              borderBottom:
                                index < serviceAreaSuggestions.length - 1
                                  ? "1px solid #EBEBEB"
                                  : "none",
                              "&:hover": {
                                bgcolor: "#f4f4f4",
                              },
                            }}
                          >
                            <Typography
                              variant="body2"
                              sx={{
                                color: "#2F2F2F",
                                fontWeight: 500,
                                mb: 0.5,
                              }}
                            >
                              {suggestion.mainText}
                            </Typography>
                            {suggestion.secondaryText && (
                              <Typography
                                variant="caption"
                                sx={{
                                  color: "#757575",
                                  fontSize: "0.75rem",
                                }}
                              >
                                {suggestion.secondaryText}
                              </Typography>
                            )}
                          </Box>
                        ))}
                      </Box>
                    )}
                  </Box>

                  {touched.serviceAreas && errors.serviceAreas && (
                    <Typography color="error" variant="body2" sx={{ mb: 2 }}>
                      {errors.serviceAreas}
                    </Typography>
                  )}

                  <Typography
                    variant="body2"
                    sx={{
                      mb: 1.5,
                      color: "black",
                      fontWeight: 500,
                      fontSize: "0.9rem",
                    }}
                  >
                    Selected service areas
                  </Typography>

                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      gap: 1,
                      mb: 3,
                      maxHeight: "300px",
                      overflowY: "auto",
                      p: 1,
                    }}
                  >
                    {values.serviceAreas.map((area, index) => (
                      <Chip
                        key={index}
                        label={area}
                        onDelete={() =>
                          handleRemoveServiceArea(area, setFieldValue, values)
                        }
                        sx={{
                          bgcolor: "#309898",
                          color: "#ffffff",
                          borderRadius: "16px",
                          fontWeight: 500,
                          "& .MuiChip-deleteIcon": {
                            color: "#ffffff",
                            "&:hover": {
                              color: "#f0f0f0",
                            },
                          },
                          "&:hover": {
                            bgcolor: "#267373",
                          },
                        }}
                      />
                    ))}
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "flex-start",
                      mt: 2,
                      gap: 2,
                    }}
                  >
                    <Button
                      variant="contained"
                      type="submit"
                      sx={{
                        textTransform: "none",
                        bgcolor: "#309898",
                        borderRadius: "5px",
                        px: 3,
                        color: "white",
                        fontWeight: 500,
                        "&:hover": {
                          bgcolor: "#267373",
                        },
                      }}
                    >
                      Save
                    </Button>
                    <Button
                      variant="outlined"
                      onClick={handleCloseServiceAreaModal}
                      sx={{
                        textTransform: "none",
                        color: "#757575",
                        borderColor: "#EBEBEB",
                        borderRadius: "5px",
                        px: 3,
                        bgcolor: "white",
                        fontWeight: 500,
                        "&:hover": {
                          bgcolor: "#f4f4f4",
                          borderColor: "#EBEBEB",
                        },
                      }}
                    >
                      Cancel
                    </Button>
                  </Box>
                </Form>
              )}
            </Formik>
          </DialogContent>
        </Dialog>

        {/* Business Description Modal */}
        <Dialog
          open={isDescriptionModalOpen}
          onClose={handleCloseDescriptionModal}
          fullWidth
          maxWidth="sm"
          PaperProps={{
            style: {
              backgroundColor: "white",
              color: "black",
              borderRadius: "8px",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "20px 24px" },
              borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
            }}
          >
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: "black",
                fontSize: { xs: "1.1rem", sm: "1.25rem" },
              }}
            >
              Business description
            </Typography>
            <IconButton
              edge="end"
              color="inherit"
              onClick={handleCloseDescriptionModal}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent sx={{ px: { xs: 2, sm: 3 }, py: { xs: 2, sm: 2 } }}>
            <Typography
              variant="body2"
              sx={{
                mb: 2,
                color: "rgba(0, 0, 0, 0.6)",
                fontSize: { xs: "0.875rem", sm: "1rem" },
              }}
            >
              Tell customers about your business. Focus on what makes it
              special.
            </Typography>

            <Formik
              initialValues={{
                description: profileData?.description || "",
              }}
              validationSchema={yup.object({
                description: yup
                  .string()
                  .max(750, "Description must be 750 characters or less"),
              })}
              onSubmit={(values) => {
                console.log("Description updated:", values);
                handleCloseDescriptionModal();
                fetchBusinessProfile();
                setToastConfig(
                  ToastSeverity.Success,
                  "Business description updated successfully",
                  true
                );
              }}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                isValid,
                handleSubmit,
                setFieldValue,
              }) => (
                <Form>
                  <Box sx={{ mb: 3 }}>
                    <TextField
                      fullWidth
                      name="description"
                      value={values.description}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched.description && Boolean(errors.description)}
                      helperText={touched.description && errors.description}
                      multiline
                      rows={10}
                      sx={{ mb: 1 }}
                      InputProps={{
                        style: { color: "black" },
                      }}
                    />
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        mt: 1,
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          color:
                            values.description.length > 750
                              ? "error.main"
                              : "text.secondary",
                        }}
                      >
                        {values.description.length}/750
                      </Typography>
                    </Box>
                  </Box>

                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mt: 2,
                      borderTop: "1px solid rgba(0, 0, 0, 0.12)",
                      pt: 2,
                    }}
                  >
                    <Button
                      variant="text"
                      startIcon={<EditIcon />}
                      onClick={() => handleGenerateDescription(setFieldValue)}
                      disabled={isGeneratingDescription}
                      sx={{
                        textTransform: "none",
                        color: "#1976d2",
                      }}
                    >
                      {isGeneratingDescription
                        ? "Generating..."
                        : "Suggest description"}
                    </Button>
                    <Box>
                      <Button
                        variant="outlined"
                        onClick={handleCloseDescriptionModal}
                        sx={{
                          mr: 2,
                          textTransform: "none",
                          color: "#1976d2",
                          borderColor: "#1976d2",
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        type="submit"
                        disabled={!isValid}
                        sx={{
                          textTransform: "none",
                          bgcolor: "#1976d2",
                        }}
                      >
                        Save
                      </Button>
                    </Box>
                  </Box>
                </Form>
              )}
            </Formik>
          </DialogContent>
        </Dialog>

        {/* Business Location Modal */}
        <Dialog
          open={isBusinessLocationModalOpen}
          onClose={handleCloseBusinessLocationModal}
          fullWidth
          maxWidth="md"
          PaperProps={{
            style: {
              backgroundColor: "white",
              borderRadius: "8px",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "20px 24px" },
              borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
              bgcolor: "white",
            }}
          >
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 500,
                color: "black",
                fontSize: { xs: "1.1rem", sm: "1.25rem" },
              }}
            >
              Location and areas
            </Typography>
            <IconButton
              edge="end"
              color="inherit"
              onClick={handleCloseBusinessLocationModal}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent
            sx={{
              padding: { xs: "16px", sm: "20px 24px" },
              bgcolor: "white",
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 500,
                color: "black",
                mb: 1,
              }}
            >
              Business location
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "text.secondary",
                mb: 3,
              }}
            >
              If customers visit your business, add an address and adjust the
              pin on the map to its location.
            </Typography>

            <Formik
              initialValues={businessLocationData}
              validationSchema={businessLocationValidationSchema}
              onSubmit={handleSaveBusinessLocation}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                isValid,
                handleSubmit,
                setFieldValue,
              }) => (
                <Form onSubmit={handleSubmit}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={values.showAddress}
                        onChange={(e) =>
                          setFieldValue("showAddress", e.target.checked)
                        }
                        sx={{
                          color: "#1976d2",
                          "&.Mui-checked": {
                            color: "#1976d2",
                          },
                        }}
                      />
                    }
                    label={
                      <Typography sx={{ color: "black", fontSize: "0.9rem" }}>
                        Show business address to customers
                      </Typography>
                    }
                    sx={{ mb: 2 }}
                  />

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Box sx={{ display: "flex", flexDirection: "column" }}>
                        <Typography
                          variant="body2"
                          sx={{ mb: 1, fontWeight: 500 }}
                        >
                          Country/Region
                        </Typography>
                        <FormControl fullWidth>
                          <Select
                            name="country"
                            value={values.country}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.country && Boolean(errors.country)}
                            sx={{ mb: 2 }}
                          >
                            <MenuItem value="India">India</MenuItem>
                            <MenuItem value="US">United States</MenuItem>
                            <MenuItem value="UK">United Kingdom</MenuItem>
                            <MenuItem value="CA">Canada</MenuItem>
                          </Select>
                        </FormControl>

                        <Typography
                          variant="body2"
                          sx={{ mb: 1, fontWeight: 500 }}
                        >
                          Street address
                        </Typography>
                        <TextField
                          name="streetAddress"
                          value={values.streetAddress}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={
                            touched.streetAddress &&
                            Boolean(errors.streetAddress)
                          }
                          helperText={
                            touched.streetAddress && errors.streetAddress
                          }
                          fullWidth
                          sx={{ mb: 2 }}
                        />

                        <Typography
                          variant="body2"
                          sx={{ mb: 1, fontWeight: 500 }}
                        >
                          Street address line 2 (optional)
                        </Typography>
                        <TextField
                          name="streetAddressLine2"
                          value={values.streetAddressLine2}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          fullWidth
                          sx={{ mb: 2 }}
                        />

                        <Typography
                          variant="body2"
                          sx={{ mb: 1, fontWeight: 500 }}
                        >
                          Town/City
                        </Typography>
                        <TextField
                          name="city"
                          value={values.city}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.city && Boolean(errors.city)}
                          helperText={touched.city && errors.city}
                          fullWidth
                          sx={{ mb: 2 }}
                        />

                        <Typography
                          variant="body2"
                          sx={{ mb: 1, fontWeight: 500 }}
                        >
                          Pincode
                        </Typography>
                        <TextField
                          name="pincode"
                          value={values.pincode}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          error={touched.pincode && Boolean(errors.pincode)}
                          helperText={touched.pincode && errors.pincode}
                          fullWidth
                          sx={{ mb: 2 }}
                        />

                        <Typography
                          variant="body2"
                          sx={{ mb: 1, fontWeight: 500 }}
                        >
                          State
                        </Typography>
                        <FormControl fullWidth>
                          <Select
                            name="state"
                            value={values.state}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            error={touched.state && Boolean(errors.state)}
                          >
                            <MenuItem value="Karnataka">Karnataka</MenuItem>
                            <MenuItem value="Maharashtra">Maharashtra</MenuItem>
                            <MenuItem value="Tamil Nadu">Tamil Nadu</MenuItem>
                            <MenuItem value="Delhi">Delhi</MenuItem>
                            <MenuItem value="Gujarat">Gujarat</MenuItem>
                            <MenuItem value="Rajasthan">Rajasthan</MenuItem>
                            <MenuItem value="Uttar Pradesh">
                              Uttar Pradesh
                            </MenuItem>
                            <MenuItem value="West Bengal">West Bengal</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Box
                        sx={{
                          height: "400px",
                          bgcolor: "#f5f5f5",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          borderRadius: 1,
                        }}
                      >
                        <Typography color="text.secondary">
                          Map location
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <DialogActions
                    sx={{
                      justifyContent: "space-between",
                      padding: "16px 0",
                      mt: 2,
                    }}
                  >
                    <Button
                      onClick={handleCloseBusinessLocationModal}
                      variant="outlined"
                      sx={{
                        color: "black",
                        borderColor: "rgba(0, 0, 0, 0.23)",
                        "&:hover": {
                          borderColor: "black",
                          backgroundColor: "rgba(0, 0, 0, 0.04)",
                        },
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={!isValid}
                      sx={{
                        bgcolor: "#1976d2",
                        "&:hover": {
                          bgcolor: "#1565c0",
                        },
                      }}
                    >
                      Save
                    </Button>
                  </DialogActions>
                </Form>
              )}
            </Formik>
          </DialogContent>
        </Dialog>

        {/* Business Categories Modal */}
        <AddBusinessCategoryModal
          open={isCategoriesModalOpen}
          onClose={handleCloseCategoriesModal}
          onAddCategory={handleSaveCategories}
        />

        {/* Services Modal */}
        <Dialog
          open={isServicesModalOpen}
          onClose={handleCloseServicesModal}
          fullWidth
          maxWidth="md"
          sx={{
            "& .MuiDialog-paper": {
              backgroundColor: "white",
              borderRadius: "8px",
              maxHeight: "90vh",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: { xs: "16px", sm: "20px 24px" },
              borderBottom: "1px solid #EBEBEB",
              bgcolor: "#EDEDED",
            }}
          >
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 600,
                color: "#2F2F2F",
                fontSize: { xs: "1.1rem", sm: "1.25rem" },
              }}
            >
              Services Management
            </Typography>
            <IconButton
              edge="end"
              onClick={handleCloseServicesModal}
              aria-label="close"
              sx={{
                color: "#757575",
                "&:hover": {
                  bgcolor: "#30989833",
                  color: "#309898",
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>

          <DialogContent
            sx={{
              px: { xs: 2, sm: 3 },
              py: { xs: 2, sm: 3 },
              bgcolor: "#ffffff",
              minHeight: "400px",
              maxHeight: "70vh",
              overflow: "auto",
            }}
          >
            {servicesCategories && servicesCategories.length > 0 ? (
              <ServicesDisplay
                categories={servicesCategories}
                onUpdateCategories={handleUpdateServicesCategories}
              />
            ) : (
              <Box sx={{ textAlign: "center", py: 4 }}>
                <Typography variant="h6" sx={{ color: "#757575", mb: 2 }}>
                  Loading services...
                </Typography>
                <Typography variant="body2" sx={{ color: "#757575" }}>
                  Please wait while we load your services data.
                </Typography>
              </Box>
            )}
          </DialogContent>
        </Dialog>

        {/* Business Hours Modal */}
        <BusinessHours
          isOpen={isBusinessHoursModalOpen}
          onClose={handleCloseBusinessHoursModal}
          initialValues={profileData?.regularHours || []}
          onSave={handleSaveBusinessHours}
          hideButton={true}
        />
      </Box>
    </LeftMenuComponent>
  );
};

export default BusinessProfileManagement;
