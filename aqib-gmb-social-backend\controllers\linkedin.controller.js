const logger = require("../utils/logger");
const LinkedIn = require("../models/linkedin.models");
const LinkedInService = require("../services/linkedin.service");

// Create LinkedIn service instance
const linkedinService = new LinkedInService();

/**
 * Welcome endpoint for LinkedIn API
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("linkedin", "welcome", req.requestId);

    res.status(200).json({
      success: true,
      message: "LinkedIn API is working",
      timestamp: new Date().toISOString(),
      endpoints: {
        authenticate: "POST /v1/linkedin/authenticate",
        callback: "GET /v1/linkedin/callback",
        callbackValidation: "POST /v1/linkedin/callback-validation",
        getPages: "GET /v1/linkedin/pages/:userId",
        createPost: "POST /v1/linkedin/posts/:userId",
      },
    });
  } catch (error) {
    logger.error("Error in LinkedIn welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Initiate LinkedIn authentication
 */
const authenticate = async (req, res) => {
  try {
    logger.logControllerAction("linkedin", "authenticate", req.requestId);

    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    logger.info("LinkedIn authentication initiated", {
      requestId: req.requestId,
      userId,
    });

    // Validate LinkedIn configuration
    if (!linkedinService.validateConfig()) {
      return res.status(500).json({
        success: false,
        message: "LinkedIn configuration is incomplete",
      });
    }

    const authUrl = linkedinService.generateAuthUrl(userId);

    logger.info("LinkedIn authentication URL generated", {
      requestId: req.requestId,
      userId,
      hasAuthUrl: !!authUrl,
    });

    res.status(200).json({
      success: true,
      authUrl: authUrl,
      message: "LinkedIn authentication URL generated successfully",
    });
  } catch (error) {
    logger.error("Error in LinkedIn authenticate", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Handle LinkedIn OAuth callback
 */
const callback = async (req, res) => {
  try {
    logger.logControllerAction("linkedin", "callback", req.requestId);

    const { code, state, error, error_description } = req.query;

    // Handle OAuth errors
    if (error) {
      logger.error("LinkedIn OAuth error", {
        requestId: req.requestId,
        error,
        error_description,
      });

      const frontendUrl = process.env.FRONTEND_URL;
      return res.redirect(
        `${frontendUrl}/business-management/linkedin/callback?error=${encodeURIComponent(
          error
        )}&error_description=${encodeURIComponent(error_description || "")}`
      );
    }

    if (!code || !state) {
      logger.error("Missing code or state in LinkedIn callback", {
        requestId: req.requestId,
        hasCode: !!code,
        hasState: !!state,
      });

      const frontendUrl = process.env.FRONTEND_URL;
      return res.redirect(
        `${frontendUrl}/business-management/linkedin/callback?error=missing_parameters`
      );
    }

    // Redirect to frontend callback page with code and state
    const frontendUrl = process.env.FRONTEND_URL;
    res.redirect(
      `${frontendUrl}/business-management/linkedin/callback?code=${encodeURIComponent(
        code
      )}&state=${encodeURIComponent(state)}`
    );
  } catch (error) {
    logger.error("Error in LinkedIn callback", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    const frontendUrl = process.env.FRONTEND_URL;
    res.redirect(
      `${frontendUrl}/business-management/linkedin/callback?error=server_error`
    );
  }
};

/**
 * Handle LinkedIn OAuth callback validation
 */
const callbackValidation = async (req, res) => {
  try {
    logger.logControllerAction("linkedin", "callbackValidation", req.requestId);

    const { code, state } = req.body;

    if (!code || !state) {
      return res.status(400).json({
        success: false,
        message: "Authorization code and state are required",
      });
    }

    let userId;
    try {
      const stateData = JSON.parse(state);
      userId = stateData.userId;
    } catch (error) {
      logger.error("Invalid state parameter", {
        requestId: req.requestId,
        state,
        error: error.message,
      });
      return res.status(400).json({
        success: false,
        message: "Invalid state parameter",
      });
    }

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID not found in state",
      });
    }

    // Exchange authorization code for access token
    const tokenData = await linkedinService.exchangeCodeForToken(code);

    if (!tokenData || !tokenData.access_token) {
      return res.status(400).json({
        success: false,
        message: "Failed to exchange authorization code for access token",
      });
    }

    // Get LinkedIn user profile
    const linkedinUserInfo = await linkedinService.getUserProfile(
      tokenData.access_token
    );

    if (!linkedinUserInfo) {
      return res.status(400).json({
        success: false,
        message: "Failed to get LinkedIn user profile",
      });
    }

    // Save OAuth tokens with LinkedIn user info
    const tokenResult = await LinkedIn.saveOAuthTokens({
      userId,
      linkedinUserId: linkedinUserInfo.id,
      linkedinUserName:
        linkedinUserInfo.localizedFirstName +
        " " +
        linkedinUserInfo.localizedLastName,
      linkedinUserEmail: linkedinUserInfo.emailAddress,
      linkedinUserPicture: linkedinUserInfo.profilePicture?.displayImage,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || null,
      expiresAt: tokenData.expires_in
        ? new Date(Date.now() + tokenData.expires_in * 1000)
        : null,
    });

    logger.info("LinkedIn authentication completed successfully", {
      requestId: req.requestId,
      userId,
      linkedinUserId: linkedinUserInfo.id,
      linkedinUserEmail: linkedinUserInfo.emailAddress,
    });

    res.status(200).json({
      success: true,
      message: "LinkedIn authentication successful",
      data: {
        userId,
        linkedinUserId: linkedinUserInfo.id,
        linkedinUserName:
          linkedinUserInfo.localizedFirstName +
          " " +
          linkedinUserInfo.localizedLastName,
        linkedinUserEmail: linkedinUserInfo.emailAddress,
      },
    });
  } catch (error) {
    logger.error("Error in LinkedIn callback validation", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get LinkedIn accounts for user
 */
const getLinkedInAccounts = async (req, res) => {
  try {
    logger.logControllerAction(
      "linkedin",
      "getLinkedInAccounts",
      req.requestId
    );

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const result = await LinkedIn.getLinkedInAccounts(parseInt(userId));

    if (!result.success) {
      return res.status(404).json({
        success: false,
        message: "No LinkedIn accounts found",
      });
    }

    logger.info("LinkedIn accounts retrieved successfully", {
      requestId: req.requestId,
      userId,
      accountsCount: result.accounts.length,
    });

    res.status(200).json({
      success: true,
      accounts: result.accounts,
      message: "LinkedIn accounts retrieved successfully",
    });
  } catch (error) {
    logger.error("Error getting LinkedIn accounts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get LinkedIn pages/organizations for user
 */
const getPages = async (req, res) => {
  try {
    logger.logControllerAction("linkedin", "getPages", req.requestId);

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    const result = await LinkedIn.getLinkedInAccounts(parseInt(userId));

    if (!result.success || result.accounts.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No LinkedIn accounts found",
      });
    }

    // For LinkedIn, we'll use the user's profile as the "page"
    const pages = result.accounts.map((account) => ({
      id: account.linkedin_user_id,
      name: account.linkedin_user_name,
      email: account.linkedin_user_email,
      picture: account.linkedin_user_picture,
      type: "profile",
    }));

    logger.info("LinkedIn pages retrieved successfully", {
      requestId: req.requestId,
      userId,
      pagesCount: pages.length,
    });

    res.status(200).json({
      success: true,
      pages: pages,
      pagesCount: pages.length,
      message: "LinkedIn pages retrieved successfully",
    });
  } catch (error) {
    logger.error("Error getting LinkedIn pages", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Create LinkedIn post
 */
const createPost = async (req, res) => {
  try {
    logger.logControllerAction("linkedin", "createPost", req.requestId);

    const { userId } = req.params;
    const { profileId, text, media, published, scheduledPublishTime } =
      req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    if (!profileId || !text) {
      return res.status(400).json({
        success: false,
        message: "Profile ID and text are required",
      });
    }

    // Get user's LinkedIn access token
    const accountsResult = await LinkedIn.getLinkedInAccounts(parseInt(userId));
    const account = accountsResult.accounts.find(
      (acc) => acc.linkedin_user_id === profileId
    );

    if (!account) {
      return res.status(404).json({
        success: false,
        message: "LinkedIn account not found",
      });
    }

    // Create post using LinkedIn service
    const postResult = await linkedinService.createPost({
      accessToken: account.access_token,
      profileId: profileId,
      text: text,
      media: media,
      published: published !== false, // Default to true
      scheduledPublishTime: scheduledPublishTime,
    });

    if (!postResult.success) {
      return res.status(400).json({
        success: false,
        message: postResult.message || "Failed to create LinkedIn post",
        error: postResult.error,
      });
    }

    // Save post to database
    const postData = {
      userId: parseInt(userId),
      profileId: profileId,
      postId: postResult.postId,
      text: text,
      media: media ? JSON.stringify(media) : null,
      published: published !== false,
      scheduledPublishTime: scheduledPublishTime,
      linkedinResponse: JSON.stringify(postResult.response),
    };

    await LinkedIn.savePost(postData);

    logger.info("LinkedIn post created successfully", {
      requestId: req.requestId,
      userId,
      profileId,
      postId: postResult.postId,
    });

    res.status(200).json({
      success: true,
      postId: postResult.postId,
      message: "LinkedIn post created successfully",
      data: postResult.response,
    });
  } catch (error) {
    logger.error("Error creating LinkedIn post", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getLinkedInAccounts,
  getPages,
  createPost,
};
