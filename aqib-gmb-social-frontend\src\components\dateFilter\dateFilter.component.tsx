import React, { useEffect, useState } from "react";
import {
  Box,
  Menu,
  Radio,
  RadioGroup,
  FormControlLabel,
  Typography,
  OutlinedInput,
  InputLabel,
  FormControl,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import dayjs, { Dayjs } from "dayjs";
import { INPUT_FIELD_STYLES } from "../../constants/styles.constant";

interface Props {
  onDateChange: (range: {
    from: string;
    to: string;
    isSameMonthYear: boolean;
  }) => void;
  disableFutureDates?: boolean;
  requireDateValidation?: boolean;
}

const durationLabels: Record<string, string> = {
  all: "All Time",
  "7": "Past 7 days",
  "30": "Past 30 days",
  "90": "Past 90 days",
  "180": "Past 180 days",
  custom: "Pick Date Range",
};

const DateFilter: React.FC<Props> = ({
  onDateChange,
  disableFutureDates = false,
  requireDateValidation = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDuration, setSelectedDuration] = useState("all");
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);
  const [dateError, setDateError] = useState<string | null>(null);

  const open = Boolean(anchorEl);
  const isCustomRange = selectedDuration === "custom";

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setTimeout(() => handleApply(), 100); // Delay to avoid conflict with selection events
  };

  useEffect(() => {
    console.log(selectedDuration);
    handleDurationChange(selectedDuration);
    setTimeout(() => {
      onDateChange({
        from: dayjs().subtract(1, "year").format("YYYY-MM-DD"),
        to: dayjs().format("YYYY-MM-DD"),
        isSameMonthYear: false,
      });
    }, 1000);
  }, []);

  const handleDurationChange = (value: string) => {
    setSelectedDuration(value);
    setDateError(null);

    const end = dayjs();
    let start: Dayjs | null = null;

    switch (value) {
      case "7":
        start = end.subtract(7, "day");
        break;
      case "30":
        start = end.subtract(30, "day");
        break;
      case "90":
        start = end.subtract(90, "day");
        break;
      case "180":
        start = end.subtract(180, "day");
        break;
      case "all":
        start = dayjs().subtract(1, "year"); // Arbitrary wide range
        break;
      case "custom":
        setFromDate(null);
        setToDate(null);
        return;
    }

    setFromDate(start);
    setToDate(end);
  };

  const validateDates = () => {
    if (isCustomRange && fromDate && toDate) {
      // Check if from date is after to date
      if (fromDate.isAfter(toDate)) {
        setDateError("From date cannot be after To date.");
        return false;
      }

      // Check for future dates if validation is required
      if (disableFutureDates) {
        const today = dayjs().startOf("day");
        if (fromDate.isAfter(today)) {
          setDateError("From date cannot be in the future.");
          return false;
        }
        if (toDate.isAfter(today)) {
          setDateError("To date cannot be in the future.");
          return false;
        }
      }
    }

    // Additional validation for performance reports
    if (requireDateValidation && isCustomRange && fromDate && toDate) {
      if (!fromDate.isBefore(toDate)) {
        setDateError("To date must be greater than From date.");
        return false;
      }
    }

    setDateError(null);
    return true;
  };

  const handleApply = () => {
    if (!validateDates()) return;

    if (fromDate && toDate) {
      // Check if month and year are the same
      if (
        fromDate.year() === toDate.year() &&
        fromDate.month() === toDate.month() &&
        selectedDuration === "custom"
      ) {
        const startOfMonth = fromDate.startOf("month");
        const endOfMonth = fromDate.endOf("month");
        onDateChange({
          from: startOfMonth.format("YYYY-MM-DD"),
          to: endOfMonth.format("YYYY-MM-DD"),
          isSameMonthYear: true,
        });
      } else {
        onDateChange({
          from: fromDate.format("YYYY-MM-DD"),
          to: toDate.format("YYYY-MM-DD"),
          isSameMonthYear: false,
        });
      }
    }
    setAnchorEl(null);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <FormControl variant="outlined" fullWidth>
        <InputLabel htmlFor="outlined-select-date">Duration</InputLabel>
        <OutlinedInput
          id="outlined-select-date"
          onClick={handleClick}
          value={durationLabels[selectedDuration]}
          endAdornment={<ArrowDropDownIcon />}
          label="Duration"
          sx={{
            cursor: "pointer",
            ...INPUT_FIELD_STYLES,
          }}
          readOnly
        />
      </FormControl>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "left" }}
        transformOrigin={{ vertical: "top", horizontal: "left" }}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              borderRadius: 2,
              p: 2,
              width: 270,
            },
          },
        }}
      >
        <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
          Duration
        </Typography>

        <RadioGroup
          value={selectedDuration}
          onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
            handleDurationChange(event.target.value)
          }
        >
          {Object.entries(durationLabels).map(([value, label]) => (
            <FormControlLabel
              key={value}
              value={value}
              control={<Radio />}
              label={label}
            />
          ))}
        </RadioGroup>

        {isCustomRange && (
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <DatePicker
              label="From"
              value={fromDate}
              onChange={(newValue) => setFromDate(newValue)}
              views={["year", "month", "day"]} // Show full date picker for performance reports
              maxDate={disableFutureDates ? dayjs() : undefined}
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  error: Boolean(dateError),
                  sx: INPUT_FIELD_STYLES,
                },
              }}
            />
            <DatePicker
              label="To"
              value={toDate}
              minDate={fromDate || undefined}
              maxDate={disableFutureDates ? dayjs() : undefined}
              onChange={(newValue) => setToDate(newValue)}
              views={["year", "month", "day"]} // Show full date picker for performance reports
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  error: Boolean(dateError),
                  helperText: dateError,
                  sx: INPUT_FIELD_STYLES,
                },
              }}
            />
          </Box>
        )}
      </Menu>
    </LocalizationProvider>
  );
};

export default DateFilter;
