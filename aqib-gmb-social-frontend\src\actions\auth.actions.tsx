import { <PERSON><PERSON>GIN, USER_ROLES } from "../constants/endPoints.constant";
import { ILoginModel } from "../interfaces/request/ILoginModel";
import HttpHelperService from "../services/httpHelper.service";
import { useContext, createRef } from "react";
import {
  AUTH_REQUESTED,
  AUTH_SUCCESS,
  AUTH_ERROR,
  AUTH_LOGOUT,
  AUTH_UNAUTHORIZED,
} from "../constants/reducer.constant";
import axios from "axios";
import { Action, Dispatch } from "redux";
import { AnyAction } from "redux";
import ApplicationHelperService from "../services/helperService";
import { ISignInResponseModel } from "../interfaces/response/ISignInResponseModel";

const _applicationHelperService = new ApplicationHelperService({});

export const authInitiate =
  (payload: ILoginModel, setLoading: (isLoading: boolean) => void) =>
  async (dispatch: Dispatch<Action>) => {
    try {
      const _httpHelperService = new HttpHelperService(dispatch);
      setLoading(true); // Start loading
      dispatch({
        type: AUTH_REQUESTED,
        payload: payload,
      });

      _httpHelperService
        .login(payload)
        .then(async (response: ISignInResponseModel) => {
          let isLoginSuccess =
            response.message === "Success" && response.result !== null;

          if (isLoginSuccess) {
            const roleAccessResponse = await _httpHelperService.get(
              `${USER_ROLES(response.result.id)}`
            );

            localStorage.setItem(
              "MyLocoBiz_UserInfo",
              JSON.stringify(response)
            );

            dispatch({
              type: AUTH_SUCCESS,
              payload: { ...response, rbAccess: roleAccessResponse.list[0] },
            });
          } else {
            dispatch({
              type: AUTH_ERROR,
              payload: response,
            });
          }
        })
        .catch((error) => {
          console.log(error);
          dispatch({
            type: AUTH_ERROR,
            payload: error.response?.data?.message || "Login failed",
          });
        })
        .finally(() => {
          setLoading(false); // Always stop loading
        });
    } catch (error) {
      dispatch({
        type: AUTH_ERROR,
        payload: error,
      });
      setLoading(false); // In case of try-block error
    }
  };

export const updateUserData =
  (payload: any) => async (dispatch: Dispatch<Action>) => {
    try {
      dispatch({
        type: AUTH_SUCCESS,
        payload: payload,
      });
    } catch (error) {
      dispatch({
        type: AUTH_ERROR,
        payload: error,
      });
    }
  };

export const logOut = () => (dispatch: Dispatch) => {
  localStorage.removeItem("MyLocoBiz_UserInfo");
  dispatch({
    type: AUTH_LOGOUT,
    payload: null,
  });
};

export const sessionExpired = () => (dispatch: Dispatch) => {
  localStorage.removeItem("MyLocoBiz_UserInfo");
  dispatch({
    type: AUTH_UNAUTHORIZED,
    payload: { message: "" },
  });
};
