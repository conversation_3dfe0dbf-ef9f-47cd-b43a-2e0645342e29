#!/usr/bin/env node

/**
 * Test script for Review Sync Service
 * This script allows manual testing of the review sync functionality
 */

require("dotenv").config();

const database = require("./config/database");
const reviewSyncService = require("./services/reviewSyncService");
const logger = require("./utils/logger");

class ReviewSyncTester {
  constructor() {
    this.startTime = new Date();
  }

  /**
   * Initialize and run the test
   */
  async run() {
    try {
      console.log("=".repeat(60));
      console.log("GMB Review Sync Service - Manual Test");
      console.log("=".repeat(60));
      console.log(`Started at: ${this.startTime.toISOString()}`);
      console.log("");

      // Initialize database connection
      await this.initializeDatabase();

      // Display current configuration
      await this.displayConfiguration();

      // Run the review sync
      await this.runReviewSync();

      // Display results
      await this.displayResults();

      console.log("");
      console.log("=".repeat(60));
      console.log("Test completed successfully!");
      console.log("=".repeat(60));

    } catch (error) {
      console.error("Test failed:", error.message);
      logger.error("Review sync test failed:", {
        error: error.message,
        stack: error.stack,
      });
      process.exit(1);
    } finally {
      // Close database connections
      await database.close();
    }
  }

  /**
   * Initialize database connection
   */
  async initializeDatabase() {
    try {
      console.log("Initializing database connection...");
      await database.initialize();
      console.log("✓ Database connection established");
    } catch (error) {
      console.error("✗ Database initialization failed:", error.message);
      throw error;
    }
  }

  /**
   * Display current configuration
   */
  async displayConfiguration() {
    console.log("");
    console.log("Configuration:");
    console.log("-".repeat(40));
    console.log(`Backend API URL: ${process.env.BACKEND_API_URL}`);
    console.log(`Service User ID: ${process.env.SERVICE_USER_ID}`);
    console.log(`Has Auth Token: ${!!process.env.SERVICE_AUTH_TOKEN}`);
    console.log(`Batch Size: ${process.env.BATCH_SIZE || 50}`);
    console.log(`Review Sync Schedule: ${process.env.REVIEW_SYNC_SCHEDULE || "0 23 * * *"}`);

    // Get service status
    const status = reviewSyncService.getStatus();
    console.log(`Service Configured: ${status.isConfigured ? "✓" : "✗"}`);
    
    if (!status.isConfigured) {
      console.log("⚠️  Service is not properly configured!");
      console.log("Please check your environment variables.");
    }
  }

  /**
   * Run the review sync process
   */
  async runReviewSync() {
    console.log("");
    console.log("Starting review sync process...");
    console.log("-".repeat(40));

    try {
      const startTime = Date.now();
      const results = await reviewSyncService.syncAllReviews();
      const duration = Date.now() - startTime;

      console.log(`✓ Review sync completed in ${duration}ms`);
      
      return results;
    } catch (error) {
      console.error("✗ Review sync failed:", error.message);
      throw error;
    }
  }

  /**
   * Display sync results
   */
  async displayResults() {
    console.log("");
    console.log("Sync Results:");
    console.log("-".repeat(40));

    const stats = reviewSyncService.getStats();
    
    console.log(`Total OAuth Tokens Processed: ${stats.totalTokensProcessed}`);
    console.log(`Total Locations Processed: ${stats.totalLocationsProcessed}`);
    console.log(`Total Reviews Synced: ${stats.totalReviewsSynced}`);
    console.log(`Successful Syncs: ${stats.successfulSyncs}`);
    console.log(`Failed Syncs: ${stats.failedSyncs}`);
    
    if (stats.duration) {
      console.log(`Total Duration: ${stats.duration}ms`);
    }

    if (stats.errors && stats.errors.length > 0) {
      console.log("");
      console.log("Errors encountered:");
      console.log("-".repeat(20));
      stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.error} (${error.context})`);
        if (error.accountId) console.log(`   Account: ${error.accountId}`);
        if (error.locationId) console.log(`   Location: ${error.locationId}`);
        console.log(`   Time: ${error.timestamp}`);
        console.log("");
      });
    }

    // Display recent sync operations from database
    try {
      const reviewSyncModel = require("./models/reviewSyncModel");
      const recentOps = await reviewSyncModel.getRecentSyncOperations(10);
      
      if (recentOps && recentOps.length > 0) {
        console.log("");
        console.log("Recent Sync Operations (from database):");
        console.log("-".repeat(40));
        
        recentOps.forEach((op, index) => {
          console.log(`${index + 1}. ${op.gmbLocationName || op.location_id}`);
          console.log(`   Status: ${op.sync_status}`);
          console.log(`   Reviews: ${op.reviews_count}`);
          console.log(`   Duration: ${op.sync_duration_ms}ms`);
          console.log(`   Time: ${op.created_at}`);
          if (op.error_message) {
            console.log(`   Error: ${op.error_message}`);
          }
          console.log("");
        });
      }
    } catch (error) {
      console.log("Could not fetch recent operations from database:", error.message);
    }
  }

  /**
   * Display help information
   */
  static displayHelp() {
    console.log("GMB Review Sync Service - Test Script");
    console.log("");
    console.log("Usage:");
    console.log("  node test-review-sync.js");
    console.log("");
    console.log("This script will:");
    console.log("  1. Initialize database connection");
    console.log("  2. Display current configuration");
    console.log("  3. Run the review sync process");
    console.log("  4. Display detailed results");
    console.log("");
    console.log("Environment Variables:");
    console.log("  BACKEND_API_URL - Backend API base URL");
    console.log("  SERVICE_USER_ID - Service user ID for authentication");
    console.log("  SERVICE_AUTH_TOKEN - Authentication token");
    console.log("  BATCH_SIZE - Number of locations to process in batch");
    console.log("  REVIEW_SYNC_SCHEDULE - Cron schedule for automatic sync");
    console.log("");
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes("--help") || args.includes("-h")) {
  ReviewSyncTester.displayHelp();
  process.exit(0);
}

// Create and run the tester
const tester = new ReviewSyncTester();

// Handle graceful shutdown
process.on("SIGINT", async () => {
  console.log("\nReceived SIGINT, shutting down gracefully...");
  await database.close();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\nReceived SIGTERM, shutting down gracefully...");
  await database.close();
  process.exit(0);
});

// Run the test
tester.run().catch((error) => {
  console.error("Test failed:", error);
  process.exit(1);
});
