# GMB Review Sync Service

This document describes the Review Sync functionality that has been added to the GMB Auto Reply Service.

## Overview

The Review Sync Service automatically synchronizes Google My Business reviews for all configured OAuth tokens and locations. It runs as a scheduled job within the existing GMB Auto Reply Service.

## Features

- **Automated Daily Sync**: Runs every night at 11:00 PM UTC by default
- **Comprehensive Coverage**: Syncs reviews for all active OAuth tokens and their associated locations
- **Database Logging**: Tracks sync operations with detailed logs
- **Error Handling**: Continues processing even if individual locations fail
- **Manual Testing**: Includes test script for manual execution
- **Statistics Tracking**: Provides detailed statistics and performance metrics

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Review Sync Configuration
REVIEW_SYNC_SCHEDULE=0 23 * * *
# Sync all reviews daily at 11:00 PM UTC

# Backend API Configuration (required)
BACKEND_API_URL=http://localhost:3000
SERVICE_USER_ID=52
SERVICE_AUTH_TOKEN=your_auth_token_here

# Optional Configuration
BATCH_SIZE=50
BACKEND_API_TIMEOUT=300000
```

### Schedule Configuration

The sync schedule uses cron format:
- `0 23 * * *` - Daily at 11:00 PM UTC (default)
- `0 2 * * *` - Daily at 2:00 AM UTC
- `0 23 * * 0` - Weekly on Sunday at 11:00 PM UTC
- `0 23 1 * *` - Monthly on the 1st at 11:00 PM UTC

## How It Works

### 1. Token Discovery
- Fetches all active OAuth tokens from `gmb_oauth_tokens` table
- Filters for tokens with `statusId = 1`

### 2. Location Processing
- For each OAuth token, retrieves associated locations from `gmb_locations` table
- Processes each location individually

### 3. Review Synchronization
- Calls the existing `/v1/gmb-reviews/reviews-list` API endpoint
- Uses the same logic as the manual sync button in the frontend
- Inserts new reviews or updates existing ones in `gmb_reviews` table

### 4. Logging and Statistics
- Logs each sync operation to `review_sync_log` table
- Tracks success/failure rates, review counts, and performance metrics
- Maintains detailed error logs for troubleshooting

## Database Schema

### review_sync_log Table

```sql
CREATE TABLE review_sync_log (
  id INT AUTO_INCREMENT PRIMARY KEY,
  account_id VARCHAR(255) NOT NULL,
  location_id VARCHAR(255) NOT NULL,
  user_id INT NOT NULL,
  sync_status ENUM('success', 'failed', 'skipped') NOT NULL,
  reviews_count INT DEFAULT 0,
  error_message TEXT,
  sync_duration_ms INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_account_id (account_id),
  INDEX idx_location_id (location_id),
  INDEX idx_user_id (user_id),
  INDEX idx_sync_status (sync_status),
  INDEX idx_created_at (created_at)
);
```

## Usage

### Automatic Execution

The service runs automatically according to the configured schedule. No manual intervention is required.

### Manual Testing

To test the review sync functionality manually:

```bash
# Run the test script
npm run test-review-sync

# Or directly
node test-review-sync.js
```

The test script will:
1. Display current configuration
2. Run the sync process
3. Show detailed results and statistics
4. Display recent sync operations from the database

### Service Management

```bash
# Start the service (includes review sync)
npm start

# Development mode with auto-restart
npm run dev

# Check service status
npm run status
```

## Monitoring

### Log Files

Review sync operations are logged to:
- `logs/combined.log` - All log entries
- `logs/error.log` - Error entries only
- Database `review_sync_log` table - Structured sync operation logs

### Statistics

The service provides comprehensive statistics:

```javascript
{
  lastRunTime: "2024-01-15T23:00:00.000Z",
  totalTokensProcessed: 5,
  totalLocationsProcessed: 25,
  totalReviewsSynced: 150,
  successfulSyncs: 23,
  failedSyncs: 2,
  errors: [...],
  isRunning: false,
  duration: 45000
}
```

### Health Monitoring

The service includes health checks that run hourly and log:
- Memory usage
- Active job status
- Sync statistics
- System uptime

## Error Handling

### Resilient Processing
- Individual location failures don't stop the entire sync process
- OAuth token issues are logged but don't prevent processing other tokens
- Network timeouts are handled gracefully with retries

### Error Types
- **Authentication Errors**: Invalid or expired OAuth tokens
- **API Errors**: Backend API unavailable or returning errors
- **Database Errors**: Connection issues or query failures
- **Network Errors**: Timeout or connectivity issues

### Recovery
- Failed syncs are logged with detailed error messages
- The service will retry on the next scheduled run
- Manual testing can be used to diagnose specific issues

## Troubleshooting

### Common Issues

1. **No OAuth Tokens Found**
   - Check that users have completed Google OAuth authentication
   - Verify `gmb_oauth_tokens` table has active records (`statusId = 1`)

2. **Authentication Failures**
   - Verify `SERVICE_AUTH_TOKEN` is valid and not expired
   - Check that `SERVICE_USER_ID` exists and has proper permissions

3. **API Connection Issues**
   - Verify `BACKEND_API_URL` is correct and accessible
   - Check network connectivity between sync service and backend API
   - Increase `BACKEND_API_TIMEOUT` if requests are timing out

4. **Database Connection Issues**
   - Verify database credentials in `.env` file
   - Check database server availability
   - Ensure required tables exist

### Debugging

1. **Enable Debug Logging**
   ```env
   LOG_LEVEL=debug
   ```

2. **Run Manual Test**
   ```bash
   npm run test-review-sync
   ```

3. **Check Recent Sync Operations**
   ```sql
   SELECT * FROM review_sync_log 
   ORDER BY created_at DESC 
   LIMIT 20;
   ```

4. **Monitor Service Logs**
   ```bash
   tail -f logs/combined.log
   ```

## Performance Considerations

### Batch Processing
- Processes locations sequentially to avoid overwhelming the API
- Configurable batch size via `BATCH_SIZE` environment variable
- Built-in delays and timeouts to prevent rate limiting

### Resource Usage
- Memory usage scales with the number of OAuth tokens and locations
- Database connections are pooled and reused
- Logs are automatically rotated to prevent disk space issues

### Optimization Tips
- Schedule sync during low-traffic hours (default: 11:00 PM UTC)
- Monitor sync duration and adjust timeout settings if needed
- Clean up old sync logs periodically (automated after 30 days)

## Security

### Authentication
- Uses the same authentication mechanism as the main backend
- Service account credentials are stored securely in environment variables
- OAuth tokens are handled according to existing security practices

### Data Protection
- Sensitive data is sanitized in logs
- Database connections use encrypted connections when available
- API calls include proper authentication headers

## Support

For issues or questions regarding the Review Sync Service:

1. Check the logs for error details
2. Run the manual test script for diagnostics
3. Review the database sync logs for patterns
4. Contact the development team with specific error messages and timestamps
