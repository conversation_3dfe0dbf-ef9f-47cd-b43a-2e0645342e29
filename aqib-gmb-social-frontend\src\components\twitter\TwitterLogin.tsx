import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  <PERSON>alogContent,
  DialogActions,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from "@mui/material";
import TwitterIcon from "@mui/icons-material/Twitter";
import { useSelector, useDispatch } from "react-redux";
import TwitterService from "../../services/twitter/twitter.service";

interface TwitterLoginProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (userData: any) => void;
  onError?: (error: string) => void;
}

const TwitterLogin: React.FC<TwitterLoginProps> = ({
  open,
  onClose,
  onSuccess,
  onError,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const dispatch = useDispatch();

  const _twitterService = new TwitterService(dispatch);

  const handleTwitterLogin = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!userInfo?.id) {
        throw new Error("User information not available");
      }

      // Get Twitter authentication URL
      const authResponse = await _twitterService.authenticate(userInfo.id);

      if (!authResponse.success) {
        throw new Error(
          authResponse.message || "Failed to initiate Twitter authentication"
        );
      }

      // Open Twitter authentication popup
      try {
        const authResult = await _twitterService.openAuthPopup(
          authResponse.data.authUrl
        );

        // Validate the callback
        const validationResponse = await _twitterService.validateCallback(
          authResult.code,
          authResult.state
        );

        if (validationResponse.success) {
          if (onSuccess) {
            onSuccess(validationResponse.data.user);
          }
          onClose();
        } else {
          throw new Error(
            validationResponse.message || "Authentication validation failed"
          );
        }
      } catch (popupError: any) {
        if (popupError.message === "Authentication cancelled") {
          // User cancelled authentication, don't show error
          setError(null);
        } else {
          throw popupError;
        }
      }
    } catch (error: any) {
      console.error("Twitter authentication error:", error);
      const errorMessage =
        error.message || "Failed to authenticate with Twitter";
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ textAlign: "center", pb: 1 }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mb: 1,
          }}
        >
          <TwitterIcon sx={{ fontSize: 32, color: "#1DA1F2", mr: 1 }} />
          <Typography variant="h5" component="span">
            Connect Twitter Account
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ textAlign: "center", py: 2 }}>
          <Typography variant="body1" sx={{ mb: 3, color: "text.secondary" }}>
            Connect your Twitter account to start creating and managing tweets
            directly from our platform.
          </Typography>

          <Box
            sx={{
              p: 3,
              backgroundColor: "#f8f9fa",
              borderRadius: 2,
              border: "1px solid #e9ecef",
              mb: 3,
            }}
          >
            <Typography variant="h6" sx={{ mb: 2, color: "#1DA1F2" }}>
              What you can do:
            </Typography>
            <Box sx={{ textAlign: "left" }}>
              <Typography
                variant="body2"
                sx={{ mb: 1, display: "flex", alignItems: "center" }}
              >
                • Create and publish tweets
              </Typography>
              <Typography
                variant="body2"
                sx={{ mb: 1, display: "flex", alignItems: "center" }}
              >
                • Upload images and videos
              </Typography>
              <Typography
                variant="body2"
                sx={{ mb: 1, display: "flex", alignItems: "center" }}
              >
                • Schedule tweets for later
              </Typography>
              <Typography
                variant="body2"
                sx={{ mb: 1, display: "flex", alignItems: "center" }}
              >
                • Manage multiple Twitter accounts
              </Typography>
              <Typography
                variant="body2"
                sx={{ display: "flex", alignItems: "center" }}
              >
                • Track tweet performance
              </Typography>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3, textAlign: "left" }}>
              {error}
            </Alert>
          )}

          <Typography
            variant="caption"
            sx={{ color: "text.secondary", display: "block", mb: 2 }}
          >
            By connecting your Twitter account, you agree to our terms of
            service and Twitter's API terms of use.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ justifyContent: "center", pb: 3 }}>
        <Button
          onClick={handleClose}
          disabled={loading}
          variant="outlined"
          sx={{ mr: 2, minWidth: 120 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleTwitterLogin}
          disabled={loading}
          variant="contained"
          startIcon={
            loading ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              <TwitterIcon />
            )
          }
          sx={{
            backgroundColor: "#1DA1F2",
            "&:hover": { backgroundColor: "#1a91da" },
            "&:disabled": { backgroundColor: "grey.300" },
            minWidth: 180,
          }}
        >
          {loading ? "Connecting..." : "Connect Twitter"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TwitterLogin;
