import {
  FunctionComponent,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Link from "@mui/material/Link";
import React from "react";
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Button,
  Switch,
  FormControlLabel,
  LinearProgress,
  CircularProgress,
  Drawer,
  Grid,
  Paper,
  Tooltip,
  Card,
  CardContent,
  FormControl,
} from "@mui/material";

//Css Import
import "../signIn/signIn.screen.style.css";

import * as yup from "yup";
import { Formik, FormikErrors, FormikTouched } from "formik";
import { ILoginModel } from "../../interfaces/request/ILoginModel";
import { useDispatch, useSelector } from "react-redux";
import { authInitiate } from "../../actions/auth.actions";
import SyncOutlinedIcon from "@mui/icons-material/SyncOutlined";
import CheckCircleOutlineOutlinedIcon from "@mui/icons-material/CheckCircleOutlineOutlined";
import {
  LocalizationProvider,
  MobileDateTimePicker,
} from "@mui/x-date-pickers";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs"; // Day.js library
import InfoCard from "./components/InfoCard.screen";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import SubmitPost, {
  IModalWithSelect,
  ISelectionLocationWithPost,
} from "./components/submitPost.component";
import { IGoogleCreatePost } from "../../interfaces/request/IGoogleCreatePost";
import { ToastContext } from "../../context/toast.context";
import { LoadingContext } from "../../context/loading.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import {
  GooglePostValidationSchema,
  FacebookPostValidationSchema,
  InstagramPostValidationSchema,
  LinkedInPostValidationSchema,
  validatePlatformData,
} from "./validations/platformValidations";
import GooglePostStatusDialog from "./components/GooglePostStatusDialog";
import FacebookPostStatusDialog from "./components/FacebookPostStatusDialog";
import InstagramPostStatusDialog from "./components/InstagramPostStatusDialog";
import LinkedInPostStatusDialog from "./components/LinkedInPostStatusDialog";
import GooglePostForm from "./components/googlePostForm.component";
import GooglePostPreview from "./components/googlePostPreview.component";
import { EVENT_TYPES, TOPIC_TYPES } from "../../constants/application.constant";
import {
  CalendarToday,
  ImageOutlined as ImageOutlinedIcon,
} from "@mui/icons-material";
import {
  IconButton,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import CallIcon from "@mui/icons-material/Call";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import ShoppingCartCheckoutIcon from "@mui/icons-material/ShoppingCartCheckout";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import SchoolIcon from "@mui/icons-material/School";
import { Campaign, Web } from "@mui/icons-material";
import { getIn } from "formik";
import utc from "dayjs/plugin/utc";
import { CardMedia } from "@mui/material";
import { ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import PostsService from "../../services/posts/posts.service";
import { IFileUploadResponseModel } from "../../interfaces/response/IFileUploadResponseModel";
import { styled } from "@mui/system";
import VisibilityIcon from "@mui/icons-material/Visibility";
import BlockIcon from "@mui/icons-material/Block";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import CancelIcon from "@mui/icons-material/Cancel";
import { useLocation, useNavigate } from "react-router-dom";
import { useSearchParams } from "react-router-dom";
import LocalActivityIcon from "@mui/icons-material/LocalActivity";
import ThumbUpAltIcon from "@mui/icons-material/ThumbUpAlt";
import LinkIcon from "@mui/icons-material/Link";
import ScheduleLater from "../../components/scheduleLater/scheduleLater.component";
import GenericDrawer from "../../components/genericDrawer/genericDrawer.component";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import { IExtendedPageProps } from "../../interfaces/IExtendedPageProps";
import GallerySelectionComponent from "./components/gallerySelection.component";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import InstagramLogin from "../../components/instagram/InstagramLogin";
import FacebookLogin from "../../components/facebook/FacebookLogin";
import FacebookPostForm from "./components/facebookPostForm.component";
import InstagramPostForm from "./components/instagramPostForm.component";
import FacebookService from "../../services/facebook/facebook.service";
import InstagramService from "../../services/instagram/instagram.service";
import {
  IFacebookCreatePost,
  IFacebookSelectedPage,
} from "../../interfaces/request/IFacebookCreatePost";
import {
  IInstagramCreatePost,
  IInstagramSelectedAccount,
} from "../../interfaces/request/IInstagramCreatePost";
import { IFacebookPageData } from "../../interfaces/response/IFacebookCreatePostResponse";
import { IInstagramAccountData } from "../../interfaces/response/IInstagramCreatePostResponse";
import FacebookPostPreview from "../../components/facebook/FacebookPostPreview";
import InstagramPostPreview from "../../components/instagram/InstagramPostPreview";
import LinkedInLogin from "../../components/linkedin/LinkedInLogin";
import LinkedInPostForm from "./components/linkedinPostForm.component";
import LinkedInService from "../../services/linkedin/linkedin.service";
import {
  ILinkedInCreatePost,
  ILinkedInSelectedProfile,
} from "../../services/linkedin/linkedin.service";
import { ILinkedInPageData } from "../../services/linkedin/linkedin.service";
import LinkedInPostPreview from "../../components/linkedin/LinkedInPostPreview";
import TwitterPostForm from "./components/twitterPostForm.component";
import TwitterService from "../../services/twitter/twitter.service";
import {
  ITwitterCreatePost,
  ITwitterSelectedAccount,
} from "../../interfaces/request/ITwitterCreatePost";
import { ITwitterAccountData } from "../../interfaces/response/ITwitterCreatePostResponse";
import TwitterPostPreview from "../../components/twitter/TwitterPostPreview";
import TwitterPostStatusDialog from "./components/TwitterPostStatusDialog";

dayjs.extend(utc);

const DEBUG_MODE = import.meta.env.MODE === "development";

const FormErrorDebugger = ({
  errors,
  touched,
}: {
  errors: any;
  touched: any;
}) => {
  if (!DEBUG_MODE) return null;

  const renderErrorMessage = (message: any): string => {
    if (typeof message === "string") {
      return message;
    } else if (typeof message === "object" && message !== null) {
      return JSON.stringify(message);
    }
    return String(message);
  };

  return (
    <Box
      sx={{
        position: "fixed",
        bottom: 0,
        right: 0,
        width: "300px",
        maxHeight: "300px",
        overflowY: "auto",
        backgroundColor: "rgba(255, 0, 0, 0.1)",
        padding: 2,
        zIndex: 9999,
        border: "1px solid red",
      }}
    >
      <Typography variant="subtitle2" fontWeight="bold">
        Form Errors:
      </Typography>
      {Object.keys(errors).length === 0 ? (
        <Typography variant="body2">No errors</Typography>
      ) : (
        Object.entries(errors).map(([field, message]) => (
          <Box key={field} sx={{ mb: 1 }}>
            <Typography variant="caption" fontWeight="bold">
              {field}:
            </Typography>
            <Typography variant="caption" display="block" color="error">
              {renderErrorMessage(message)}
              {touched[field] ? " (touched)" : " (not touched)"}
            </Typography>
          </Box>
        ))
      )}
    </Box>
  );
};

type PostCreationProgressIndicator = {
  percent: number;
  status: string;
};

const CreateSocialPost: FunctionComponent<IExtendedPageProps> = ({
  title,
  createPost,
}) => {
  const iconMap: { [key: string]: JSX.Element } = {
    CallIcon: <CallIcon />,
    CalendarMonthIcon: <CalendarMonthIcon />,
    ShoppingCartIcon: <ShoppingCartIcon />,
    ShoppingCartCheckoutIcon: <ShoppingCartCheckoutIcon />,
    PersonAddIcon: <PersonAddIcon />,
    SchoolIcon: <SchoolIcon />,
  };
  const navigate = useNavigate();
  const location = useLocation();
  const formikSchedulerRef = useRef(null);
  const formikRef = useRef<any>(null);
  const fileInputRef = useRef(null);
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const [tabValue, setTabValue] = useState(1);

  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const { setToastConfig } = useContext(ToastContext);
  const { setLoading } = useContext(LoadingContext);
  const [date, setDate] = React.useState(dayjs());
  const [scheduleForLater, setScheduleForLater] = useState<boolean>(false);
  const handleClosePost = () => setShowLocationSelection({ isShow: false });
  const [showLocationSelection, setShowLocationSelection] =
    useState<IModalWithSelect>({
      isShow: false,
    });

  const [uploadedImages, setUploadedImages] = useState<unknown[]>([]);
  const [showCreatePostStatus, setShowCreatePostStatus] =
    useState<boolean>(false);
  const [selectedLocations, setSelectedLocations] = useState<
    ISelectionLocationWithPost[]
  >([]);
  const [gallerySelectionOpen, setGallerySelectionOpen] =
    useState<boolean>(false);
  const [selectedFromGallery, setSelectedFromGallery] = useState<any>(null);
  const [showInstagramLogin, setShowInstagramLogin] = useState(false);
  const [showFacebookLogin, setShowFacebookLogin] = useState(false);
  const [facebookPages, setFacebookPages] = useState<IFacebookPageData[]>([]);
  const [isFacebookConnected, setIsFacebookConnected] = useState(false);
  const [instagramAccounts, setInstagramAccounts] = useState<
    IInstagramAccountData[]
  >([]);
  const [isInstagramConnected, setIsInstagramConnected] = useState(false);
  const [linkedinProfiles, setLinkedinProfiles] = useState<ILinkedInPageData[]>(
    []
  );
  const [isLinkedinConnected, setIsLinkedinConnected] = useState(false);
  const [twitterAccounts, setTwitterAccounts] = useState<ITwitterAccountData[]>(
    []
  );
  const [isTwitterConnected, setIsTwitterConnected] = useState(false);
  const _postsService = new PostsService(dispatch);
  const _facebookService = new FacebookService(dispatch);
  const _instagramService = new InstagramService(dispatch);
  const _linkedinService = new LinkedInService(dispatch);
  const _twitterService = new TwitterService(dispatch);

  // Generate a unique bulk post ID
  const generateBulkPostId = () => {
    return (
      "bulk_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11)
    );
  };
  const MIN_ALLOWED_CHARS = 1;
  const MAX_ALLOWED_CHARS = 1500;

  const [postCreationProgress, setPostCreationProgress] =
    useState<PostCreationProgressIndicator>({
      percent: 30,
      status: "",
    });

  // Track when all API calls are completed (success or failure)
  const [allApiCallsCompleted, setAllApiCallsCompleted] =
    useState<boolean>(false);

  const topic = searchParams.get("topic");
  const domainRegex =
    /^(https?:\/\/)?((([a-z\d]([a-z\d-]*[a-z\d])*)\.)+[a-z]{2,})(:\d+)?(\/[-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(#[-a-z\d_]*)?$/i;

  const iconTextInputStyle = {
    paddingLeft: "14px", // Font size
  };

  const CREATE_POST_INITIAL_POST: IGoogleCreatePost = {
    languageCode: "en-US",
    summary: "",
    event: {
      title: "",
      schedule: {
        startTime: "",
        endTime: "",
      },
    },
    offer: null,
    media: [],
    topicType:
      topic &&
      [
        TOPIC_TYPES.Offer,
        TOPIC_TYPES.WhatsNew,
        TOPIC_TYPES.Event,
        // TOPIC_TYPES.Informative,
        // TOPIC_TYPES.Standard,
      ].includes(topic.toUpperCase())
        ? topic
        : TOPIC_TYPES.WhatsNew,
    callToAction: {
      actionType: EVENT_TYPES.CallNow,
      url: "",
    },
  };

  const [createPostInitials, setCreatePostInitials] =
    useState<IGoogleCreatePost>(CREATE_POST_INITIAL_POST);

  // Edit mode states
  const [isEditMode, setIsEditMode] = useState(false);
  const [isBulkEditMode, setIsBulkEditMode] = useState(false);
  const [editTitle, setEditTitle] = useState("Create Posts");

  // Facebook form initial state
  const FACEBOOK_POST_INITIAL: IFacebookCreatePost = {
    pageId: "",
    message: "",
    description: "",
    link: "",
    media: [],
    published: true,
  };

  const [facebookFormData, setFacebookFormData] = useState<IFacebookCreatePost>(
    FACEBOOK_POST_INITIAL
  );

  // Facebook multi-page selection state
  const [selectedFacebookPages, setSelectedFacebookPages] = useState<
    IFacebookSelectedPage[]
  >([]);

  // Instagram form initial state
  const INSTAGRAM_POST_INITIAL: IInstagramCreatePost = {
    accountId: "",
    caption: "",
    mediaUrl: "",
    mediaType: "image",
    published: true,
  };

  const [instagramFormData, setInstagramFormData] =
    useState<IInstagramCreatePost>(INSTAGRAM_POST_INITIAL);

  // Instagram multi-account selection state
  const [selectedInstagramAccounts, setSelectedInstagramAccounts] = useState<
    IInstagramSelectedAccount[]
  >([]);

  // LinkedIn form initial state
  const LINKEDIN_POST_INITIAL: ILinkedInCreatePost = {
    profileId: "",
    text: "",
    media: [],
    published: true,
  };

  const [linkedinFormData, setLinkedinFormData] = useState<ILinkedInCreatePost>(
    LINKEDIN_POST_INITIAL
  );

  // LinkedIn multi-profile selection state
  const [selectedLinkedinProfiles, setSelectedLinkedinProfiles] = useState<
    ILinkedInSelectedProfile[]
  >([]);

  // Twitter form initial state
  const TWITTER_POST_INITIAL: ITwitterCreatePost = {
    accountId: "",
    text: "",
    mediaUrls: [],
  };

  const [twitterFormData, setTwitterFormData] =
    useState<ITwitterCreatePost>(TWITTER_POST_INITIAL);

  // Twitter multi-account selection state
  const [selectedTwitterAccounts, setSelectedTwitterAccounts] = useState<
    ITwitterSelectedAccount[]
  >([]);

  useEffect(() => {
    if (location.state && location.state.createPost) {
      setCreatePostInitials(location.state.createPost);
      setIsEditMode(true);
      setEditTitle(location.state.title || "Edit Post");
      setTabValue(1); // Force Google tab in edit mode

      // If we're editing a post and it has media, convert them to mock File objects
      if (
        location.state.createPost.media &&
        location.state.createPost.media.length > 0
      ) {
        // Use setTimeout to prevent blocking the main thread during file creation
        setTimeout(() => {
          const existingMediaFiles = location.state.createPost.media.map(
            (mediaItem: any, index: number) => {
              // Create a mock File object for existing media
              const mockFile = new File(
                [""],
                `existing-image-${index + 1}.jpg`,
                {
                  type: "image/jpeg",
                  lastModified: Date.now(),
                }
              );

              // Add custom properties to track this is from existing post
              (mockFile as any).isFromGallery = true;
              (mockFile as any).isFromExistingPost = true;
              (mockFile as any).s3Url = mediaItem.sourceUrl;
              (mockFile as any).mediaFormat = mediaItem.mediaFormat;
              (mockFile as any).original_file_name = `existing-image-${
                index + 1
              }.jpg`;

              // Override size property for validation
              Object.defineProperty(mockFile, "size", {
                value: 1024 * 1024, // 1MB placeholder size
                writable: false,
              });

              return mockFile;
            }
          );

          setUploadedImages(existingMediaFiles);
        }, 0);
      }
    } else if (location.state && location.state.bulkEdit) {
      setIsBulkEditMode(true);
      setIsEditMode(true);
      setEditTitle(location.state.title || "Edit Bulk Post");
      setTabValue(1); // Force Google tab in bulk edit mode
    } else {
      setIsEditMode(false);
      setIsBulkEditMode(false);
      setEditTitle("Create Posts");
    }

    document.title = title;
  }, []);

  // No need to manually reset form - enableReinitialize handles this

  // Handle tab parameter from URL
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab) {
      const tabNumber = parseInt(tab, 10);
      if (tabNumber >= 1 && tabNumber <= 5) {
        setTabValue(tabNumber);
        // Show info message if accessing Facebook tab without connection
        if (tabNumber === 2 && !isFacebookConnected) {
          setToastConfig(
            ToastSeverity.Info,
            "Please connect your Facebook account to create posts.",
            true
          );
        }
        // Show info message if accessing Instagram tab without connection
        if (tabNumber === 3 && !isInstagramConnected) {
          setToastConfig(
            ToastSeverity.Info,
            "Please connect your Instagram account to create posts.",
            true
          );
        }
        // Show info message if accessing Twitter tab without connection
        if (tabNumber === 4 && !isTwitterConnected) {
          setToastConfig(
            ToastSeverity.Info,
            "Please connect your Twitter account to create posts.",
            true
          );
        }
        // Show info message if accessing LinkedIn tab without connection
        if (tabNumber === 5 && !isLinkedinConnected) {
          setToastConfig(
            ToastSeverity.Info,
            "Please connect your LinkedIn account to create posts.",
            true
          );
        }
      }
    }
  }, [
    searchParams,
    isFacebookConnected,
    isInstagramConnected,
    isLinkedinConnected,
  ]);

  // Load Facebook pages on component mount if user is authenticated
  useEffect(() => {
    const loadFacebookPages = async () => {
      try {
        const response = await _facebookService.getPages(userInfo?.id);
        if (response.success && response.pages && response.pages.length > 0) {
          setFacebookPages(response.pages);
          setIsFacebookConnected(true);
          console.log(
            `Loaded ${response.pages.length} Facebook pages on mount`
          );
        } else {
          setIsFacebookConnected(false);
        }
      } catch (error) {
        // Silently fail - user probably hasn't connected Facebook yet
        setIsFacebookConnected(false);
        console.log("No Facebook pages found on mount (user not connected)");
      }
    };

    const loadInstagramAccounts = async () => {
      try {
        const response = await _instagramService.getAccounts(userInfo?.id);
        if (
          response?.success &&
          response.data?.accounts &&
          response.data.accounts.length > 0
        ) {
          setInstagramAccounts(response.data.accounts);
          setIsInstagramConnected(true);
          console.log(
            `Loaded ${response.data.accounts.length} Instagram accounts on mount`
          );
        } else {
          setIsInstagramConnected(false);
        }
      } catch (error) {
        // Silently fail - user probably hasn't connected Instagram yet
        setIsInstagramConnected(false);
        console.log(
          "No Instagram accounts found on mount (user not connected)"
        );
      }
    };

    const loadLinkedinProfiles = async () => {
      try {
        const response = await _linkedinService.getPages(userInfo?.id);
        if (response?.success && response.pages && response.pages.length > 0) {
          setLinkedinProfiles(response.pages);
          setIsLinkedinConnected(true);

          // Initialize selected profiles
          const initialSelectedProfiles = response.pages.map((profile) => ({
            id: profile.id,
            name: profile.name,
            email: profile.email,
            picture: profile.picture,
            selected: false,
          }));
          setSelectedLinkedinProfiles(initialSelectedProfiles);

          console.log(
            `Loaded ${response.pages.length} LinkedIn profiles on mount`
          );
        } else {
          setIsLinkedinConnected(false);
        }
      } catch (error) {
        // Silently fail - user probably hasn't connected LinkedIn yet
        setIsLinkedinConnected(false);
        console.log("No LinkedIn profiles found on mount (user not connected)");
      }
    };

    const loadTwitterAccounts = async () => {
      try {
        const response = await _twitterService.getAccounts(userInfo?.id);
        if (response && response.data && response.data.length > 0) {
          setTwitterAccounts(response.data);
          setIsTwitterConnected(true);
          console.log("Twitter accounts loaded:", response.data);
        } else {
          setTwitterAccounts([]);
          setIsTwitterConnected(false);
        }
      } catch (error) {
        setTwitterAccounts([]);
        setIsTwitterConnected(false);
        console.log("No Twitter accounts found on mount (user not connected)");
      }
    };

    if (userInfo?.id) {
      loadFacebookPages();
      loadInstagramAccounts();
      loadLinkedinProfiles();
      loadTwitterAccounts();
    }
  }, [userInfo?.id]);

  type EventType = (typeof EVENT_TYPES)[keyof typeof EVENT_TYPES];

  // Old global validation schema - now replaced with platform-specific validations
  /*
  const CreatePostSchema = yup.object().shape({
    event: yup
      .object()
      .nullable()
      .when("$topicType", (topicType, schema) => {
        if (
          topicType &&
          (topicType[0] === TOPIC_TYPES.Event ||
            topicType[0] === TOPIC_TYPES.Offer)
        ) {
          return schema.nonNullable().required("Event is required");
        }
        return schema; // Keep it nullable for other types
      })
      .shape({
        title: yup
          .string()
          .nullable()
          .transform((value) => (value === "" ? null : value))
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Event) {
              return schema.nonNullable().required("Title is required");
            }
            return schema; // Keep it nullable for other types
          })
          .test({
            name: "mandatory-check-when-event",
            message: "This field is required",
            test: function (value) {
              const { from } = this;
              const objectValues = from as any;
              const values = objectValues[objectValues.length - 1]
                .value as IGoogleCreatePost;

              // Validate only if topicType is "Event"
              if (values.topicType === TOPIC_TYPES.Event) {
                return Boolean(value && value.trim().length > 0);
              }
              return true; // Skip validation for other types
            },
          }),
        schedule: yup.object().shape({
          startTime: yup
            .string()
            .nullable()
            .when("$topicType", (topicType, schema) => {
              if (
                topicType &&
                (topicType[0] === TOPIC_TYPES.Event ||
                  topicType[0] === TOPIC_TYPES.Offer)
              ) {
                return schema.nonNullable().required("Start Time is required");
              }
              return schema; // Keep it nullable for other types
            })
            .test({
              name: "mandatory-check-start-date",
              message: "StartDate is required",
              test: function (value) {
                const { from } = this;
                const objectValues = from as any;
                const values = objectValues[objectValues.length - 1]
                  .value as IGoogleCreatePost;

                // Validate only if topicType is "Event"
                if (
                  values.topicType === TOPIC_TYPES.Event ||
                  values.topicType === TOPIC_TYPES.Offer
                ) {
                  return Boolean(value && value.trim().length > 0);
                }
                return true; // Skip validation for other types
              },
            }),
          endTime: yup
            .string()
            .nullable()
            .when("$topicType", (topicType, schema) => {
              if (
                topicType &&
                (topicType[0] === TOPIC_TYPES.Event ||
                  topicType[0] === TOPIC_TYPES.Offer)
              ) {
                return schema.nonNullable().required("End Time is required");
              }
              return schema; // Keep it nullable for other types
            })
            .test({
              name: "mandatory-check-end-date",
              message: "EndDate is required",
              test: function (value) {
                const { from } = this;
                const objectValues = from as any;
                const values = objectValues[objectValues.length - 1]
                  .value as IGoogleCreatePost;

                // Validate only if topicType is "Event or Offer"
                if (
                  values.topicType === TOPIC_TYPES.Event ||
                  values.topicType === TOPIC_TYPES.Offer
                ) {
                  return Boolean(value && value.trim().length > 0);
                }
                return true; // Skip validation for other types
              },
            }),
        }),
      }),
    offer: yup
      .object()
      .nullable()
      .when("$topicType", (topicType, schema) => {
        if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
          return schema.nonNullable().required("Offer is required");
        }
        return schema; // Keep it nullable for other types
      })
      .shape({
        couponCode: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
              return schema.nonNullable().required("Coupon Code is required");
            }
            return schema; // Keep it nullable for other types
          }), // Required check
        redeemOnlineUrl: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
              return schema
                .nonNullable()
                .required("Online Redeem Url is required");
            }
            return schema; // Keep it nullable for other types
          }), // Required check
        termsConditions: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
              return schema
                .nonNullable()
                .required("Terms & Conditions is required");
            }
            return schema; // Keep it nullable for other types
          }), // Required check
      }),
    summary: yup
      .string()
      .required("Summary is required")
      .test(
        "len",
        `Should me maximum of ${MAX_ALLOWED_CHARS} characters`,
        (val) => val.length <= MAX_ALLOWED_CHARS
      ),
    media: yup
      .array()
      .min(1, "At least one image is required")
      .test("fileSize", "Each file must be less than 5MB", (files) =>
        files
          ? files.every(
              (file) =>
                (file as any).isFromGallery ||
                (file as any).isFromExistingPost ||
                file.size <= 5 * 1024 * 1024
            )
          : true
      )
      .test("fileFormat", "Only JPG and PNG are allowed", (files) =>
        files
          ? files.every(
              (file) =>
                (file as any).isFromGallery ||
                (file as any).isFromExistingPost ||
                ["image/jpeg", "image/png"].includes(file.type)
            )
          : true
      ),
    callToAction: yup
      .object()
      .nullable()
      .when("$topicType", (topicType, schema) => {
        if (topicType && topicType[0] === TOPIC_TYPES.Event) {
          return schema.nonNullable().required("Event is required for Event");
        }
        return schema; // Keep it nullable for other types
      })
      .shape({
        actionType: yup
          .string()
          .nullable()
          .when("$callToAction.actionType", (actionType, schema) => {
            if (
              actionType &&
              Object.values(EVENT_TYPES).includes(actionType[0] as EventType)
            ) {
              return schema.nonNullable().required("Action is required");
            }
            return schema; // Keep it nullable for other types
          }),
        url: yup
          .string()
          .nullable()
          .when("$callToAction.actionType", (actionType, schema) => {
            if (
              actionType &&
              actionType[0] &&
              actionType[0] !== EVENT_TYPES.Call
            ) {
              return schema
                .nonNullable()
                .matches(domainRegex, "Invalid domain format")
                .required("Url is required");
            }
            return schema; // Keep it nullable for other types
          }),
      }),
  });
  */

  // Platform-specific submit handlers
  const handleGooglePostSubmit = async (formValues: any) => {
    try {
      // Validate Google post data
      await validatePlatformData("google", {
        ...formValues,
        media: uploadedImages,
        $topicType: [formValues.topicType],
        $hasCallToAction: [formValues.callToAction !== null],
      });

      if (isEditMode) {
        handleEditPost(formValues as IGoogleCreatePost);
      } else {
        setShowLocationSelection({
          isShow: true,
          createPostModel: {
            googleRequest: formValues as IGoogleCreatePost,
            schedule: null,
            images: uploadedImages,
          },
        });
      }
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.errors
          ? error.errors.join(", ")
          : "Google post validation failed",
        true
      );
    }
  };

  const handleFacebookPostSubmit = async () => {
    try {
      // Validate Facebook post data
      await validatePlatformData("facebook", {
        message: facebookFormData.message,
        description: facebookFormData.description,
        link: facebookFormData.link,
        selectedPages: selectedFacebookPages,
        $hasLink: [
          facebookFormData.link !== null && facebookFormData.link !== "",
        ],
      });

      // If validation passes, submit the Facebook post
      await submitFacebookPost();
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.errors
          ? error.errors.join(", ")
          : "Facebook post validation failed",
        true
      );
    }
  };

  const handleInstagramPostSubmit = async () => {
    try {
      // Validate Instagram post data
      await validatePlatformData("instagram", {
        caption: instagramFormData.caption,
        media: uploadedImages,
        hashtags: instagramFormData.hashtags || [],
        selectedAccounts: selectedInstagramAccounts,
      });

      // Handle Instagram post submission logic here
      setToastConfig(
        ToastSeverity.Success,
        "Instagram post validation passed! Ready to submit.",
        true
      );
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.errors
          ? error.errors.join(", ")
          : "Instagram post validation failed",
        true
      );
    }
  };

  const handleLinkedInPostSubmit = async () => {
    try {
      // Validate LinkedIn post data
      await validatePlatformData("linkedin", {
        text: linkedinFormData.text,
        title: linkedinFormData.title,
        description: linkedinFormData.description,
        link: linkedinFormData.link,
        selectedProfiles: selectedLinkedinProfiles,
        $hasLink: [
          linkedinFormData.link !== null && linkedinFormData.link !== "",
        ],
      });

      // Handle LinkedIn post submission logic here
      setToastConfig(
        ToastSeverity.Success,
        "LinkedIn post validation passed! Ready to submit.",
        true
      );
    } catch (error: any) {
      setToastConfig(
        ToastSeverity.Error,
        error.errors
          ? error.errors.join(", ")
          : "LinkedIn post validation failed",
        true
      );
    }
  };

  const handleEditPost = async (values: IGoogleCreatePost) => {
    try {
      debugger;
      setLoading(true);

      // Prepare update data
      const updateData = {
        summary: values.summary,
        topicType: values.topicType,
        languageCode: values.languageCode,
        postContent: values,
        callToAction: values.callToAction,
        event: values.event,
        offer: values.offer,
        media: uploadedImages.map((img: any) => ({
          mediaFormat: img.mediaFormat || "IMAGE",
          sourceUrl: img.s3Url || img.sourceUrl,
        })),
      };

      if (isBulkEditMode) {
        // Handle bulk edit mode
        const bulkPostId = location.state?.bulkPostId;

        if (!bulkPostId) {
          throw new Error("Bulk post ID not found");
        }

        const response = await _postsService.updateBulkPosts(
          userInfo.id,
          bulkPostId,
          updateData
        );

        if (response.isSuccess) {
          const { data } = response;
          const successMessage =
            data.failedUpdates > 0
              ? `Updated ${data.successfulUpdates} of ${data.totalPosts} posts. ${data.failedUpdates} failed.`
              : `Successfully updated all ${data.successfulUpdates} posts!`;

          setToastConfig(
            data.failedUpdates > 0
              ? ToastSeverity.Warning
              : ToastSeverity.Success,
            successMessage,
            true
          );

          // Show detailed results if there were failures
          if (data.failedUpdates > 0) {
            console.log("Failed updates:", data.errors);
          }

          // Navigate back to posts list
          navigate("/post-management/posts");
        } else {
          throw new Error(response.message || "Failed to update bulk posts");
        }
      } else {
        // Handle single post edit
        const googlePostName =
          location.state?.googlePostName ||
          location.state?.createPost?.name ||
          location.state?.postName;

        if (!googlePostName) {
          throw new Error("Post identifier not found");
        }

        // Determine if this is a single edit from bulk post
        const isSingleEdit = location.state?.isSingleEdit || false;

        const response = await _postsService.updatePost(
          userInfo.id,
          googlePostName,
          updateData,
          isSingleEdit
        );

        if (response.isSuccess) {
          setToastConfig(
            ToastSeverity.Success,
            response.message || "Post updated successfully!",
            true
          );

          // Navigate back to posts list
          navigate("/post-management/posts");
        } else {
          throw new Error(response.message || "Failed to update post");
        }
      }
    } catch (error) {
      console.error("Error updating post:", error);
      setToastConfig(
        ToastSeverity.Error,
        error.message || "Failed to update post. Please try again.",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const _handlePostSubmission = async (
    values: IGoogleCreatePost,
    formikHelpers: any
  ) => {
    if (values.topicType === TOPIC_TYPES.Event) {
      formikHelpers.setTouched({
        event: {
          title: true, // Manually mark nested field as touched
          schedule: {
            startTime: true,
            endTime: true,
          },
        },
        summary: true,
      });
    } else if (values.topicType === TOPIC_TYPES.Offer) {
      formikHelpers.setTouched({
        event: {
          title: true, // Manually mark nested field as touched
          schedule: {
            startTime: true,
            endTime: true,
          },
        },
        offer: {
          couponCode: true,
          redeemOnlineUrl: true,
          termsConditions: true,
        },
        summary: true,
      });
    } else {
      formikHelpers.setTouched({
        summary: true,
      });
    }

    try {
      await CreatePostSchema.validate(values, {
        abortEarly: false,
      }); // Validate form

      if (isEditMode) {
        // Handle edit mode - call edit API directly
        await handleEditPost(values);
      } else {
        // Handle create mode - show location selection
        setShowLocationSelection({
          isShow: true,
          createPostModel: {
            googleRequest: values,
            schedule: null,
            images: uploadedImages,
          },
        });
      }
      console.log("Form Submitted Successfully!", values);
    } catch (errors: any) {
      if (errors.inner) {
        console.log("Validation Errors:", errors.inner);
        errors.inner.forEach((error: any) => {
          console.log(`Field: ${error.path}, Message: ${error.message}`);
        });
      } else {
        console.log("Unexpected Error:", errors);
      }
    }
  };

  const handleClick = () => {
    if (fileInputRef && fileInputRef.current) {
      (fileInputRef.current as any).click(); // Trigger file input click
    }
  };

  const handleGallerySelection = () => {
    setGallerySelectionOpen(true);
  };

  const handleImageSelectFromGallery = async (
    _imageUrl: string,
    asset: any,
    setFieldValue?: any
  ) => {
    try {
      // Instead of fetching the image, we'll create a mock File object
      // and store the asset info for later use in form submission
      const mockFile = new File([""], asset.original_file_name, {
        type: asset.mime_type,
        lastModified: Date.now(),
      });

      // Add custom properties to track this is from gallery
      (mockFile as any).isFromGallery = true;
      (mockFile as any).s3Url = asset.s3_url;
      (mockFile as any).assetId = asset.id;
      (mockFile as any).original_file_name = asset.original_file_name;

      // Override size property for validation (gallery images are already validated)
      Object.defineProperty(mockFile, "size", {
        value: asset.file_size,
        writable: false,
      });

      // Set the selected image as uploaded images
      setUploadedImages([mockFile]);
      setSelectedFromGallery(asset);

      // Update Instagram form data with mediaUrl if on Instagram tab
      if (tabValue === 3) {
        setInstagramFormData((prev) => ({
          ...prev,
          mediaUrl: asset.s3_url,
          mediaType: asset.file_type === "video" ? "video" : "image",
        }));
      }

      // Update form field value if setFieldValue is available
      if (setFieldValue) {
        setFieldValue("media", [mockFile]);
      }

      setToastConfig(
        ToastSeverity.Success,
        `${asset.file_type === "video" ? "Video" : "Image"} "${
          asset.original_file_name
        }" selected from gallery`,
        true
      );
    } catch (error) {
      console.error("Error selecting image from gallery:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to select image from gallery",
        true
      );
    }
  };

  const PostTypeTabs = (props: { value: any; onChange: any }) => (
    <Tabs
      className="whiteBg"
      value={props.value}
      onChange={props.onChange}
      sx={{ borderBottom: "1px solid #ddd" }}
    >
      {Boolean(rbAccess && rbAccess.Google) && (
        <Tab
          label={
            <Box sx={{ gap: "8px" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
                <span>Google</span>
                <Button
                  disabled
                  variant="outlined"
                  startIcon={
                    <CheckCircleOutlineOutlinedIcon color="success"></CheckCircleOutlineOutlinedIcon>
                  }
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  sx={{ border: 0 }}
                ></Button>
              </Box>
              <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                <Typography sx={{ fontSize: 10 }}>0 Locations</Typography>
              </Box>
            </Box>
          }
          value={1}
        />
      )}

      {Boolean(rbAccess && rbAccess.Facebook) && (
        <Tab
          disabled={isEditMode}
          label={
            <Box sx={{ gap: "8px" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
                <span>Facebook</span>
                {isFacebookConnected ? (
                  <Button
                    disabled
                    variant="outlined"
                    startIcon={
                      <CheckCircleOutlineOutlinedIcon color="success"></CheckCircleOutlineOutlinedIcon>
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    sx={{ border: 0 }}
                  ></Button>
                ) : userInfo?.id ? (
                  <FacebookLogin
                    userId={userInfo.id}
                    onLoginSuccess={async () => {
                      // Load Facebook pages after successful login
                      try {
                        const response = await _facebookService.getPages(
                          userInfo.id
                        );
                        if (response.success && response.pages) {
                          setFacebookPages(response.pages);
                          setIsFacebookConnected(true);
                          setToastConfig(
                            ToastSeverity.Success,
                            `${response.pages.length} Facebook pages loaded successfully`,
                            true
                          );
                        }
                      } catch (error) {
                        console.error("Error loading Facebook pages:", error);
                        setToastConfig(
                          ToastSeverity.Error,
                          "Failed to load Facebook pages",
                          true
                        );
                      }
                    }}
                  />
                ) : (
                  <Button
                    variant="outlined"
                    disabled
                    sx={{ minHeight: "50px", color: "#999" }}
                  >
                    Loading...
                  </Button>
                )}
              </Box>
              <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                <Typography sx={{ fontSize: 10 }}>
                  {isFacebookConnected
                    ? `${facebookPages.length} Pages`
                    : "Connect Account"}
                </Typography>
              </Box>
            </Box>
          }
          value={2}
        />
      )}

      {Boolean(rbAccess && rbAccess.Facebook) && (
        <Tab
          disabled={isEditMode}
          label={
            <Box sx={{ gap: "8px" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
                <span>Instagram</span>
                {isInstagramConnected ? (
                  <Button
                    disabled
                    variant="outlined"
                    startIcon={
                      <CheckCircleOutlineOutlinedIcon color="success"></CheckCircleOutlineOutlinedIcon>
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    sx={{ border: 0 }}
                  ></Button>
                ) : (
                  <Button
                    variant="outlined"
                    startIcon={
                      <SyncOutlinedIcon color="error"></SyncOutlinedIcon>
                    }
                    onClick={async (e) => {
                      e.stopPropagation();
                      try {
                        const response = await _instagramService.authenticate(
                          userInfo?.id
                        );
                        if (response?.success && response.authUrl) {
                          window.location.href = response.authUrl;
                        } else {
                          setToastConfig(
                            ToastSeverity.Error,
                            "Failed to initiate Instagram authentication",
                            true
                          );
                        }
                      } catch (error) {
                        console.error("Instagram authentication error:", error);
                        setToastConfig(
                          ToastSeverity.Error,
                          "Error initiating Instagram authentication",
                          true
                        );
                      }
                    }}
                  ></Button>
                )}
              </Box>
              <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                <Typography sx={{ fontSize: 10 }}>
                  {isInstagramConnected
                    ? `${instagramAccounts.length} Accounts`
                    : "Connect Account"}
                </Typography>
              </Box>
            </Box>
          }
          value={3}
        />
      )}

      {/* Twitter */}
      {Boolean(rbAccess && rbAccess.Twitter) && (
        <Tab
          disabled={isEditMode}
          label={
            <Box sx={{ gap: "8px" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
                <span>Twitter</span>
                {isTwitterConnected ? (
                  <Button
                    disabled
                    variant="outlined"
                    startIcon={
                      <CheckCircleOutlineOutlinedIcon color="success"></CheckCircleOutlineOutlinedIcon>
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    sx={{ border: 0 }}
                  ></Button>
                ) : userInfo?.id ? (
                  <Button
                    variant="outlined"
                    startIcon={
                      <SyncOutlinedIcon color="error"></SyncOutlinedIcon>
                    }
                    onClick={async (e) => {
                      e.stopPropagation();
                      try {
                        console.log("userInfo:", userInfo);
                        console.log("userInfo.id:", userInfo.id);

                        const response = await _twitterService.authenticate(
                          userInfo.id
                        );
                        if (response?.success && response?.data?.authUrl) {
                          window.location.href = response.data.authUrl;
                        } else {
                          setToastConfig(
                            ToastSeverity.Error,
                            "Failed to initiate Twitter authentication",
                            true
                          );
                        }
                      } catch (error) {
                        console.error("Twitter authentication error:", error);
                        setToastConfig(
                          ToastSeverity.Error,
                          "Error initiating Twitter authentication",
                          true
                        );
                      }
                    }}
                  ></Button>
                ) : (
                  <Button
                    variant="outlined"
                    disabled
                    sx={{ minHeight: "50px", color: "#999" }}
                  >
                    Loading...
                  </Button>
                )}
              </Box>
              <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                <Typography sx={{ fontSize: 10 }}>
                  {isTwitterConnected
                    ? `${twitterAccounts.length} Accounts`
                    : "Connect Account"}
                </Typography>
              </Box>
            </Box>
          }
          value={4}
        />
      )}

      {/* LinkedIn */}
      {Boolean(rbAccess && rbAccess.LinkedIn) && (
        <Tab
          disabled={isEditMode}
          label={
            <Box sx={{ gap: "8px" }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: "50px" }}>
                <span>LinkedIn</span>
                {isLinkedinConnected ? (
                  <Button
                    disabled
                    variant="outlined"
                    startIcon={
                      <CheckCircleOutlineOutlinedIcon color="success"></CheckCircleOutlineOutlinedIcon>
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    sx={{ border: 0 }}
                  ></Button>
                ) : userInfo?.id ? (
                  <LinkedInLogin
                    userId={userInfo.id}
                    onLoginSuccess={async () => {
                      // Load LinkedIn profiles after successful login
                      try {
                        const response = await _linkedinService.getPages(
                          userInfo.id
                        );
                        if (response?.success && response.pages) {
                          setLinkedinProfiles(response.pages);
                          setIsLinkedinConnected(true);

                          // Initialize selected profiles
                          const initialSelectedProfiles = response.pages.map(
                            (profile) => ({
                              id: profile.id,
                              name: profile.name,
                              email: profile.email,
                              picture: profile.picture,
                              selected: false,
                            })
                          );
                          setSelectedLinkedinProfiles(initialSelectedProfiles);

                          setToastConfig(
                            ToastSeverity.Success,
                            `${response.pages.length} LinkedIn profile(s) loaded successfully`,
                            true
                          );
                        }
                      } catch (error) {
                        console.error(
                          "Error loading LinkedIn profiles:",
                          error
                        );
                        setToastConfig(
                          ToastSeverity.Error,
                          "Failed to load LinkedIn profiles",
                          true
                        );
                      }
                    }}
                  />
                ) : (
                  <Button
                    variant="outlined"
                    disabled
                    sx={{ minHeight: "50px", color: "#999" }}
                  >
                    Loading...
                  </Button>
                )}
              </Box>
              <Box sx={{ display: "flex", alignItems: "flex-start" }}>
                <Typography sx={{ fontSize: 10 }}>
                  {isLinkedinConnected
                    ? `${linkedinProfiles.length} Profile(s)`
                    : "Connect Account"}
                </Typography>
              </Box>
            </Box>
          }
          value={5}
        />
      )}
    </Tabs>
  );

  const handleFileChange = (event: any) => {
    setUploadedImages([]);
    const files = Array.from(event.target.files);
    if (files.length > 5) {
      setToastConfig(
        ToastSeverity.Error,
        `You can only upload a maximum of 5 files.`,
        true
      );
      return;
    }

    // Define allowed file types based on current tab
    const allowedTypes =
      tabValue === 3 || tabValue === 2 || tabValue === 4 || tabValue === 5
        ? [
            // Images
            "image/jpeg",
            "image/jpg",
            "image/png",
            "image/gif",
            "image/webp",
            // Videos
            "video/mp4",
            "video/avi",
            "video/mov",
            "video/wmv",
            "video/flv",
            "video/webm",
          ]
        : ["image/jpeg", "image/png"]; // Google posts only support JPG/PNG

    const validFiles = files.filter((file: any) => {
      if (!allowedTypes.includes(file.type)) {
        const fileTypeText =
          tabValue === 3 || tabValue === 2 || tabValue === 4 || tabValue === 5
            ? "Only JPG, PNG, GIF, WebP images and MP4, AVI, MOV, WMV, FLV, WebM videos are allowed."
            : "Only JPG and PNG are allowed.";
        setToastConfig(
          ToastSeverity.Error,
          `Invalid file type: ${file.name}. ${fileTypeText}`,
          true
        );
        return false;
      }
      if (file.size < 10240) {
        setToastConfig(
          ToastSeverity.Error,
          `File "${file.name}" is too small. Minimum size is 10KB.`,
          true
        );
        return false;
      }
      return true;
    });

    setUploadedImages(validFiles);

    // Update form field value for the active tab
    if (formikRef.current && validFiles.length > 0) {
      formikRef.current.setFieldValue("media", validFiles);
    }

    // Update Instagram form data with mediaUrl if on Instagram tab and files are uploaded
    if (tabValue === 3 && validFiles.length > 0) {
      const firstFile = validFiles[0] as File;
      const mediaUrl = URL.createObjectURL(firstFile);
      const mediaType = firstFile.type.startsWith("video/") ? "video" : "image";

      setInstagramFormData((prev) => ({
        ...prev,
        mediaUrl: mediaUrl,
        mediaType: mediaType,
      }));
    }
  };

  const EventDateTimePicker = (props: {
    setFieldValue: any;
    values: IGoogleCreatePost;
    errors: FormikErrors<IGoogleCreatePost>;
    touched: FormikTouched<IGoogleCreatePost>;
    setFieldTouched: any;
    showtitle: boolean;
  }) => {
    const formatDayJsToISO = (date: Dayjs): string => {
      return date.utc().format("YYYY-MM-DDTHH:mm:ss[Z]");
    };
    const {
      setFieldValue,
      values,
      errors,
      touched,
      setFieldTouched,
      showtitle,
    } = props;
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ width: "100%", margin: "auto" }}>
          {showtitle && (
            <Typography variant="h6" sx={{ mb: 1 }}>
              Event Start & End Date - Time
            </Typography>
          )}

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 2,
              bgcolor: "#ffffff",
              borderRadius: 2,
              p: 2,
              justifyContent: "space-evenly",
            }}
          >
            <FormControl fullWidth>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                {/* Start Date */}
                <MobileDateTimePicker
                  value={
                    values.event &&
                    values.event.schedule &&
                    values.event.schedule.startTime
                      ? dayjs(values.event.schedule.startTime)
                      : null
                  }
                  onChange={(newValue) => {
                    if (newValue != null) {
                      setFieldValue(
                        "event.schedule.startTime",
                        formatDayJsToISO(newValue)
                      );
                    }
                  }}
                  onClose={() =>
                    setFieldTouched("event.schedule.startTime", true)
                  }
                  minDate={dayjs()}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: Boolean(
                        getIn(errors, "event.schedule.startTime") &&
                          getIn(touched, "event.schedule.startTime")
                      ),
                      helperText:
                        getIn(errors, "event.schedule.startTime") &&
                        getIn(touched, "event.schedule.startTime")
                          ? getIn(errors, "event.schedule.startTime")
                          : "",
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarTodayIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                  sx={{ width: "100%" }}
                  label={"Start Date"}
                />
              </Box>
            </FormControl>
            <Typography>-</Typography>
            <FormControl fullWidth>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                {/* End Date */}
                <MobileDateTimePicker
                  disabled={Boolean(
                    !(
                      values.event &&
                      values.event.schedule &&
                      values.event.schedule.startTime
                    )
                  )}
                  minDate={
                    values.event &&
                    values.event.schedule &&
                    values.event.schedule.startTime != null
                      ? dayjs(values.event.schedule.startTime).add(1, "day")
                      : undefined
                  }
                  value={
                    values.event &&
                    values.event?.schedule &&
                    values.event?.schedule?.endTime
                      ? dayjs(values.event.schedule.endTime)
                      : null
                  }
                  onChange={(newValue) => {
                    if (newValue != null) {
                      setFieldValue(
                        "event.schedule.endTime",
                        formatDayJsToISO(newValue)
                      );
                    }
                  }}
                  onClose={() =>
                    setFieldTouched("event.schedule.endTime", true)
                  }
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: Boolean(
                        getIn(errors, "event.schedule.endTime") &&
                          getIn(touched, "event.schedule.endTime")
                      ),
                      helperText:
                        getIn(errors, "event.schedule.endTime") &&
                        getIn(touched, "event.schedule.endTime")
                          ? getIn(errors, "event.schedule.endTime")
                          : "",
                      InputProps: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <CalendarTodayIcon />
                          </InputAdornment>
                        ),
                      },
                    },
                  }}
                  sx={{ width: "100%" }}
                  label={"End Date"}
                />
              </Box>
            </FormControl>
          </Box>
        </Box>
      </LocalizationProvider>
    );
  };

  const submitPost = async (
    createGooglePostList: ISelectionLocationWithPost[],
    values: IGoogleCreatePost
  ) => {
    if (createGooglePostList) {
      debugger;
      handleClosePost();
      setSelectedLocations(createGooglePostList);
      setShowCreatePostStatus(true);
      setAllApiCallsCompleted(false); // Reset API completion status
      console.log(uploadedImages);
      const formData = new FormData();
      for (let i = 0; i < uploadedImages.length; i++) {
        formData.append("files", uploadedImages[i] as unknown as Blob);
      }
      setPostCreationProgress({
        percent: 20,
        status: "Uploading images",
      });

      // Generate bulk post ID if multiple locations are selected
      const isBulkPost = createGooglePostList.length > 1;
      const bulkPostId = isBulkPost ? generateBulkPostId() : undefined;

      let mediaObject = [];

      // Check if images are from gallery, existing post, or need to be uploaded
      const galleryImages = uploadedImages.filter(
        (img: any) => img.isFromGallery && !img.isFromExistingPost
      );
      const existingPostImages = uploadedImages.filter(
        (img: any) => img.isFromExistingPost
      );
      const newImages = uploadedImages.filter((img: any) => !img.isFromGallery);

      // Handle gallery images (already in S3)
      for (let galleryImg of galleryImages) {
        mediaObject.push({
          mediaFormat: "PHOTO",
          sourceUrl: (galleryImg as any).s3Url,
        });
      }

      // Handle existing post images (already in S3)
      for (let existingImg of existingPostImages) {
        mediaObject.push({
          mediaFormat: (existingImg as any).mediaFormat || "PHOTO",
          sourceUrl: (existingImg as any).s3Url,
        });
      }

      // Upload new images to S3 temp folder if any
      if (newImages.length > 0) {
        var fileUploadResponse: any = await _postsService.uploadImagesToS3(
          userInfo.id,
          newImages as unknown as File[]
        );

        if (
          fileUploadResponse.isSuccess &&
          fileUploadResponse.data.length === newImages.length
        ) {
          for (let index = 0; index < newImages.length; index++) {
            const uploadedFile = fileUploadResponse.data[index];
            mediaObject.push({
              mediaFormat: "PHOTO",
              // Use signed URL since bucket doesn't allow public ACLs
              sourceUrl: uploadedFile.signedUrl,
            });
          }
        } else {
          setToastConfig(
            ToastSeverity.Error,
            "Failed to upload new images",
            true
          );
          return;
        }
      }

      // Continue with post creation if we have media
      if (mediaObject.length > 0) {
        let postList = createGooglePostList;

        const percent = 80 / createGooglePostList.length;

        for (let index = 0; index < createGooglePostList.length; index++) {
          try {
            let element2 = createGooglePostList[index];
            const postRequest = {
              ...element2.createGooglePost,
              media: mediaObject,
              // Set event to null if topicType is not EVENT
              event:
                element2.createGooglePost.topicType === TOPIC_TYPES.Event
                  ? element2.createGooglePost.event
                  : null,
              // Set offer to null if topicType is not OFFER
              offer:
                element2.createGooglePost.topicType === TOPIC_TYPES.Offer
                  ? element2.createGooglePost.offer
                  : null,
            };

            setPostCreationProgress({
              percent: postCreationProgress.percent + percent / 2,
              status: `Posting ${element2.locationInfo.locationName}`,
            });

            console.log("Create post request: ", {
              ...element2,
              createGooglePost: postRequest,
            });

            try {
              console.log({
                ...element2,
                createGooglePost: postRequest,
                // scheduleForLater: scheduleForLater
                //   ? (formikSchedulerRef.current as unknown as any).values
                //   : null,
              });
              var createPostResponse: any = await _postsService.createPost(
                userInfo.id,
                {
                  ...element2,
                  createGooglePost: postRequest,
                  // scheduleForLater: scheduleForLater
                  //   ? (formikSchedulerRef.current as unknown as any).values
                  //   : null,
                },
                isBulkPost,
                bulkPostId
              );

              if (createPostResponse.isSuccess) {
                postList[index].locationInfo.viewUrl =
                  createPostResponse.data.searchUrl;
                postList[index].locationInfo.status =
                  createPostResponse.isSuccess;
                setSelectedLocations([...postList]);
              } else {
                postList[index].locationInfo.status = false;
                setSelectedLocations([...postList]);
              }

              setPostCreationProgress({
                percent: postCreationProgress.percent + percent / 2,
                status: `Posting ${element2.locationInfo.locationName}`,
              });

              setPostCreationProgress({
                percent: 100,
                status: createPostResponse.message,
              });
            } catch (error) {
              console.error("Error creating post:", error);
              postList[index].locationInfo.status = false;
              setSelectedLocations([...postList]);
            }
          } catch (error) {
            console.error("Error in post creation loop:", error);
            postList[index].locationInfo.status = false;
            setSelectedLocations([...postList]);
          }
        }
        // Mark all API calls as completed regardless of success/failure
        setAllApiCallsCompleted(true);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "No images available for post creation",
          true
        );
        setShowCreatePostStatus(false);
        // Mark all API calls as completed even if no images
        setAllApiCallsCompleted(true);
      }
    }
  };

  const submitFacebookPost = async () => {
    try {
      // Validation for multi-page mode
      if (
        selectedFacebookPages.length === 0 ||
        !facebookFormData.message ||
        !facebookFormData.description
      ) {
        setToastConfig(
          ToastSeverity.Error,
          "Please select Facebook pages, enter a message, and add a description",
          true
        );
        return;
      }

      setShowCreatePostStatus(true);
      setAllApiCallsCompleted(false); // Reset API completion status
      setPostCreationProgress({
        percent: 20,
        status: "Preparing Facebook post",
      });

      // Prepare media for Facebook
      let mediaArray = [];
      if (uploadedImages.length > 0) {
        setPostCreationProgress({
          percent: 40,
          status: "Preparing images for Facebook",
        });

        // Convert images to File objects for Facebook posting
        const imageFiles: File[] = [];

        for (let i = 0; i < uploadedImages.length; i++) {
          const image = uploadedImages[i];

          // Check if it's already a File object (direct upload) or needs to be downloaded (gallery selection)
          if (image instanceof File) {
            imageFiles.push(image);
          } else if (typeof image === "string") {
            // It's a URL from gallery selection, download it
            try {
              setPostCreationProgress({
                percent: 40 + (i / uploadedImages.length) * 20,
                status: `Downloading image ${i + 1} of ${
                  uploadedImages.length
                }`,
              });

              const response = await fetch(image);
              const blob = await response.blob();

              // Extract filename from URL or create a default one
              const urlParts = image.split("/");
              const filename =
                urlParts[urlParts.length - 1].split("?")[0] ||
                `image-${i + 1}.jpg`;

              // Create File object from blob
              const file = new File([blob], filename, {
                type: blob.type || "image/jpeg",
              });
              imageFiles.push(file);
            } catch (error) {
              console.error("Error downloading image:", error);
              setToastConfig(
                ToastSeverity.Error,
                `Failed to download image ${i + 1}`,
                true
              );
              setShowCreatePostStatus(false);
              return;
            }
          }
        }

        // Upload all images to S3 temp folder
        setPostCreationProgress({
          percent: 60,
          status: "Uploading images to S3",
        });

        const fileUploadResponse: any = await _postsService.uploadImagesToS3(
          userInfo.id,
          imageFiles
        );

        if (fileUploadResponse.isSuccess) {
          mediaArray = fileUploadResponse.data.map((uploadedFile: any) => ({
            type: "image",
            url: uploadedFile.signedUrl,
          }));
        } else {
          setToastConfig(ToastSeverity.Error, "Failed to upload images", true);
          setShowCreatePostStatus(false);
          return;
        }
      }

      if (selectedFacebookPages.length > 1) {
        // Multi-page posting logic
        setPostCreationProgress({
          percent: 50,
          status: "Creating posts for multiple pages",
        });

        const totalPages = selectedFacebookPages.length;
        let successCount = 0;
        let failedPages: string[] = [];

        // Update selectedFacebookPages state to track progress
        const updatedPages = [...selectedFacebookPages];
        setSelectedFacebookPages(updatedPages);

        for (let i = 0; i < selectedFacebookPages.length; i++) {
          const page = selectedFacebookPages[i];

          try {
            setPostCreationProgress({
              percent: 50 + (i / totalPages) * 40,
              status: `Creating post for ${page.pageName}`,
            });

            // Replace {Page Name} with actual page name
            const dynamicMessage = facebookFormData.message.replace(
              /{Page Name}/g,
              page.pageName
            );
            const dynamicDescription = facebookFormData.description?.replace(
              /{Page Name}/g,
              page.pageName
            );

            // Create post data for this specific page
            const postData = {
              ...facebookFormData,
              pageId: page.pageId,
              message: dynamicMessage,
              description: dynamicDescription,
              media: mediaArray,
            };

            const response = await _facebookService.createPost(
              userInfo.id,
              postData
            );

            if (response.success) {
              updatedPages[i] = {
                ...page,
                status: true,
                facebookUrl: response.data?.facebookUrl,
              };
              successCount++;
            } else {
              updatedPages[i] = {
                ...page,
                status: false,
                error: response.message || "Failed to create post",
              };
              failedPages.push(page.pageName);
            }
          } catch (error: any) {
            updatedPages[i] = {
              ...page,
              status: false,
              error: error.message || "Unknown error",
            };
            failedPages.push(page.pageName);
          }

          // Update state to show progress
          setSelectedFacebookPages([...updatedPages]);
        }

        setPostCreationProgress({
          percent: 100,
          status: `Completed: ${successCount}/${totalPages} posts created`,
        });
        setAllApiCallsCompleted(true); // Mark all API calls as completed

        if (successCount === totalPages) {
          setToastConfig(
            ToastSeverity.Success,
            `Successfully created posts for all ${totalPages} Facebook pages`,
            true
          );
        } else if (successCount > 0) {
          setToastConfig(
            ToastSeverity.Warning,
            `Created ${successCount}/${totalPages} posts. Failed: ${failedPages.join(
              ", "
            )}`,
            true
          );
        } else {
          setToastConfig(
            ToastSeverity.Error,
            `Failed to create posts for all pages: ${failedPages.join(", ")}`,
            true
          );
        }

        // Reset form after completion
        setFacebookFormData(FACEBOOK_POST_INITIAL);
        setUploadedImages([]);
      } else {
        // Single page posting logic (existing logic)
        setPostCreationProgress({
          percent: 70,
          status: "Creating Facebook post",
        });

        // For single page posting, set pageId from the first selected page
        const pageId =
          selectedFacebookPages.length > 0
            ? selectedFacebookPages[0].pageId
            : facebookFormData.pageId;

        const postData = {
          ...facebookFormData,
          pageId: pageId,
          media: mediaArray,
        };

        const response = await _facebookService.createPost(
          userInfo.id,
          postData
        );

        if (response.success) {
          setPostCreationProgress({
            percent: 100,
            status: "Facebook post created successfully",
          });
          setAllApiCallsCompleted(true); // Mark all API calls as completed

          setToastConfig(
            ToastSeverity.Success,
            "Facebook post created successfully",
            true
          );

          // Reset form
          setFacebookFormData(FACEBOOK_POST_INITIAL);
          setUploadedImages([]);
        } else {
          setToastConfig(
            ToastSeverity.Error,
            response.message || "Failed to create Facebook post",
            true
          );
          setShowCreatePostStatus(false);
        }
      }
    } catch (error: any) {
      console.error("Error creating Facebook post:", error);
      setToastConfig(ToastSeverity.Error, "Error creating Facebook post", true);
      setShowCreatePostStatus(false);
    }
    // Mark all API calls as completed regardless of success/failure
    setAllApiCallsCompleted(true);
  };

  const submitInstagramPost = async () => {
    if (selectedInstagramAccounts.length === 0) {
      setToastConfig(
        ToastSeverity.Error,
        "Please select at least one Instagram account",
        true
      );
      return;
    }

    if (!instagramFormData.caption || !instagramFormData.mediaUrl) {
      setToastConfig(
        ToastSeverity.Error,
        "Please fill in all required fields and upload media",
        true
      );
      return;
    }

    setShowCreatePostStatus(true);
    setAllApiCallsCompleted(false); // Reset API completion status
    setPostCreationProgress({
      percent: 10,
      status: "Preparing Instagram posts",
    });

    try {
      // Update selected accounts status to null (loading)
      const updatedAccounts = selectedInstagramAccounts.map((account) => ({
        ...account,
        status: null,
      }));
      setSelectedInstagramAccounts(updatedAccounts);

      setPostCreationProgress({
        percent: 30,
        status: "Creating Instagram posts",
      });

      // Create posts for each selected account
      for (let i = 0; i < selectedInstagramAccounts.length; i++) {
        const account = selectedInstagramAccounts[i];

        try {
          setPostCreationProgress({
            percent: 30 + (i / selectedInstagramAccounts.length) * 60,
            status: `Creating post for ${account.accountName}`,
          });

          const postData: IInstagramCreatePost = {
            ...instagramFormData,
            accountId: account.accountId,
            caption: instagramFormData.caption.replace(
              /{Account Name}/g,
              account.accountName
            ),
          };

          const response = await _instagramService.createPost(
            userInfo?.id,
            postData
          );

          // Update account status
          const accountIndex = selectedInstagramAccounts.findIndex(
            (a) => a.accountId === account.accountId
          );
          if (accountIndex !== -1) {
            const newAccounts = [...selectedInstagramAccounts];
            newAccounts[accountIndex] = {
              ...newAccounts[accountIndex],
              status: response?.success || false,
              instagramUrl: response?.data?.instagramUrl || "",
              error: response?.success
                ? undefined
                : response?.message || "Failed to create post",
            };
            setSelectedInstagramAccounts(newAccounts);
          }
        } catch (error: any) {
          console.error(
            `Error creating post for account ${account.accountName}:`,
            error
          );

          // Update account status to failed
          const accountIndex = selectedInstagramAccounts.findIndex(
            (a) => a.accountId === account.accountId
          );
          if (accountIndex !== -1) {
            const newAccounts = [...selectedInstagramAccounts];
            newAccounts[accountIndex] = {
              ...newAccounts[accountIndex],
              status: false,
              error: error.message || "Failed to create post",
            };
            setSelectedInstagramAccounts(newAccounts);
          }
        }
      }

      setPostCreationProgress({
        percent: 100,
        status: "Instagram posts completed",
      });
      setAllApiCallsCompleted(true); // Mark all API calls as completed

      // Check if any posts were successful
      const successfulPosts = selectedInstagramAccounts.filter(
        (account) => account.status === true
      );
      if (successfulPosts.length > 0) {
        setToastConfig(
          ToastSeverity.Success,
          `Successfully created ${successfulPosts.length} Instagram post(s)`,
          true
        );

        // Reset form and images
        setInstagramFormData(INSTAGRAM_POST_INITIAL);
        setSelectedInstagramAccounts([]);
        setUploadedImages([]);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to create Instagram posts",
          true
        );
        setShowCreatePostStatus(false);
      }
    } catch (error: any) {
      console.error("Error creating Instagram post:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Error creating Instagram post",
        true
      );
      setShowCreatePostStatus(false);
    }
    // Mark all API calls as completed regardless of success/failure
    setAllApiCallsCompleted(true);
  };

  const submitLinkedInPost = async () => {
    if (selectedLinkedinProfiles.filter((p) => p.selected).length === 0) {
      setToastConfig(
        ToastSeverity.Error,
        "Please select at least one LinkedIn profile",
        true
      );
      return;
    }

    if (!linkedinFormData.text.trim()) {
      setToastConfig(ToastSeverity.Error, "Please enter post text", true);
      return;
    }

    setShowCreatePostStatus(true);
    setAllApiCallsCompleted(false); // Reset API completion status
    setPostCreationProgress({
      percent: 10,
      status: "Preparing LinkedIn posts",
    });

    try {
      const selectedProfiles = selectedLinkedinProfiles.filter(
        (p) => p.selected
      );

      // Update selected profiles status to null (loading)
      const updatedProfiles = selectedLinkedinProfiles.map((profile) => ({
        ...profile,
        status: profile.selected ? null : profile.status,
      }));
      setSelectedLinkedinProfiles(updatedProfiles);

      setPostCreationProgress({
        percent: 30,
        status: "Creating LinkedIn posts",
      });

      // Create posts for each selected profile
      for (let i = 0; i < selectedProfiles.length; i++) {
        const profile = selectedProfiles[i];

        try {
          setPostCreationProgress({
            percent: 30 + (i / selectedProfiles.length) * 60,
            status: `Creating post for ${profile.name}`,
          });

          const postData: ILinkedInCreatePost = {
            ...linkedinFormData,
            profileId: profile.id,
            text: linkedinFormData.text.replace(
              /{Profile Name}/g,
              profile.name
            ),
          };

          const response = await _linkedinService.createPost(
            userInfo?.id,
            postData
          );

          // Update profile status
          const profileIndex = selectedLinkedinProfiles.findIndex(
            (p) => p.id === profile.id
          );
          if (profileIndex !== -1) {
            const newProfiles = [...selectedLinkedinProfiles];
            newProfiles[profileIndex] = {
              ...newProfiles[profileIndex],
              status: response?.success || false,
              linkedinUrl: response?.data?.linkedinUrl || "",
              error: response?.success
                ? undefined
                : response?.message || "Failed to create post",
            };
            setSelectedLinkedinProfiles(newProfiles);
          }
        } catch (error: any) {
          console.error(
            `Error creating post for profile ${profile.name}:`,
            error
          );

          // Update profile status to failed
          const profileIndex = selectedLinkedinProfiles.findIndex(
            (p) => p.id === profile.id
          );
          if (profileIndex !== -1) {
            const newProfiles = [...selectedLinkedinProfiles];
            newProfiles[profileIndex] = {
              ...newProfiles[profileIndex],
              status: false,
              error: error.message || "Failed to create post",
            };
            setSelectedLinkedinProfiles(newProfiles);
          }
        }
      }

      setPostCreationProgress({
        percent: 100,
        status: "LinkedIn posts completed",
      });
      setAllApiCallsCompleted(true); // Mark all API calls as completed

      // Check if any posts were successful
      const successfulPosts = selectedLinkedinProfiles.filter(
        (profile) => profile.status === true
      );
      if (successfulPosts.length > 0) {
        setToastConfig(
          ToastSeverity.Success,
          `Successfully created ${successfulPosts.length} LinkedIn post(s)`,
          true
        );

        // Reset form and images
        setLinkedinFormData(LINKEDIN_POST_INITIAL);
        setSelectedLinkedinProfiles((prev) =>
          prev.map((p) => ({ ...p, selected: false, status: undefined }))
        );
        setUploadedImages([]);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to create LinkedIn posts",
          true
        );
        setShowCreatePostStatus(false);
      }
    } catch (error: any) {
      console.error("Error creating LinkedIn post:", error);
      setToastConfig(ToastSeverity.Error, "Error creating LinkedIn post", true);
      setShowCreatePostStatus(false);
    }
    // Mark all API calls as completed regardless of success/failure
    setAllApiCallsCompleted(true);
  };

  // Twitter post submission handler
  const handleTwitterPostSubmit = async () => {
    try {
      // Validate Twitter post data
      if (!twitterFormData.text.trim()) {
        setToastConfig(ToastSeverity.Error, "Please enter tweet text", true);
        return;
      }

      if (selectedTwitterAccounts.length === 0) {
        setToastConfig(
          ToastSeverity.Error,
          "Please select at least one Twitter account",
          true
        );
        return;
      }

      setShowCreatePostStatus(true);
      setAllApiCallsCompleted(false); // Reset API completion status
      setPostCreationProgress({
        percent: 10,
        status: "Preparing Twitter posts",
      });

      // Upload images to S3 if any
      let mediaUrls: string[] = [];
      if (uploadedImages.length > 0) {
        setPostCreationProgress({
          percent: 30,
          status: "Uploading media",
        });

        const uploadResponse = await _postsService.uploadImagesToS3(
          userInfo.id,
          uploadedImages as File[]
        );

        if (uploadResponse.success) {
          mediaUrls = uploadResponse.data.map(
            (uploadedFile: any) => uploadedFile.signedUrl
          );
        } else {
          setToastConfig(ToastSeverity.Error, "Failed to upload images", true);
          setShowCreatePostStatus(false);
          return;
        }
      }

      setPostCreationProgress({
        percent: 50,
        status: "Creating Twitter posts",
      });

      const successfulPosts: any[] = [];
      const failedPosts: any[] = [];

      // Create posts for each selected account
      for (let i = 0; i < selectedTwitterAccounts.length; i++) {
        const account = selectedTwitterAccounts[i];

        try {
          const postData: ITwitterCreatePost = {
            accountId: account.accountId,
            text: twitterFormData.text,
            mediaUrls: mediaUrls,
            replyTo: twitterFormData.replyTo,
            quoteTweetId: twitterFormData.quoteTweetId,
            scheduledPublishTime: twitterFormData.scheduledPublishTime,
          };

          const response = await _twitterService.createPost(
            userInfo.id,
            postData
          );

          if (response && response.success) {
            successfulPosts.push({
              account: account,
              response: response.data,
            });

            // Update account status in selectedTwitterAccounts
            setSelectedTwitterAccounts((prev) =>
              prev.map((acc) =>
                acc.accountId === account.accountId
                  ? {
                      ...acc,
                      status: true,
                      twitterUrl: response.data.twitterUrl,
                    }
                  : acc
              )
            );
          } else {
            failedPosts.push({
              account: account,
              error: response?.message || "Unknown error",
            });

            // Update account status in selectedTwitterAccounts
            setSelectedTwitterAccounts((prev) =>
              prev.map((acc) =>
                acc.accountId === account.accountId
                  ? {
                      ...acc,
                      status: false,
                      error: response?.message || "Unknown error",
                    }
                  : acc
              )
            );
          }
        } catch (error: any) {
          failedPosts.push({
            account: account,
            error: error.message || "Unknown error occurred",
          });

          // Update account status in selectedTwitterAccounts
          setSelectedTwitterAccounts((prev) =>
            prev.map((acc) =>
              acc.accountId === account.accountId
                ? {
                    ...acc,
                    status: false,
                    error: error.message || "Unknown error occurred",
                  }
                : acc
            )
          );
        }

        // Update progress
        const progress = 50 + ((i + 1) / selectedTwitterAccounts.length) * 40;
        setPostCreationProgress({
          percent: progress,
          status: `Creating post ${i + 1} of ${selectedTwitterAccounts.length}`,
        });
      }

      setPostCreationProgress({
        percent: 100,
        status: "Completed",
      });

      if (successfulPosts.length > 0) {
        setToastConfig(
          ToastSeverity.Success,
          `Successfully created ${successfulPosts.length} Twitter post(s)`,
          true
        );

        // Reset form and images
        setTwitterFormData(TWITTER_POST_INITIAL);
        setSelectedTwitterAccounts([]);
        setUploadedImages([]);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to create Twitter posts",
          true
        );
        setShowCreatePostStatus(false);
      }
    } catch (error: any) {
      console.error("Error creating Twitter post:", error);
      setToastConfig(ToastSeverity.Error, "Error creating Twitter post", true);
      setShowCreatePostStatus(false);
    }
    // Mark all API calls as completed regardless of success/failure
    setAllApiCallsCompleted(true);
  };

  const BlinkingText = styled(Typography)`
    @keyframes blink {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    animation: blink 1s infinite;
  `;

  const getProgressColor = () => {
    if (postCreationProgress.percent >= 100) {
      return "primary.main"; // Completed color (Green)
    }
    return "secondary.main"; // Processing color (Blue)
  };

  return (
    <Box>
      <LeftMenuComponent>
        <Box className="commonTableHeader">
          <h3 className="commonTitle pageTitle">{editTitle}</h3>
        </Box>
        <Formik
          innerRef={formikRef}
          enableReinitialize={true}
          initialValues={{
            ...createPostInitials,
            media: [],
          }}
          onSubmit={(values, formikHelpers) => {
            // Platform-specific validation and submission is now handled by individual submit buttons
            // This onSubmit is kept for compatibility but won't be used
            console.log("Form submitted with values:", values);
            formikHelpers.setSubmitting(false);
          }}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            handleSubmit,
            isSubmitting,
            isValid,
            setFieldValue,
            setFieldTouched,
            setTouched,
            setErrors,
            /* and other goodies */
          }) => {
            // Since we're not using enableReinitialize, we don't need to track form changes
            // The form will maintain its state naturally

            return (
              <form
                onSubmit={(e) => {
                  e.preventDefault(); // Prevents page refresh
                  handleSubmit();
                }}
              >
                <div className="height100">
                  <Box className="height100">
                    <Box sx={{ display: "flex" }}>
                      {/* <Sidebar /> */}
                      <Box sx={{ width: "100%" }}>
                        <PostTypeTabs
                          value={tabValue}
                          onChange={(e: any, newValue: number) => {
                            setTabValue(newValue);
                          }}
                        />
                        <Box sx={{ display: "flex", gap: "16px", mt: 2 }}>
                          <Box sx={{ width: "70%" }}>
                            {tabValue === 1 &&
                              Boolean(rbAccess && rbAccess.Google) && (
                                <GooglePostForm
                                  values={values}
                                  errors={errors}
                                  touched={touched}
                                  setFieldValue={setFieldValue}
                                  uploadedImages={uploadedImages}
                                  handleClick={handleClick}
                                  handleGallerySelection={() =>
                                    setGallerySelectionOpen(true)
                                  }
                                  MAX_ALLOWED_CHARS={MAX_ALLOWED_CHARS}
                                  onSubmit={(formValues) =>
                                    handleGooglePostSubmit(formValues)
                                  }
                                  isEditMode={isEditMode}
                                  isBulkEditMode={isBulkEditMode}
                                />
                              )}
                            {tabValue === 2 &&
                              isFacebookConnected &&
                              Boolean(rbAccess && rbAccess.Facebook) && (
                                <FacebookPostForm
                                  formData={facebookFormData}
                                  onFormChange={setFacebookFormData}
                                  uploadedImages={uploadedImages}
                                  onImageUpload={() => {
                                    if (fileInputRef.current) {
                                      (
                                        fileInputRef.current as HTMLInputElement
                                      ).click();
                                    }
                                  }}
                                  onGalleryOpen={() =>
                                    setGallerySelectionOpen(true)
                                  }
                                  onImageRemove={(index) => {
                                    const newImages = [...uploadedImages];
                                    newImages.splice(index, 1);
                                    setUploadedImages(newImages);
                                  }}
                                  errors={{}}
                                  facebookPages={facebookPages}
                                  loading={false}
                                  selectedPages={selectedFacebookPages}
                                  onSelectedPagesChange={
                                    setSelectedFacebookPages
                                  }
                                  onSubmit={() => handleFacebookPostSubmit()}
                                  isFacebookConnected={isFacebookConnected}
                                />
                              )}
                            {tabValue === 2 && !isFacebookConnected && (
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  minHeight: "400px",
                                  textAlign: "center",
                                  gap: 2,
                                }}
                              >
                                <Typography variant="h6" color="text.secondary">
                                  Connect Your Facebook Account
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Please connect your Facebook account using the
                                  Connect button in the tab above to start
                                  creating Facebook posts.
                                </Typography>
                              </Box>
                            )}
                            {tabValue === 3 &&
                              isInstagramConnected &&
                              Boolean(rbAccess && rbAccess.Facebook) && (
                                <InstagramPostForm
                                  formData={instagramFormData}
                                  onFormChange={setInstagramFormData}
                                  uploadedImages={uploadedImages}
                                  onImageUpload={() => {
                                    if (fileInputRef.current) {
                                      (
                                        fileInputRef.current as HTMLInputElement
                                      ).click();
                                    }
                                  }}
                                  onGalleryOpen={() =>
                                    setGallerySelectionOpen(true)
                                  }
                                  onImageRemove={(index) => {
                                    const newImages = [...uploadedImages];
                                    newImages.splice(index, 1);
                                    setUploadedImages(newImages);

                                    // Clear Instagram form data if no images left
                                    if (newImages.length === 0) {
                                      setInstagramFormData((prev) => ({
                                        ...prev,
                                        mediaUrl: "",
                                        mediaType: "image",
                                      }));
                                    } else {
                                      // Update with the first remaining image
                                      const firstImage = newImages[0];
                                      let mediaUrl = "";

                                      if ((firstImage as any).s3Url) {
                                        mediaUrl = (firstImage as any).s3Url;
                                      } else if (firstImage instanceof File) {
                                        mediaUrl =
                                          URL.createObjectURL(firstImage);
                                      }

                                      const mediaType = (
                                        firstImage as File
                                      )?.type?.startsWith("video/")
                                        ? "video"
                                        : "image";

                                      setInstagramFormData((prev) => ({
                                        ...prev,
                                        mediaUrl: mediaUrl,
                                        mediaType: mediaType,
                                      }));
                                    }
                                  }}
                                  errors={{}}
                                  instagramAccounts={instagramAccounts}
                                  loading={false}
                                  selectedAccounts={selectedInstagramAccounts}
                                  onSelectedAccountsChange={
                                    setSelectedInstagramAccounts
                                  }
                                  onSubmit={() => handleInstagramPostSubmit()}
                                  isInstagramConnected={isInstagramConnected}
                                />
                              )}
                            {tabValue === 3 && !isInstagramConnected && (
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  minHeight: "400px",
                                  textAlign: "center",
                                  gap: 2,
                                }}
                              >
                                <Typography variant="h6" color="text.secondary">
                                  Connect Your Instagram Account
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Please connect your Instagram account using
                                  the Connect button in the tab above to start
                                  creating Instagram posts.
                                </Typography>
                              </Box>
                            )}
                            {tabValue === 4 &&
                              isTwitterConnected &&
                              Boolean(rbAccess && rbAccess.Twitter) && (
                                <TwitterPostForm
                                  formData={twitterFormData}
                                  onFormChange={setTwitterFormData}
                                  uploadedImages={uploadedImages}
                                  onImageUpload={() => {
                                    if (fileInputRef.current) {
                                      (
                                        fileInputRef.current as HTMLInputElement
                                      ).click();
                                    }
                                  }}
                                  onGalleryOpen={() =>
                                    setGallerySelectionOpen(true)
                                  }
                                  onImageRemove={(index) => {
                                    const newImages = [...uploadedImages];
                                    newImages.splice(index, 1);
                                    setUploadedImages(newImages);
                                  }}
                                  errors={{}}
                                  twitterAccounts={twitterAccounts}
                                  loading={false}
                                  selectedAccounts={selectedTwitterAccounts}
                                  onSelectedAccountsChange={
                                    setSelectedTwitterAccounts
                                  }
                                  onSubmit={() => handleTwitterPostSubmit()}
                                  isTwitterConnected={isTwitterConnected}
                                />
                              )}
                            {tabValue === 4 && !isTwitterConnected && (
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  height: "400px",
                                  textAlign: "center",
                                  p: 3,
                                }}
                              >
                                <Typography variant="h5" sx={{ mb: 2 }}>
                                  Connect Your Twitter Account
                                </Typography>
                                <Typography
                                  variant="body1"
                                  color="textSecondary"
                                  sx={{ mb: 3 }}
                                >
                                  To create Twitter posts, you need to connect
                                  your Twitter account first. Click the Connect
                                  button in the tab above to start creating
                                  Twitter posts.
                                </Typography>
                              </Box>
                            )}
                            {tabValue === 5 &&
                              isLinkedinConnected &&
                              Boolean(rbAccess && rbAccess.LinkedIn) && (
                                <LinkedInPostForm
                                  profiles={linkedinProfiles}
                                  selectedProfiles={selectedLinkedinProfiles}
                                  onSelectedProfilesChange={
                                    setSelectedLinkedinProfiles
                                  }
                                  formData={linkedinFormData}
                                  onFormDataChange={setLinkedinFormData}
                                  uploadedImages={uploadedImages}
                                  scheduleForLater={scheduleForLater}
                                  onScheduleChange={setScheduleForLater}
                                  scheduledDate={date}
                                  onScheduledDateChange={setDate}
                                  onImageUpload={() =>
                                    document
                                      .getElementById("file-upload")
                                      ?.click()
                                  }
                                  onGalleryOpen={() =>
                                    setGallerySelectionOpen(true)
                                  }
                                  onSubmit={() => handleLinkedInPostSubmit()}
                                  isLinkedInConnected={isLinkedinConnected}
                                />
                              )}
                            {tabValue === 5 && !isLinkedinConnected && (
                              <Box
                                sx={{
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  minHeight: "400px",
                                  textAlign: "center",
                                  gap: 2,
                                }}
                              >
                                <Typography variant="h6" color="text.secondary">
                                  Connect Your LinkedIn Account
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Please connect your LinkedIn account using the
                                  Connect button in the tab above to start
                                  creating LinkedIn posts.
                                </Typography>
                              </Box>
                            )}
                          </Box>
                          <Box sx={{ width: "30%" }}>
                            {/* Conditional Preview based on active tab */}
                            {tabValue === 1 && (
                              <GooglePostPreview
                                values={values as any}
                                uploadedImages={uploadedImages}
                                createPostInitials={createPostInitials}
                              />
                            )}
                            {tabValue === 2 && (
                              <FacebookPostPreview
                                key={`${facebookFormData.message}-${facebookFormData.description}-${selectedFacebookPages.length}`}
                                formData={facebookFormData}
                                uploadedImages={uploadedImages}
                                selectedPage={null}
                                selectedPages={selectedFacebookPages}
                                enableMultiPageSelection={true}
                              />
                            )}
                            {tabValue === 3 && isInstagramConnected && (
                              <InstagramPostPreview
                                key={`${instagramFormData.caption}-${instagramFormData.mediaUrl}-${selectedInstagramAccounts.length}`}
                                formData={instagramFormData}
                                uploadedImages={uploadedImages}
                                selectedAccount={null}
                                selectedAccounts={selectedInstagramAccounts}
                                enableMultiAccountSelection={true}
                              />
                            )}
                            {tabValue === 3 && !isInstagramConnected && (
                              <Card
                                elevation={3}
                                sx={{
                                  borderRadius: 2,
                                  p: 3,
                                  mb: 2,
                                  textAlign: "center",
                                  backgroundColor: "#f9fafb",
                                }}
                              >
                                <Typography variant="h6" color="text.secondary">
                                  Connect Instagram
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Connect your Instagram account to see post
                                  preview
                                </Typography>
                              </Card>
                            )}
                            {tabValue === 4 && isTwitterConnected && (
                              <TwitterPostPreview
                                key={`${twitterFormData.text}-${selectedTwitterAccounts.length}`}
                                formData={twitterFormData}
                                uploadedImages={uploadedImages}
                                selectedAccount={null}
                                selectedAccounts={selectedTwitterAccounts}
                                enableMultiAccountSelection={true}
                              />
                            )}
                            {tabValue === 4 && !isTwitterConnected && (
                              <Card
                                elevation={3}
                                sx={{
                                  borderRadius: 2,
                                  p: 3,
                                  textAlign: "center",
                                  backgroundColor: "#f5f5f5",
                                }}
                              >
                                <Typography variant="h6" color="textSecondary">
                                  Twitter Preview
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="textSecondary"
                                >
                                  Connect your Twitter account to see post
                                  preview
                                </Typography>
                              </Card>
                            )}
                            {tabValue === 5 && isLinkedinConnected && (
                              <LinkedInPostPreview
                                profileName={
                                  selectedLinkedinProfiles.find(
                                    (p) => p.selected
                                  )?.name
                                }
                                profilePicture={
                                  selectedLinkedinProfiles.find(
                                    (p) => p.selected
                                  )?.picture
                                }
                                text={
                                  selectedLinkedinProfiles.filter(
                                    (p) => p.selected
                                  ).length > 1
                                    ? linkedinFormData.text.replace(
                                        /{Profile Name}/g,
                                        selectedLinkedinProfiles.find(
                                          (p) => p.selected
                                        )?.name || "Profile Name"
                                      )
                                    : linkedinFormData.text
                                }
                                media={linkedinFormData.media}
                                showPreview={!!linkedinFormData.text.trim()}
                              />
                            )}
                            {tabValue === 5 && !isLinkedinConnected && (
                              <Card
                                elevation={3}
                                sx={{
                                  borderRadius: 2,
                                  p: 3,
                                  mb: 2,
                                  textAlign: "center",
                                  backgroundColor: "#f9fafb",
                                }}
                              >
                                <Typography variant="h6" color="text.secondary">
                                  Connect LinkedIn
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Connect your LinkedIn account to see post
                                  preview
                                </Typography>
                              </Card>
                            )}
                            <InfoCard />
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  </Box>

                  <Drawer
                    anchor={"right"}
                    open={showLocationSelection.isShow}
                    onClose={() => console.log("Create Post modal closed")}
                    sx={{
                      "& .MuiDrawer-paper": {
                        maxWidth: "50vw", // Set the max width
                        width: "100%", // Ensure the drawer does not exceed the max width
                      },
                      zIndex: (theme) => {
                        return theme.zIndex.drawer;
                      },
                    }}
                  >
                    <Box className="height100">
                      <SubmitPost
                        isShow={showLocationSelection.isShow}
                        closeModal={handleClosePost}
                        createPostModel={showLocationSelection.createPostModel}
                        savePosts={(
                          createGooglePostList: ISelectionLocationWithPost[]
                        ) => submitPost(createGooglePostList, values as any)}
                      />
                    </Box>
                  </Drawer>
                </div>
                <FormErrorDebugger errors={errors} touched={touched} />

                {/* Gallery Selection Modal */}
                <GallerySelectionComponent
                  open={gallerySelectionOpen}
                  onClose={() => setGallerySelectionOpen(false)}
                  onImageSelect={(imageUrl: string, asset: any) =>
                    handleImageSelectFromGallery(imageUrl, asset, setFieldValue)
                  }
                  onComputerSelect={() => {
                    if (fileInputRef.current) {
                      (fileInputRef.current as HTMLInputElement).click();
                    }
                  }}
                />

                {/* Hidden File Input */}
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  accept="image/*,video/*"
                  multiple
                  onChange={handleFileChange}
                />
              </form>
            );
          }}
        </Formik>
      </LeftMenuComponent>

      {/* Platform-specific Post Status Dialogs */}

      {/* Google Post Status Dialog */}
      {tabValue === 1 && (
        <GooglePostStatusDialog
          open={showCreatePostStatus}
          onClose={() => setShowCreatePostStatus(false)}
          selectedLocations={selectedLocations}
          postCreationProgress={postCreationProgress}
          allApiCallsCompleted={allApiCallsCompleted}
          getProgressColor={getProgressColor}
        />
      )}

      {/* Facebook Post Status Dialog */}
      {tabValue === 2 && (
        <FacebookPostStatusDialog
          open={showCreatePostStatus}
          onClose={() => setShowCreatePostStatus(false)}
          selectedPages={selectedFacebookPages.map((page: any) => ({
            pageInfo: {
              pageName: page.name,
              pageId: page.id,
              status: page.status,
              postUrl: page.postUrl,
              errorMessage: page.error,
            },
          }))}
          postCreationProgress={postCreationProgress}
          allApiCallsCompleted={allApiCallsCompleted}
          getProgressColor={getProgressColor}
        />
      )}

      {/* Instagram Post Status Dialog */}
      {tabValue === 3 && (
        <InstagramPostStatusDialog
          open={showCreatePostStatus}
          onClose={() => setShowCreatePostStatus(false)}
          selectedAccounts={selectedInstagramAccounts.map((account: any) => ({
            accountInfo: {
              accountName: account.name,
              accountId: account.id,
              username: account.username,
              status: account.status,
              postUrl: account.postUrl,
              errorMessage: account.error,
            },
          }))}
          postCreationProgress={postCreationProgress}
          allApiCallsCompleted={allApiCallsCompleted}
          getProgressColor={getProgressColor}
        />
      )}

      {/* Twitter Post Status Dialog */}
      {tabValue === 4 && (
        <TwitterPostStatusDialog
          open={showCreatePostStatus}
          selectedAccounts={selectedTwitterAccounts.map((account: any) => ({
            accountInfo: {
              accountName: account.accountName,
              accountId: account.accountId,
              accountUsername: account.accountUsername,
              accountPictureUrl: account.accountPictureUrl,
              isVerified: account.isVerified,
              followersCount: account.followersCount,
              followingCount: account.followingCount,
              tweetCount: account.tweetCount,
              status: account.status,
              error: account.error,
              twitterUrl: account.twitterUrl,
            },
          }))}
          postCreationProgress={postCreationProgress}
        />
      )}

      {/* LinkedIn Post Status Dialog */}
      {tabValue === 5 && (
        <LinkedInPostStatusDialog
          open={showCreatePostStatus}
          onClose={() => setShowCreatePostStatus(false)}
          selectedProfiles={selectedLinkedinProfiles
            .filter((profile: any) => profile.selected)
            .map((profile: any) => ({
              profileInfo: {
                profileName: profile.name,
                profileId: profile.id,
                companyName: profile.companyName,
                status: profile.status,
                postUrl: profile.linkedinUrl,
                errorMessage: profile.error,
              },
            }))}
          postCreationProgress={postCreationProgress}
          allApiCallsCompleted={allApiCallsCompleted}
          getProgressColor={getProgressColor}
        />
      )}
    </Box>
  );
};

export default CreateSocialPost;
