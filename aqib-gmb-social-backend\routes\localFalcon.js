const express = require("express");
const router = express.Router();
const {
  welcome,
  calculateGrid,
  getLocationSuggestions,
  searchPlaces,
  getBusinessRanking,
  performSearch,
  runGridScan,
  saveConfiguration,
  getConfigurations,
  getConfiguration,
  updateConfiguration,
  deleteConfiguration,
  getAlerts,
  markAlertAsRead,
} = require("../controllers/localFalcon.controller");

const { isAuthenticated } = require("../middleware/isAuthenticated");

/**
 * @swagger
 * tags:
 *   name: Local Falcon
 *   description: Local Falcon API for local search ranking analysis
 */

/**
 * @swagger
 * /local-falcon:
 *   get:
 *     summary: Welcome message for Local Falcon API
 *     tags: [Local Falcon]
 *     responses:
 *       200:
 *         description: API status message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 version:
 *                   type: string
 */
router.get("/", welcome);

/**
 * @swagger
 * /local-falcon/location-suggestions:
 *   get:
 *     summary: Get location suggestions for autocomplete
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: query
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query for location suggestions
 *     responses:
 *       200:
 *         description: Location suggestions retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get("/location-suggestions", isAuthenticated, getLocationSuggestions);

/**
 * @swagger
 * /local-falcon/grid:
 *   post:
 *     summary: Calculate grid points from base coordinate
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lat
 *               - lng
 *               - gridSize
 *               - radius
 *               - unit
 *             properties:
 *               lat:
 *                 type: number
 *                 description: Center latitude
 *               lng:
 *                 type: number
 *                 description: Center longitude
 *               gridSize:
 *                 type: string
 *                 description: Grid size (e.g., "5x5")
 *               radius:
 *                 type: number
 *                 description: Radius distance
 *               unit:
 *                 type: string
 *                 enum: [meters, kilometers, miles]
 *                 description: Distance unit
 *     responses:
 *       200:
 *         description: Grid points calculated successfully
 *       400:
 *         description: Invalid input parameters
 *       401:
 *         description: Unauthorized
 */
router.post("/grid", isAuthenticated, calculateGrid);

/**
 * @swagger
 * /local-falcon/places:
 *   post:
 *     summary: Search for Google My Business locations
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *               - lat
 *               - lng
 *             properties:
 *               query:
 *                 type: string
 *                 description: Search query
 *               lat:
 *                 type: number
 *                 description: Search center latitude
 *               lng:
 *                 type: number
 *                 description: Search center longitude
 *               radius:
 *                 type: number
 *                 description: Search radius
 *               unit:
 *                 type: string
 *                 enum: [meters, kilometers, miles]
 *                 description: Distance unit
 *     responses:
 *       200:
 *         description: Places found successfully
 *       401:
 *         description: Unauthorized
 */
router.post("/places", isAuthenticated, searchPlaces);

/**
 * @swagger
 * /local-falcon/ranking:
 *   post:
 *     summary: Get business ranking at specific coordinate point
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - keyword
 *               - businessName
 *               - lat
 *               - lng
 *             properties:
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *               businessName:
 *                 type: string
 *                 description: Business name to rank
 *               lat:
 *                 type: number
 *                 description: Search point latitude
 *               lng:
 *                 type: number
 *                 description: Search point longitude
 *               placeId:
 *                 type: string
 *                 description: Google Place ID (optional)
 *     responses:
 *       200:
 *         description: Ranking retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.post("/ranking", isAuthenticated, getBusinessRanking);

/**
 * @swagger
 * /local-falcon/search:
 *   post:
 *     summary: Perform keyword search at specific coordinate point
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - query
 *               - lat
 *               - lng
 *             properties:
 *               query:
 *                 type: string
 *                 description: Search query
 *               lat:
 *                 type: number
 *                 description: Search point latitude
 *               lng:
 *                 type: number
 *                 description: Search point longitude
 *               radius:
 *                 type: number
 *                 description: Search radius
 *               unit:
 *                 type: string
 *                 enum: [meters, kilometers, miles]
 *                 description: Distance unit
 *     responses:
 *       200:
 *         description: Search completed successfully
 *       401:
 *         description: Unauthorized
 */
router.post("/search", isAuthenticated, performSearch);

/**
 * @swagger
 * /local-falcon/scan:
 *   post:
 *     summary: Run a full grid scan
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - keyword
 *               - businessName
 *               - lat
 *               - lng
 *               - gridSize
 *               - radius
 *               - unit
 *             properties:
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *               businessName:
 *                 type: string
 *                 description: Business name to rank
 *               lat:
 *                 type: number
 *                 description: Center latitude
 *               lng:
 *                 type: number
 *                 description: Center longitude
 *               gridSize:
 *                 type: string
 *                 description: Grid size (e.g., "5x5")
 *               radius:
 *                 type: number
 *                 description: Radius distance
 *               unit:
 *                 type: string
 *                 enum: [meters, kilometers, miles]
 *                 description: Distance unit
 *               placeId:
 *                 type: string
 *                 description: Google Place ID (optional)
 *     responses:
 *       200:
 *         description: Grid scan completed successfully
 *       401:
 *         description: Unauthorized
 */
router.post("/scan", isAuthenticated, runGridScan);

/**
 * @swagger
 * /local-falcon/configurations:
 *   post:
 *     summary: Save Local Falcon configuration
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - keyword
 *               - businessName
 *               - centerLat
 *               - centerLng
 *               - gridSize
 *               - radius
 *               - unit
 *             properties:
 *               name:
 *                 type: string
 *                 description: Configuration name
 *               keyword:
 *                 type: string
 *                 description: Search keyword
 *               businessName:
 *                 type: string
 *                 description: Business name
 *               placeId:
 *                 type: string
 *                 description: Google Place ID
 *               centerLat:
 *                 type: number
 *                 description: Center latitude
 *               centerLng:
 *                 type: number
 *                 description: Center longitude
 *               gridSize:
 *                 type: string
 *                 description: Grid size
 *               radius:
 *                 type: number
 *                 description: Radius distance
 *               unit:
 *                 type: string
 *                 enum: [meters, kilometers, miles]
 *                 description: Distance unit
 *               isScheduleEnabled:
 *                 type: boolean
 *                 description: Enable scheduled scans
 *               scheduleFrequency:
 *                 type: string
 *                 enum: [daily, weekly, monthly]
 *                 description: Schedule frequency
 *               alertThreshold:
 *                 type: number
 *                 description: Alert threshold for ranking changes
 *               settings:
 *                 type: object
 *                 description: Additional settings
 *     responses:
 *       200:
 *         description: Configuration saved successfully
 *       401:
 *         description: Unauthorized
 *   get:
 *     summary: Get all configurations for user
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Configurations retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.post("/configurations", isAuthenticated, saveConfiguration);
router.get("/configurations", isAuthenticated, getConfigurations);

/**
 * @swagger
 * /local-falcon/configurations/{configId}:
 *   get:
 *     summary: Get specific configuration by ID
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Configuration ID
 *     responses:
 *       200:
 *         description: Configuration retrieved successfully
 *       404:
 *         description: Configuration not found
 *       401:
 *         description: Unauthorized
 *   put:
 *     summary: Update configuration
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Configuration ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               keyword:
 *                 type: string
 *               businessName:
 *                 type: string
 *               placeId:
 *                 type: string
 *               centerLat:
 *                 type: number
 *               centerLng:
 *                 type: number
 *               gridSize:
 *                 type: string
 *               radius:
 *                 type: number
 *               unit:
 *                 type: string
 *               isScheduleEnabled:
 *                 type: boolean
 *               scheduleFrequency:
 *                 type: string
 *               alertThreshold:
 *                 type: number
 *               settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Configuration updated successfully
 *       401:
 *         description: Unauthorized
 *   delete:
 *     summary: Delete configuration
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Configuration ID
 *     responses:
 *       200:
 *         description: Configuration deleted successfully
 *       401:
 *         description: Unauthorized
 */
router.get("/configurations/:configId", isAuthenticated, getConfiguration);
router.put("/configurations/:configId", isAuthenticated, updateConfiguration);
router.delete(
  "/configurations/:configId",
  isAuthenticated,
  deleteConfiguration
);

/**
 * @swagger
 * /local-falcon/alerts:
 *   get:
 *     summary: Get alerts for user
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: Get only unread alerts
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get("/alerts", isAuthenticated, getAlerts);

/**
 * @swagger
 * /local-falcon/alerts/{alertId}/read:
 *   put:
 *     summary: Mark alert as read
 *     tags: [Local Falcon]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Alert ID
 *     responses:
 *       200:
 *         description: Alert marked as read successfully
 *       401:
 *         description: Unauthorized
 */
router.put("/alerts/:alertId/read", isAuthenticated, markAlertAsRead);

module.exports = router;
