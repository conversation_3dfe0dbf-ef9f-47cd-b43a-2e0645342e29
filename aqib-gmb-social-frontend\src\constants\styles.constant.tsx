// Common styles constants for consistent UI components
import { SxProps, Theme } from "@mui/material/styles";

// Common form control styles
export const FORM_CONTROL_STYLES: SxProps<Theme> = {
  backgroundColor: "var(--whiteColor)",
  borderRadius: "5px",
};

// Common input field styles (for TextField, OutlinedInput, etc.)
export const INPUT_FIELD_STYLES: SxProps<Theme> = {
  backgroundColor: "var(--whiteColor)",
  borderRadius: "5px",
};

// Common select dropdown styles
export const SELECT_DROPDOWN_STYLES: SxProps<Theme> = {
  backgroundColor: "var(--whiteColor)",
  borderRadius: "5px",
};

// Common filled input styles (for filled variant components)
export const FILLED_INPUT_STYLES: SxProps<Theme> = {
  backgroundColor: "var(--whiteColor)",
  borderRadius: "5px",
};

// Common outlined input styles with enhanced border styling
export const OUTLINED_INPUT_STYLES: SxProps<Theme> = {
  backgroundColor: "var(--whiteColor)",
  borderRadius: "8px",
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderWidth: "1px",
    },
    "&:hover fieldset": {
      borderColor: "rgba(0, 0, 0, 0.87)",
      borderWidth: "1px",
    },
    "&.Mui-focused fieldset": {
      borderColor: "rgba(0, 0, 0, 0.23)",
      borderWidth: "1px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "rgba(0, 0, 0, 0.6)",
    "&.Mui-focused": {
      color: "rgba(0, 0, 0, 0.6)",
    },
  },
};
