import React, { useState, useEffect, useRef } from "react";
import { Box, Chip, Typography, Divider } from "@mui/material";
import { ILocation } from "../../interfaces/response/ILocationsListResponseModel";

interface LocationChipsProps {
  availableLocations: ILocation[];
  locationsWithData: string[]; // Array of location IDs that have data
  onLocationToggle: (locationId: string, isSelected: boolean) => void;
  onSelectionChange: (selectedLocationIds: string[]) => void;
}

const LocationChips: React.FC<LocationChipsProps> = ({
  availableLocations,
  locationsWithData,
  onLocationToggle,
  onSelectionChange,
}) => {
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const prevLocationsWithDataRef = useRef<string[]>([]);
  const isInitializedRef = useRef(false);

  // Initialize with all locations that have data - only run when locationsWithData actually changes
  useEffect(() => {
    // Deduplicate locationsWithData to prevent duplicate chips
    const uniqueLocationsWithData = [...new Set(locationsWithData)];

    // Check if locationsWithData has actually changed
    const hasChanged =
      uniqueLocationsWithData.length !==
        prevLocationsWithDataRef.current.length ||
      uniqueLocationsWithData.some(
        (id, index) => id !== prevLocationsWithDataRef.current[index]
      );

    if (hasChanged && uniqueLocationsWithData.length > 0) {
      setSelectedLocationIds([...uniqueLocationsWithData]);
      // Only call onSelectionChange if this is the first initialization or data actually changed
      if (!isInitializedRef.current || hasChanged) {
        onSelectionChange([...uniqueLocationsWithData]);
        isInitializedRef.current = true;
      }
      prevLocationsWithDataRef.current = [...uniqueLocationsWithData];
    }
  }, [locationsWithData]); // Removed onSelectionChange from dependencies

  const handleChipClick = (locationId: string) => {
    const isCurrentlySelected = selectedLocationIds.includes(locationId);
    const newSelectedIds = isCurrentlySelected
      ? selectedLocationIds.filter((id) => id !== locationId)
      : [...selectedLocationIds, locationId];

    setSelectedLocationIds(newSelectedIds);
    onLocationToggle(locationId, !isCurrentlySelected);
    onSelectionChange(newSelectedIds);
  };

  const getLocationName = (locationId: string): string => {
    const location = availableLocations.find(
      (loc) => loc.gmbLocationId === locationId
    );
    return location?.gmbLocationName || locationId;
  };

  // Deduplicate locationsWithData for rendering
  const uniqueLocationsWithData = [...new Set(locationsWithData)];

  if (uniqueLocationsWithData.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: 2, mb: 2 }}>
      <Divider sx={{ mb: 2 }} />
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: "bold" }}>
        Data Retrieved from Locations ({uniqueLocationsWithData.length}{" "}
        locations):
      </Typography>
      <Typography
        variant="caption"
        sx={{ mb: 2, display: "block", color: "text.secondary" }}
      >
        Click chips to include/exclude locations from the analytics display
      </Typography>
      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          gap: 1,
          alignItems: "center",
        }}
      >
        {uniqueLocationsWithData.map((locationId) => {
          const isSelected = selectedLocationIds.includes(locationId);
          return (
            <Chip
              key={locationId}
              label={getLocationName(locationId)}
              onClick={() => handleChipClick(locationId)}
              color={isSelected ? "primary" : "default"}
              variant={isSelected ? "filled" : "outlined"}
              sx={{
                cursor: "pointer",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  transform: "scale(1.05)",
                  boxShadow: 2,
                },
                "& .MuiChip-label": {
                  whiteSpace: "normal",
                  wordBreak: "break-word",
                },
              }}
            />
          );
        })}
      </Box>
      {selectedLocationIds.length === 0 && (
        <Typography
          variant="caption"
          sx={{
            mt: 1,
            display: "block",
            color: "warning.main",
            fontWeight: "bold",
          }}
        >
          ⚠️ No locations selected - analytics will show zero values
        </Typography>
      )}
      <Divider sx={{ mt: 2 }} />
    </Box>
  );
};

export default LocationChips;
