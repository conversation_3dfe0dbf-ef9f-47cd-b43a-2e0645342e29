import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface IRatingsVsMonthChartProps {
  data: Record<string, Record<number, number>>;
  title?: string;
}

const RatingsVsMonthChart: React.FC<IRatingsVsMonthChartProps> = ({
  data,
  title = "Ratings Distribution by Month",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const months = Object.keys(data).sort();
  const ratings = [1, 2, 3, 4, 5];

  const datasets = ratings.map((rating) => ({
    label: `${rating} Star${rating > 1 ? "s" : ""}`,
    data: months.map((month) => data[month]?.[rating] || 0),
    backgroundColor: getColorForRating(rating, theme),
    borderColor: getColorForRating(rating, theme),
    borderWidth: 1,
  }));

  const chartData = {
    labels: months,
    datasets,
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
      },
    },
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
      },
    },
  };

  function getColorForRating(rating: number, theme: any): string {
    const reportColors = theme.palette.reportColors;

    if (reportColors) {
      const colors = {
        1: reportColors.color1, // Light blue for 1 star
        2: reportColors.color2, // Light teal for 2 stars
        3: reportColors.color3, // Very light teal for 3 stars
        4: reportColors.color4, // Light pink for 4 stars
        5: reportColors.color5, // Pink for 5 stars
      };
      return colors[rating as keyof typeof colors] || theme.palette.grey[500];
    }

    // Fallback to original colors if reportColors not available
    const primary = theme.palette.primary.main;
    const secondary = theme.palette.secondary.main;
    const colors = {
      1: "#f44336", // Red for 1 star (negative)
      2: "#ff9800", // Orange for 2 stars (poor)
      3: "#ffc107", // Amber for 3 stars (average)
      4: primary, // Primary color for 4 stars (good)
      5: secondary, // Secondary color for 5 stars (excellent)
    };
    return colors[rating as keyof typeof colors] || theme.palette.grey[500];
  }

  // Calculate totals for summary
  const totalReviews = months.reduce((total, month) => {
    return (
      total +
      ratings.reduce((monthTotal, rating) => {
        return monthTotal + (data[month]?.[rating] || 0);
      }, 0)
    );
  }, 0);

  const averageRating =
    months.length > 0
      ? (
          months.reduce((total, month) => {
            return (
              total +
              ratings.reduce((monthTotal, rating) => {
                return monthTotal + rating * (data[month]?.[rating] || 0);
              }, 0)
            );
          }, 0) / totalReviews
        ).toFixed(1)
      : "0.0";

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 3, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Average Rating: <strong>{averageRating} ⭐</strong>
          </Typography>
        </Box>
      </Box>

      {totalReviews > 0 ? (
        <Bar data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected filters
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default RatingsVsMonthChart;
