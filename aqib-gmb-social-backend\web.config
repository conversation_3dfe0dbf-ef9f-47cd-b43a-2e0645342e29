<configuration>
  <system.webServer>
    <applicationInitialization doAppInitAfterRestart="true">
      <add initializationPage="/v1" />
    </applicationInitialization>
    
    <handlers>
      <add name="iisnode" path="server.js" verb="*" modules="iisnode" resourceType="Unspecified" requireAccess="Script" />
    </handlers>

    <rewrite>
      <rules>
        <rule name="NodeApp" stopProcessing="true">
          <match url=".*" />
          <action type="Rewrite" url="server.js" />
        </rule>
      </rules>
    </rewrite>

    <iisnode
      nodeProcessCommandLine="C:\Users\<USER>\AppData\Local\nvm\v20.9.0\node.exe" 
      loggingEnabled="true"
      logDirectory="iisnode-logs"
      devErrorsEnabled="true"
      node_env="staging" />

    <!-- Note: NODE_ENV environment variable removed as it was causing IIS issues -->
    
    <httpErrors errorMode="Detailed" />
    <asp scriptErrorSentToBrowser="true" />

  </system.webServer>
</configuration>
