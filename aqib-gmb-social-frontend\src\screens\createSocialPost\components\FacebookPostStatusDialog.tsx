import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Box,
  LinearProgress,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  IconButton,
  Typography,
} from "@mui/material";
import {
  CheckCircleOutline as CheckCircleOutlineIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  Block as BlockIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import {
  FacebookPostStatusDialogProps,
  IFacebookPagePost,
} from "../interfaces/postStatusInterfaces";
import BlinkingText from "./shared/BlinkingText";

const FacebookPostStatusDialog: React.FC<FacebookPostStatusDialogProps> = ({
  open,
  onClose,
  selectedPages,
  postCreationProgress,
  allApiCallsCompleted,
  getProgressColor,
}) => {
  const navigate = useNavigate();

  return (
    <Dialog
      fullWidth
      maxWidth="md"
      open={open}
      onClose={() => console.log("On Close")}
    >
      <DialogTitle>Facebook Post Upload Status</DialogTitle>
      <Box sx={{ position: "relative", width: "100%" }}>
        <LinearProgress
          variant="determinate"
          value={postCreationProgress.percent}
          color="secondary"
          sx={{
            height: "20px",
            backgroundColor: "#d3d3d3",
            "& .MuiLinearProgress-bar": {
              backgroundColor: getProgressColor(),
            },
          }}
        />
        <BlinkingText
          variant="body2"
          sx={{
            position: "absolute",
            top: 0,
            left: "13%",
            transform: "translateX(-50%)",
            fontWeight: "bold",
            color: "#ffffff",
          }}
        >
          {postCreationProgress.status}...
        </BlinkingText>
      </Box>
      <DialogContent>
        <DialogContentText>
          <Box
            noValidate
            component="form"
            sx={{
              display: "flex",
              flexDirection: "column",
              m: "auto",
            }}
          >
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>
                      <b>Page Name</b>
                    </TableCell>
                    <TableCell>
                      <b>Page ID</b>
                    </TableCell>
                    <TableCell>
                      <b>Status</b>
                    </TableCell>
                    <TableCell>
                      <b>Actions</b>
                    </TableCell>
                    <TableCell>
                      <b>Error Message</b>
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedPages &&
                    selectedPages.map(
                      (page: IFacebookPagePost, index: number) => (
                        <TableRow key={index}>
                          <TableCell>{page.pageInfo.pageName}</TableCell>
                          <TableCell>{page.pageInfo.pageId}</TableCell>
                          <TableCell>
                            {page.pageInfo.status == null ? (
                              <CircularProgress color="secondary" size="30px" />
                            ) : page.pageInfo.status ? (
                              <CheckCircleOutlineIcon color="success" />
                            ) : (
                              <CancelIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell>
                            {page.pageInfo.postUrl ? (
                              <IconButton
                                onClick={() =>
                                  window.open(page.pageInfo.postUrl, "_blank")
                                }
                                color="primary"
                                size="small"
                              >
                                <VisibilityIcon />
                              </IconButton>
                            ) : (
                              <BlockIcon color="error" />
                            )}
                          </TableCell>
                          <TableCell>
                            {page.pageInfo.errorMessage && (
                              <Typography variant="caption" color="error">
                                {page.pageInfo.errorMessage}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    )}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          disabled={!allApiCallsCompleted}
          onClick={() => navigate("/post-management/posts")}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FacebookPostStatusDialog;
