const Service = require("node-windows").Service;
const path = require("path");

console.log("Testing node-windows service creation...");
console.log("Node version:", process.version);
console.log("Platform:", process.platform);
console.log("Architecture:", process.arch);

// Create a very simple test service
const svc = new Service({
  name: "TestNodeService",
  description: "Test Node Service",
  script: path.join(__dirname, "app.js"),
  execPath: process.execPath,
  workingDirectory: __dirname,
});

svc.on("install", function () {
  console.log("✓ Service installed successfully");
});

svc.on("alreadyinstalled", function () {
  console.log("! Service already exists");
});

svc.on("error", function (err) {
  console.error("✗ Service error:", err);
});

svc.on("invalidinstallation", function () {
  console.error("✗ Invalid installation");
});

console.log("Attempting to install test service...");
svc.install();
