const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");
const {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getTwitterAccounts,
  createPost,
  getPosts,
} = require("../controllers/twitter.controller");

/**
 * @route GET /v1/twitter/
 * @desc Welcome endpoint for Twitter integration
 * @access Public
 */
router.get("/", welcome);

/**
 * @route POST /v1/twitter/authenticate
 * @desc Initiate Twitter OAuth authentication
 * @access Private
 * @body { userId: number, businessId: number }
 */
router.post("/authenticate", isAuthenticated, authenticate);

/**
 * @route POST /v1/twitter/callback
 * @desc Handle Twitter OAuth callback (redirect)
 * @access Public
 * @body { code: string, state: string }
 */
router.post("/callback", callback);

/**
 * @route POST /v1/twitter/callback-validation
 * @desc Validate Twitter OAuth callback (AJAX)
 * @access Public
 * @body { code: string, state: string }
 */
router.post("/callback-validation", callbackValidation);

/**
 * @route GET /v1/twitter/accounts/:userId
 * @desc Get Twitter accounts for user
 * @access Private
 * @params { userId: number }
 */
router.get("/accounts/:userId", isAuthenticated, getTwitterAccounts);

/**
 * @route POST /v1/twitter/post/:userId
 * @desc Create Twitter post
 * @access Private
 * @params { userId: number }
 * @body {
 *   accountId: string,
 *   text: string,
 *   mediaUrls?: string[],
 *   replyTo?: string,
 *   quoteTweetId?: string,
 *   scheduledPublishTime?: string
 * }
 */
router.post("/post/:userId", isAuthenticated, createPost);

/**
 * @route GET /v1/twitter/posts/:userId
 * @desc Get Twitter posts for user
 * @access Private
 * @params { userId: number }
 * @query { limit?: number, offset?: number }
 */
router.get("/posts/:userId", isAuthenticated, getPosts);

module.exports = router;
