import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  ACCOUNTS_COUNT,
  IMAGE_PROXY,
  LIST_OF_LOCATIONS,
  LOCATION_SUMMARY,
  REFRESH_LOCATIONS,
  UPDATE_LOCATION_PHONE_NUMBERS,
} from "../../constants/endPoints.constant";
import { IPaginationModel } from "../../interfaces/IPaginationModel";
import { Action } from "redux";

class LocationService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  getLocations = async () => {
    return await this._httpHelperService.get(LIST_OF_LOCATIONS);
  };

  getLocationsPaginated = async (
    userId: number,
    paginationModel: IPaginationModel,
    businessId: number,
    businessGroupId: number,
    search: string
  ) => {
    return await this._httpHelperService.get(
      `${LIST_OF_LOCATIONS}/userId/${userId}?pageNo=${paginationModel.pageNo}&offset=${paginationModel.offset}&businessId=${businessId}&businessGroupId=${businessGroupId}&search=${search}`
    );
  };

  getAccountsCount = async (userId: number) => {
    return await this._httpHelperService.get(
      `${ACCOUNTS_COUNT}/userId/${userId}`
    );
  };

  syncAllLocations = async (userId: number) => {
    return await this._httpHelperService.get(
      `${REFRESH_LOCATIONS}/userId/${userId}`
    );
  };

  getLocationSummary = async (headerObject: any) => {
    return await this._httpHelperService.get(LOCATION_SUMMARY, headerObject);
  };

  getGoogleImageBase64 = async (url: string) => {
    return await this._httpHelperService.get(IMAGE_PROXY(url));
  };

  updatePhoneNumbers = async (
    accountId: string,
    locationId: string,
    phoneData: { primaryPhone: string; additionalPhones: string[] }
  ) => {
    return await this._httpHelperService.put(
      `${UPDATE_LOCATION_PHONE_NUMBERS}/${accountId}/${locationId}`,
      phoneData
    );
  };
}

export default LocationService;
