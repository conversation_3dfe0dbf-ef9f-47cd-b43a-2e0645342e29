const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");
const {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getInstagramAccounts,
  getAccounts,
  createPost,
  getPosts,
  debugAccounts,
} = require("../controllers/instagram.controller");

/**
 * @swagger
 * components:
 *   schemas:
 *     InstagramPost:
 *       type: object
 *       required:
 *         - accountId
 *         - caption
 *         - mediaUrl
 *       properties:
 *         accountId:
 *           type: string
 *           description: Instagram account ID
 *         caption:
 *           type: string
 *           description: Post caption
 *         mediaUrl:
 *           type: string
 *           description: URL of the media to post
 *         mediaType:
 *           type: string
 *           enum: [image, video]
 *           default: image
 *           description: Type of media
 *         published:
 *           type: boolean
 *           default: true
 *           description: Whether to publish immediately
 *         scheduledPublishTime:
 *           type: string
 *           format: date-time
 *           description: Scheduled publish time (if not publishing immediately)
 */

/**
 * @swagger
 * /v1/instagram:
 *   get:
 *     summary: Welcome endpoint for Instagram API
 *     description: Returns basic information about the Instagram API
 *     tags: [Instagram]
 *     responses:
 *       200:
 *         description: Welcome message and API information
 */
router.get("/", welcome);

/**
 * @swagger
 * /v1/instagram/authenticate:
 *   post:
 *     summary: Initiate Instagram authentication
 *     description: Generates Instagram OAuth URL for user authentication
 *     tags: [Instagram]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: User ID
 *     responses:
 *       200:
 *         description: Authentication URL generated successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post("/authenticate", isAuthenticated, authenticate);

/**
 * @swagger
 * /v1/instagram/callback:
 *   get:
 *     summary: Handle Instagram OAuth callback
 *     description: Handles the OAuth callback from Instagram and redirects to frontend
 *     tags: [Instagram]
 *     parameters:
 *       - in: query
 *         name: code
 *         schema:
 *           type: string
 *         description: Authorization code from Instagram
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *         description: State parameter for security
 *     responses:
 *       302:
 *         description: Redirects to frontend with result
 */
router.get("/callback", callback);

/**
 * @swagger
 * /v1/instagram/callback-validation:
 *   post:
 *     summary: Validate Instagram OAuth callback
 *     description: Processes the OAuth callback and completes authentication
 *     tags: [Instagram]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - state
 *             properties:
 *               code:
 *                 type: string
 *                 description: Authorization code from Instagram
 *               state:
 *                 type: string
 *                 description: State parameter for security
 *     responses:
 *       200:
 *         description: Authentication completed successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
router.post("/callback-validation", callbackValidation);

/**
 * @swagger
 * /v1/instagram/accounts/{userId}:
 *   get:
 *     summary: Get Instagram accounts for user
 *     description: Retrieves all Instagram accounts connected to the user
 *     tags: [Instagram]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Instagram accounts retrieved successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: No accounts found
 *       500:
 *         description: Internal server error
 */
router.get("/accounts/:userId", isAuthenticated, getInstagramAccounts);

/**
 * @swagger
 * /v1/instagram/pages/{userId}:
 *   get:
 *     summary: Get Instagram pages for user
 *     description: Retrieves all Instagram pages/accounts connected to the user
 *     tags: [Instagram]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: Instagram pages retrieved successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: No pages found
 *       500:
 *         description: Internal server error
 */
router.get("/pages/:userId", isAuthenticated, getAccounts);

/**
 * @swagger
 * /v1/instagram/posts/{userId}:
 *   post:
 *     summary: Create Instagram post
 *     description: Creates a new post on Instagram
 *     tags: [Instagram]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/InstagramPost'
 *     responses:
 *       200:
 *         description: Post created successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: Instagram account not found
 *       500:
 *         description: Internal server error
 */
router.post("/posts/:userId", isAuthenticated, createPost);

/**
 * @swagger
 * /v1/instagram/posts/{userId}:
 *   get:
 *     summary: Get Instagram posts for user
 *     description: Retrieves Instagram posts created by the user
 *     tags: [Instagram]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *       - in: query
 *         name: accountId
 *         schema:
 *           type: string
 *         description: Instagram account ID (optional)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of posts per page
 *     responses:
 *       200:
 *         description: Posts retrieved successfully
 *       400:
 *         description: Bad request
 *       404:
 *         description: No posts found
 *       500:
 *         description: Internal server error
 */
router.get("/posts/:userId", isAuthenticated, getPosts);

// Debug endpoint to check Instagram account data
router.get("/debug/accounts/:userId", isAuthenticated, debugAccounts);

module.exports = router;
