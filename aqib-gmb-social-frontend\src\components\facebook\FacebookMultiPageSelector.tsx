import React, { useState, useEffect } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Box,
  Typography,
  SelectChangeEvent,
  Checkbox,
  ListItemText,
  Chip,
  FormHelperText,
  Badge,
} from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { IFacebookPageData } from "../../interfaces/response/IFacebookCreatePostResponse";
import { IFacebookSelectedPage } from "../../interfaces/request/IFacebookCreatePost";

interface FacebookMultiPageSelectorProps {
  pages: IFacebookPageData[];
  selectedPages: IFacebookSelectedPage[];
  onPagesChange: (pages: IFacebookSelectedPage[]) => void;
  error?: string;
  loading?: boolean;
  disabled?: boolean;
}

const FacebookMultiPageSelector: React.FC<FacebookMultiPageSelectorProps> = ({
  pages,
  selectedPages,
  onPagesChange,
  error,
  loading = false,
  disabled = false,
}) => {
  const [selectedPageIds, setSelectedPageIds] = useState<string[]>([]);

  // Update selectedPageIds when selectedPages prop changes
  useEffect(() => {
    setSelectedPageIds(selectedPages.map((page) => page.pageId));
  }, [selectedPages]);

  const handlePageChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    setSelectedPageIds(value);

    // Convert selected page IDs to IFacebookSelectedPage objects
    const newSelectedPages: IFacebookSelectedPage[] = value.map((pageId) => {
      const page = pages.find((p) => p.page_id === pageId);
      return {
        pageId: pageId,
        pageName: page?.page_name || "",
        pageCategory: page?.page_category,
        pagePictureUrl: page?.page_picture_url,
        status: undefined,
        facebookUrl: undefined,
        error: undefined,
      };
    });

    onPagesChange(newSelectedPages);
  };

  const handleRemovePage = (pageIdToRemove: string) => {
    const newSelectedPageIds = selectedPageIds.filter(
      (id) => id !== pageIdToRemove
    );
    setSelectedPageIds(newSelectedPageIds);

    const newSelectedPages = selectedPages.filter(
      (page) => page.pageId !== pageIdToRemove
    );
    onPagesChange(newSelectedPages);
  };

  const renderValue = (selected: string[]) => {
    if (selected.length === 0) {
      return <em>Select Facebook Pages</em>;
    }

    if (selected.length === 1) {
      const page = pages.find((p) => p.page_id === selected[0]);
      return page?.page_name || selected[0];
    }

    return `${selected.length} pages selected`;
  };

  return (
    <FormControl variant="filled" fullWidth error={!!error}>
      <InputLabel id="facebook-multi-page-select-label">
        Select Facebook Pages
      </InputLabel>
      <Select
        labelId="facebook-multi-page-select-label"
        multiple
        value={selectedPageIds}
        onChange={handlePageChange}
        disabled={loading || pages.length === 0 || disabled}
        renderValue={renderValue}
        sx={{
          backgroundColor: "var(--whiteColor)",
          borderRadius: "5px",
        }}
      >
        <MenuItem value="">
          <em>Select Facebook Pages</em>
        </MenuItem>
        {pages.map((page) => {
          const isSelected = selectedPageIds.indexOf(page.page_id) > -1;
          return (
            <MenuItem key={page.page_id} value={page.page_id}>
              <Checkbox checked={isSelected} />
              <Box
                sx={{ display: "flex", alignItems: "center", gap: 1, flex: 1 }}
              >
                {page.page_picture_url ? (
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: "top", horizontal: "right" }}
                    badgeContent={
                      isSelected ? (
                        <CheckCircleIcon
                          sx={{
                            color: "primary.main",
                            fontSize: 16,
                            backgroundColor: "white",
                            borderRadius: "50%",
                          }}
                        />
                      ) : null
                    }
                  >
                    <Avatar
                      src={page.page_picture_url}
                      sx={{ width: 32, height: 32 }}
                    />
                  </Badge>
                ) : (
                  <Avatar sx={{ width: 32, height: 32 }}>
                    {page.page_name.charAt(0).toUpperCase()}
                  </Avatar>
                )}
                <Box>
                  <ListItemText
                    primary={page.page_name}
                    secondary={page.page_category}
                    sx={{ margin: 0 }}
                  />
                </Box>
              </Box>
            </MenuItem>
          );
        })}
      </Select>

      {/* Display selected pages as chips */}
      {selectedPages.length > 0 && (
        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 1 }}>
          {selectedPages.map((page) => (
            <Chip
              key={page.pageId}
              label={page.pageName}
              onDelete={() => handleRemovePage(page.pageId)}
              size="small"
              color="primary"
              variant="outlined"
              avatar={
                page.pagePictureUrl ? (
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: "top", horizontal: "right" }}
                    badgeContent={
                      <CheckCircleIcon
                        sx={{
                          color: "primary.main",
                          fontSize: 12,
                          backgroundColor: "white",
                          borderRadius: "50%",
                        }}
                      />
                    }
                  >
                    <Avatar
                      src={page.pagePictureUrl}
                      sx={{ width: 20, height: 20 }}
                    />
                  </Badge>
                ) : (
                  <Avatar sx={{ width: 20, height: 20 }}>
                    {page.pageName.charAt(0).toUpperCase()}
                  </Avatar>
                )
              }
            />
          ))}
        </Box>
      )}

      {error && <FormHelperText error>{error}</FormHelperText>}

      {pages.length === 0 && !loading && (
        <FormHelperText>
          No Facebook pages found. Please connect your Facebook account first.
        </FormHelperText>
      )}

      {selectedPages.length > 1 && (
        <FormHelperText>
          <Typography variant="caption" color="primary">
            💡 Tip: Use {"{Page Name}"} in your message to dynamically replace
            with each page's name
          </Typography>
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default FacebookMultiPageSelector;
