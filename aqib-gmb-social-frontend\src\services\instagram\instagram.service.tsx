import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import {
  IInstagramCreatePost,
  IInstagramBulkCreatePost,
  IInstagramSelectedAccount,
} from "../../interfaces/request/IInstagramCreatePost";
import {
  IInstagramCreatePostResponse,
  IInstagramAccountsResponse,
  IInstagramAuthResponse,
  IInstagramPostsResponse,
} from "../../interfaces/response/IInstagramCreatePostResponse";

class InstagramService {
  _httpHelperService: HttpHelperService | null;

  constructor(dispatch: Dispatch<Action> | null) {
    this._httpHelperService = dispatch ? new HttpHelperService(dispatch) : null;
  }

  /**
   * Initiate Instagram authentication
   * @param userId - User ID
   * @returns Authentication response with OAuth URL
   */
  authenticate = async (
    userId: number
  ): Promise<IInstagramAuthResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.post(
        `instagram/authenticate`,
        {
          userId,
        }
      );
      return response;
    } catch (error) {
      console.error("Error initiating Instagram authentication:", error);
      throw error;
    }
  };

  /**
   * Validate Instagram OAuth callback
   * @param code - Authorization code
   * @param state - State parameter
   * @returns Authentication validation response
   */
  validateCallback = async (
    code: string,
    state: string
  ): Promise<IInstagramAuthResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.post(
        `instagram/callback-validation`,
        {
          code,
          state,
        }
      );
      return response;
    } catch (error) {
      console.error("Error validating Instagram callback:", error);
      throw error;
    }
  };

  /**
   * Get Instagram accounts for user
   * @param userId - User ID
   * @returns Instagram accounts response
   */
  getAccounts = async (
    userId: number
  ): Promise<IInstagramAccountsResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.get(
        `instagram/pages/${userId}`
      );
      return response;
    } catch (error) {
      console.error("Error getting Instagram accounts:", error);
      throw error;
    }
  };

  /**
   * Create Instagram post
   * @param userId - User ID
   * @param postData - Post data
   * @returns Create post response
   */
  createPost = async (
    userId: number,
    postData: IInstagramCreatePost
  ): Promise<IInstagramCreatePostResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      const response = await this._httpHelperService.post(
        `instagram/posts/${userId}`,
        postData
      );
      return response;
    } catch (error) {
      console.error("Error creating Instagram post:", error);
      throw error;
    }
  };

  /**
   * Create bulk Instagram posts for multiple accounts
   * @param userId - User ID
   * @param bulkPostData - Bulk post data
   * @returns Array of create post responses
   */
  createBulkPosts = async (
    userId: number,
    bulkPostData: IInstagramBulkCreatePost
  ): Promise<IInstagramCreatePostResponse[]> => {
    if (!this._httpHelperService) return [];

    const results: IInstagramCreatePostResponse[] = [];

    for (const account of bulkPostData.accounts) {
      try {
        const postData: IInstagramCreatePost = {
          accountId: account.accountId,
          caption: bulkPostData.caption.replace(
            /{Account Name}/g,
            account.accountName
          ),
          mediaUrl: bulkPostData.mediaUrl,
          mediaType: bulkPostData.mediaType,
          published: bulkPostData.published,
          scheduledPublishTime: bulkPostData.scheduledPublishTime,
        };

        const response = await this.createPost(userId, postData);
        if (response) {
          results.push(response);
        }
      } catch (error) {
        console.error(
          `Error creating post for account ${account.accountId}:`,
          error
        );
        results.push({
          success: false,
          message: `Failed to create post for ${account.accountName}`,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  };

  /**
   * Get Instagram posts for user
   * @param userId - User ID
   * @param accountId - Account ID (optional)
   * @param page - Page number
   * @param limit - Limit per page
   * @returns Posts response
   */
  getPosts = async (
    userId: number,
    accountId?: string,
    page: number = 1,
    limit: number = 10
  ): Promise<IInstagramPostsResponse | null> => {
    if (!this._httpHelperService) return null;
    try {
      let url = `instagram/posts/${userId}?page=${page}&limit=${limit}`;
      if (accountId) {
        url += `&accountId=${accountId}`;
      }
      const response = await this._httpHelperService.get(url);
      return response;
    } catch (error) {
      console.error("Error getting Instagram posts:", error);
      throw error;
    }
  };

  // Legacy methods for backward compatibility
  getInstagramProfile = async (userId: number) => {
    if (!this._httpHelperService) return null;
    return await this._httpHelperService.get(`instagram/profile/${userId}`);
  };

  getInstagramInsights = async (userId: number, businessId: number) => {
    if (!this._httpHelperService) return null;
    return await this._httpHelperService.get(
      `instagram/insights/${userId}?businessId=${businessId}`
    );
  };

  getInstagramMedia = async (userId: number, businessId: number) => {
    if (!this._httpHelperService) return null;
    return await this._httpHelperService.get(
      `instagram/media/${userId}?businessId=${businessId}`
    );
  };

  updateInstagramStatus = async (businessId: number, status: boolean) => {
    if (!this._httpHelperService) return null;
    return await this._httpHelperService.put(`instagram/status/${businessId}`, {
      instagramSyncStatus: status,
    });
  };

  createInstagramPost = async (
    userId: number,
    postData: { caption: string; imageUrl: string; businessId: number }
  ) => {
    if (!this._httpHelperService) return null;

    console.log("Creating Instagram post with data:", postData);

    return await this._httpHelperService.post(
      `instagram/create-post/${userId}`,
      postData
    );
  };

  getInstagramPostHistory = async (userId: number, businessId: number) => {
    if (!this._httpHelperService) return null;
    return await this._httpHelperService.get(
      `instagram/posts/${userId}?businessId=${businessId}`
    );
  };

  // New method to check if Instagram is connected
  checkInstagramConnection = async (userId: number) => {
    if (!this._httpHelperService) return null;
    return await this._httpHelperService.get(
      `instagram/connection-status/${userId}`
    );
  };
}

export default InstagramService;
