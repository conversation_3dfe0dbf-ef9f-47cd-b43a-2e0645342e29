import React from "react";
import { <PERSON>, Typography, Avatar, Button, Grid } from "@mui/material";
import StarIcon from "@mui/icons-material/Star";
import GoogleIcon from "@mui/icons-material/Google";

const templates = [
  {
    title: "Feedback",
    name: "<PERSON>",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#FFA07A",
  },
  {
    title: "Client Testimonial",
    name: "<PERSON>",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#87CEFA",
  },
  {
    title: "<PERSON><PERSON>'s Love",
    name: "<PERSON>",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#333",
    color: "#fff",
  },
  {
    title: "Feedback",
    name: "<PERSON>",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#FFD700",
  },
  {
    title: "Customer Review",
    name: "<PERSON>",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#ADFF2F",
  },
  {
    title: "Client's Love",
    name: "<PERSON>",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#F08080",
  },
  {
    title: "Nice Words",
    name: "Sam Smith",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#9ACD32",
  },
  {
    title: "Customer Feedback",
    name: "Sam Smith",
    review: "It's a long established fact that a reader will be distracted.",
    stars: 5,
    avatar: "https://via.placeholder.com/100",
    bgColor: "#6495ED",
  },
];

const FeedbackTemplate = () => {
  return (
    <Box sx={{ padding: 4 }}>
      {/* Header */}
      <Typography variant="h4" fontWeight="bold" mb={4}>
        Popular Templates ✨
      </Typography>

      {/* Templates Grid */}
      <Grid container spacing={3}>
        {templates.map((template, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Box
              sx={{
                backgroundColor: template.bgColor,
                color: template.color || "#000",
                borderRadius: 2,
                padding: 3,
                textAlign: "center",
                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              }}
            >
              <Typography variant="h6" fontWeight="bold" mb={2}>
                {template.title}
              </Typography>
              <Avatar
                src={template.avatar}
                alt={template.name}
                sx={{ width: 80, height: 80, mx: "auto", mb: 2 }}
              />
              <Typography variant="body1" mb={2}>
                "{template.review}"
              </Typography>
              <Box display="flex" justifyContent="center" mb={2}>
                {[...Array(template.stars)].map((_, starIndex) => (
                  <StarIcon key={starIndex} sx={{ color: "#FFD700" }} />
                ))}
              </Box>
              <Box display="flex" alignItems="center" justifyContent="center">
                <GoogleIcon sx={{ color: "#4285F4", mr: 1 }} />
                <Typography variant="body2">Google Review</Typography>
              </Box>
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* Footer */}
      <Box
        mt={4}
        display="flex"
        justifyContent="center"
        gap={2}
        sx={{ flexWrap: "wrap" }}
      >
        <Button
          variant="contained"
          color="primary"
          sx={{ textTransform: "none" }} className="button-border-radius"
        >
          Create an Instant Post
        </Button>
        <Button variant="outlined" sx={{ textTransform: "none" }} className="button-border-radius">
          Download Image
        </Button>
      </Box>
    </Box>
  );
  // return (
  //   <Box sx={{ padding: 3 }}>
  //     {/* Title Section */}
  //     <Typography variant="h5" fontWeight="bold" mb={2}>
  //       Popular Templates ✨
  //     </Typography>

  //     {/* Template Grid */}
  //     <Box
  //       display="grid"
  //       gridTemplateColumns="repeat(auto-fill, minmax(200px, 1fr))"
  //       gap={2}
  //     >
  //       {/* Example Feedback Cards */}
  //       {[...Array(6)].map((_, index) => (
  //         <Box
  //           key={index}
  //           sx={{
  //             background: "#f4f4f4",
  //             borderRadius: 2,
  //             padding: 2,
  //             boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
  //             textAlign: "center",
  //           }}
  //         >
  //           <Typography variant="body1" fontWeight="bold" mb={1}>
  //             Customer Review
  //           </Typography>
  //           <Avatar
  //             sx={{
  //               width: 60,
  //               height: 60,
  //               mx: "auto",
  //               mb: 1,
  //             }}
  //             src="https://via.placeholder.com/150"
  //             alt="Avatar"
  //           />
  //           <Typography variant="body2" mb={1}>
  //             "Amazing service! Totally recommended."
  //           </Typography>
  //           <Box display="flex" justifyContent="center" mb={1}>
  //             {[...Array(5)].map((_, starIndex) => (
  //               <StarIcon key={starIndex} sx={{ color: "#FFD700" }} />
  //             ))}
  //           </Box>
  //           <Box display="flex" alignItems="center" justifyContent="center">
  //             <GoogleIcon sx={{ color: "#4285F4", mr: 1 }} />
  //             <Typography variant="body2">Google Review</Typography>
  //           </Box>
  //         </Box>
  //       ))}
  //     </Box>

  //     {/* Footer Section */}
  //     <Box
  //       mt={4}
  //       display="flex"
  //       justifyContent="center"
  //       gap={2}
  //       sx={{ flexWrap: "wrap" }}
  //     >
  //       <Button
  //         variant="contained"
  //         color="primary"
  //         sx={{ textTransform: "none" }}
  //       >
  //         Create an Instant Post
  //       </Button>
  //       <Button variant="outlined" sx={{ textTransform: "none" }}>
  //         Download Image
  //       </Button>
  //     </Box>
  //   </Box>
  // );
};

export default FeedbackTemplate;
