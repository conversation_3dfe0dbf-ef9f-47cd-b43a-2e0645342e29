const axios = require("axios");
const logger = require("../utils/logger");

class FacebookService {
  constructor() {
    // Use the latest Facebook Graph API version
    const apiVersion = process.env.FACEBOOK_API_VERSION || "v20.0";
    this.baseURL = `https://graph.facebook.com/${apiVersion}`;
    this.clientId = process.env.FACEBOOK_CLIENT_ID;
    this.clientSecret = process.env.FACEBOOK_CLIENT_SECRET;
    this.redirectUri = process.env.FACEBOOK_REDIRECT_URI;
  }

  /**
   * Generate OAuth URL for Facebook authentication
   * @param {number} userId - User ID
   * @returns {string} OAuth URL
   */
  generateAuthUrl(userId) {
    try {
      const state = JSON.stringify({ userId });
      // Use basic permissions for development, full permissions for production
      const isDevelopment = process.env.APP_ENV_NAME === "DEVELOPMENT";

      const scopes = isDevelopment
        ? ["public_profile", "email"]
        : [
            "public_profile",
            "email",
            "pages_show_list",
            "pages_manage_posts",
            "pages_read_engagement",
          ];

      // Use the same API version for OAuth dialog
      const apiVersion = process.env.FACEBOOK_API_VERSION || "v20.0";
      const authUrl =
        `https://www.facebook.com/${apiVersion}/dialog/oauth?` +
        `client_id=${this.clientId}&` +
        `redirect_uri=${encodeURIComponent(this.redirectUri)}&` +
        `scope=${encodeURIComponent(scopes.join(","))}&` +
        `response_type=code&` +
        `state=${encodeURIComponent(state)}`;

      logger.info("Facebook OAuth URL generated", {
        userId,
        scopes: scopes.join(","),
      });

      return authUrl;
    } catch (error) {
      logger.error("Error generating Facebook OAuth URL:", error);
      throw error;
    }
  }

  /**
   * Exchange authorization code for access token
   * @param {string} code - Authorization code
   * @returns {Object} Token data
   */
  async exchangeCodeForToken(code) {
    try {
      const response = await axios.get(`${this.baseURL}/oauth/access_token`, {
        params: {
          client_id: this.clientId,
          client_secret: this.clientSecret,
          redirect_uri: this.redirectUri,
          code: code,
        },
      });

      logger.info("Facebook token exchange successful", {
        hasAccessToken: !!response.data.access_token,
        expiresIn: response.data.expires_in,
      });

      return response.data;
    } catch (error) {
      logger.error("Error exchanging code for token:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get Facebook user information
   * @param {string} accessToken - User access token
   * @returns {Object} User information
   */
  async getUserInfo(accessToken) {
    try {
      const response = await axios.get(`${this.baseURL}/me`, {
        params: {
          access_token: accessToken,
          fields: "id,name,email,picture",
        },
      });

      logger.info("Facebook user info retrieved", {
        userId: response.data.id,
        hasEmail: !!response.data.email,
        hasName: !!response.data.name,
      });

      return response.data;
    } catch (error) {
      logger.error("Error getting user info:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get user's Facebook pages
   * @param {string} accessToken - User access token
   * @returns {Array} List of pages
   */
  async getUserPages(accessToken) {
    try {
      const response = await axios.get(`${this.baseURL}/me/accounts`, {
        params: {
          access_token: accessToken,
          fields: "id,name,access_token,category,picture",
        },
      });

      logger.info("Facebook pages retrieved", {
        pagesCount: response.data.data.length,
      });

      return response.data.data;
    } catch (error) {
      logger.error("Error getting user pages:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create a Facebook post
   * @param {string} pageId - Facebook page ID
   * @param {string} pageAccessToken - Page access token
   * @param {Object} postData - Post data
   * @returns {Object} Post creation response
   */
  async createPost(pageId, pageAccessToken, postData) {
    try {
      const endpoint = `${this.baseURL}/${pageId}/feed`;

      logger.info("Creating Facebook post", {
        pageId,
        hasMessage: !!postData.message,
        hasMedia: !!postData.photo,
        scheduled: !!postData.scheduled_publish_time,
      });

      const response = await axios.post(endpoint, postData, {
        params: { access_token: pageAccessToken },
      });

      logger.info("Facebook post created successfully", {
        pageId,
        postId: response.data.id,
      });

      return response.data;
    } catch (error) {
      logger.error("Error creating Facebook post:", {
        pageId,
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Upload photo to Facebook
   * @param {string} pageId - Facebook page ID
   * @param {string} pageAccessToken - Page access token
   * @param {Object} photoData - Photo data
   * @returns {Object} Photo upload response
   */
  async uploadPhoto(pageId, pageAccessToken, photoData) {
    try {
      const endpoint = `${this.baseURL}/${pageId}/photos`;

      logger.info("Uploading photo to Facebook", {
        pageId,
        hasUrl: !!photoData.url,
        hasCaption: !!photoData.caption,
      });

      // If photoData contains a URL, download the image first
      let uploadData = photoData;
      if (photoData.url) {
        try {
          logger.info("Downloading image from URL for Facebook upload", {
            url: photoData.url,
          });

          // Download the image from the URL
          const imageResponse = await axios.get(photoData.url, {
            responseType: "arraybuffer",
            timeout: 30000, // 30 second timeout
          });

          // Create form data with the image buffer
          const FormData = require("form-data");
          const formData = new FormData();

          // Add the image buffer
          formData.append("source", Buffer.from(imageResponse.data), {
            filename: "image.jpg",
            contentType: imageResponse.headers["content-type"] || "image/jpeg",
          });

          // Add caption if provided
          if (photoData.caption) {
            formData.append("caption", photoData.caption);
          }

          uploadData = formData;

          logger.info("Image downloaded and prepared for Facebook upload", {
            imageSize: imageResponse.data.byteLength,
            contentType: imageResponse.headers["content-type"],
          });
        } catch (downloadError) {
          logger.error("Error downloading image from URL", {
            url: photoData.url,
            error: downloadError.message,
          });
          throw new Error(`Failed to download image: ${downloadError.message}`);
        }
      }

      const response = await axios.post(endpoint, uploadData, {
        params: { access_token: pageAccessToken },
        headers: uploadData.getHeaders ? uploadData.getHeaders() : {},
      });

      logger.info("Photo uploaded to Facebook successfully", {
        pageId,
        photoId: response.data.id,
      });

      return response.data;
    } catch (error) {
      logger.error("Error uploading photo to Facebook:", {
        pageId,
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Generate Facebook post URL
   * @param {string} pageId - Facebook page ID
   * @param {string} postId - Facebook post ID
   * @returns {string} Facebook post URL
   */
  generatePostUrl(pageId, postId) {
    // Facebook post URLs follow the pattern: https://www.facebook.com/pageId/posts/postId
    // However, the postId from API is in format pageId_actualPostId, so we need to extract the actual post ID
    const actualPostId = postId.includes("_") ? postId.split("_")[1] : postId;
    return `https://www.facebook.com/${pageId}/posts/${actualPostId}`;
  }

  /**
   * Validate Facebook configuration
   * @returns {boolean} True if configuration is valid
   */
  validateConfig() {
    const isValid = !!(this.clientId && this.clientSecret && this.redirectUri);

    if (!isValid) {
      logger.error("Facebook configuration is incomplete", {
        hasClientId: !!this.clientId,
        hasClientSecret: !!this.clientSecret,
        hasRedirectUri: !!this.redirectUri,
      });
    }

    return isValid;
  }
}

module.exports = FacebookService;
