import React, { useState, useEffect } from "react";
import { Formik, Form, FormikProps } from "formik";
import CloseIcon from "@mui/icons-material/Close";
import * as yup from "yup";
import {
  Card,
  Dialog,
  CardContent,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip,
  Box,
  Button,
  DialogTitle,
  IconButton,
  DialogContent,
  TextField,
  InputAdornment,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import SearchIcon from "@mui/icons-material/Search";

const ServiceItemsDisplay = (props: { serviceItems: any }) => {
  const [serviceAreas, setServiceAreas] = useState([
    "Mathikere, Bengaluru, Karnataka, India",
    "Shivaji Nagar, Telangana 500018, India",
    "Yelahanka, Bengaluru, Karnataka, India",
    "Benson Town, Bengaluru, Karnataka, India",
    "Frazer Town, Bengaluru, Karnataka, India",
    "Kodigehalli, Bengaluru, Karnataka, India",
    "Sahakar Nagar, Bengaluru, Karnataka, India",
    "Vasanth Nagar, Bengaluru, Karnataka, India",
    "Vidyaranyapura, Bengaluru, Karnataka, India",
    "RT Nagar, Bengaluru, Karnataka 560032, India",
    "R.M.V. 2nd Stage, Bengaluru, Karnataka, India",
    "Anandnagar, Hebbal, Bengaluru, Karnataka 560024, India",
    "Ganganagar, RT Nagar, Bengaluru, Karnataka 560032, India",
    "Sadashiva Nagar, Armane Nagar, Bengaluru, Karnataka, India",
    "Munireddypalya, J.C.Nagar, Bengaluru, Karnataka 560006, India",
    "Chola Nagar, Anandnagar, Hebbal, Bengaluru, Karnataka 560024, India",
  ]);
  const [searchArea, setSearchArea] = useState("");

  const serviceAreaValidationSchema = yup.object({
    serviceAreas: yup
      .array()
      .of(yup.string())
      .min(1, "At least one service area is required"),
  });
  const formatServiceType = (id: string) => {
    const raw = id.split(":")[1] || "";
    return (
      raw &&
      raw
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  };

  return (
    <Card variant="elevation" sx={{ p: 2, boxShadow: "none", padding: "0px" }}>
      <CardContent sx={{ boxShadow: "none", padding: "0px" }}>
        <div style={{ flexWrap: "wrap", rowGap: "0px" }}>
          {props.serviceItems &&
            props.serviceItems.map((item: any, idx: number) => {
              let formatted = "";
              if (item.structuredServiceItem) {
                formatted = formatServiceType(
                  item.structuredServiceItem.serviceTypeId
                );
                return <Chip key={idx} label={formatted} variant="outlined" />;
              }

              if (item.freeFormServiceItem) {
                formatted = item.freeFormServiceItem.label.displayName;
                return (
                  <Accordion expanded className="commonBorderCard">
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography>{formatted}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          gap: "8px",
                        }}
                      >
                        <Typography>
                          {item.freeFormServiceItem.label.description}
                        </Typography>
                      </div>
                    </AccordionDetails>
                  </Accordion>
                );
              }
            })}
        </div>
      </CardContent>
    </Card>
  );
};

export default ServiceItemsDisplay;
