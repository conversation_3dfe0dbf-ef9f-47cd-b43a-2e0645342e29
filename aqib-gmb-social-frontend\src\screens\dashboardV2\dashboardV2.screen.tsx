import { FunctionComponent, useContext, useEffect, useState } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { Divider, Typography } from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import { RegisteredEmployeesChart } from "../../components/charts/registeredEmployees.charts";
import { ActiveJobsChart } from "../../components/charts/activeJobs.charts";
import { PieChart } from "../../components/charts/pie.charts";

//Icons
import GroupIcon from "@mui/icons-material/Group";
import WorkHistoryIcon from "@mui/icons-material/WorkHistory";
import EventAvailableIcon from "@mui/icons-material/EventAvailable";
import ScheduleIcon from "@mui/icons-material/Schedule";
import EditLocationAltIcon from "@mui/icons-material/EditLocationAlt";
import HelpIcon from "@mui/icons-material/Help";
import { useDispatch, useSelector } from "react-redux";
import ArrowUpwardRoundedIcon from "@mui/icons-material/ArrowUpwardRounded";
import ArrowDownwardRoundedIcon from "@mui/icons-material/ArrowDownwardRounded";

//Css Import
import "../dashboardV2/dashboardV2.screen.style.css";

// Image imports
import picture1Image from "../../assets/dashboard/Picture1.png";
import picture2Image from "../../assets/dashboard/Picture2.png";
import picture3Image from "../../assets/dashboard/Picture3.png";

import HomeChartCard from "../../components/homeChartCard/homeChartCard.component";
import RevenueChartDashboard from "../../components/revenueChartDashboard/revenueChartDashboard.component";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import { FormHelperText, Grid2, SelectChangeEvent } from "@mui/material";
import MenuItem from "@mui/material/MenuItem";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import { LoadingContext } from "../../context/loading.context";
import BusinessService from "../../services/business/business.service";
import DateFilter from "../../components/dateFilter/dateFilter.component";
import Button from "@mui/material/Button";
import BusinessInteractionsChart from "./businessInteractionsChart";
import PlatformBreakdownChart from "./platformBreakdownChart";
import SearchQueriesList from "./searchQueriesList";
import LocationMetricsService from "../../services/locationMetrics/locationMetrics.service";
import { ILocationMetricsRequestModel } from "../../interfaces/request/ILocationMetricsRequestModel";
import dayjs from "dayjs";
import WebsiteClicksChart from "./websiteClicksChart";
import SearchBreakdown from "./searchBreakdown";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import BusinessProfileInteractionsChart from "./businessProfileInteractionsChart";

interface EventCounts {
  type: string;
  total: number;
}

interface IGraphDataModel {
  data: number[];
  labels: string[];
}

const DashboardV2: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const [locationList, setLocationList] = useState<ILocation[]>([]);
  const { setLoading } = useContext(LoadingContext);
  const _businessService = new BusinessService(dispatch);
  const _locationMetricsService = new LocationMetricsService(dispatch);
  const _applicationHelperService = new ApplicationHelperService(dispatch);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const [selectedLocationId, setSelectedLocationId] = useState<string | null>(
    null
  );
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(
    null
  );
  const [selectedDateRange, setSelectedDateRange] = useState<{
    from: string;
    to: string;
    isSameMonthYear: boolean;
  } | null>(null);
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  const [count1, setCount1] = useState<EventCounts>({
    type: "Calls",
    total: 0,
  });
  const [count2, setCount2] = useState<EventCounts>({
    type: "Directions",
    total: 0,
  });
  const [count3, setCount3] = useState<EventCounts>({
    type: "Website clicks",
    total: 0,
  });

  const INITIAL_GRAPH_DATA: IGraphDataModel = { data: [], labels: [] };

  const [websiteData, setWebsiteData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  const [callData, setCallData] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  const [directionsData, setDirectionsData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  const [messagingClicks, setMessagingClicks] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  const [bookings, setBookings] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  useEffect(() => {
    fetchLocations();
  }, []);

  useEffect(() => {
    if (selectedLocationId) {
      setSelectedAccountId(
        locationList.filter((x) => x.gmbLocationId === selectedLocationId)[0]
          .gmbAccountId
      );
    }
  }, [selectedLocationId]);

  useEffect(() => {
    if (selectedLocationId && selectedDateRange && selectedAccountId) {
      fetchAnalyticsData(
        locationList.filter((x) => x.gmbLocationId === selectedLocationId)[0]
          .gmbAccountId,
        selectedLocationId,
        selectedDateRange.from,
        selectedDateRange.to,
        selectedDateRange.isSameMonthYear
      );
    }
  }, [selectedLocationId, selectedDateRange]);

  const VALIDATION_DAYS: number = 15;

  const transformMetricData = (
    rawData: any,
    metric: string,
    daysDifference: number,
    isSameMonthYear: boolean
  ) => {
    const metricSeries =
      rawData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.find(
        (series: any) => series.dailyMetric === metric
      );

    if (!metricSeries) return { data: [], labels: [] };

    const monthly: Record<string, number> = {};
    const daily: Record<string, number> = {};

    for (const entry of metricSeries.timeSeries.datedValues) {
      const { year, month, day } = entry.date;
      const key =
        daysDifference < VALIDATION_DAYS || isSameMonthYear
          ? dayjs(`${year}-${month}-${day}`).format("YYYY-MMM-DD")
          : dayjs(`${year}-${month}-01`).format("MMM YYYY");

      const value = parseInt(entry.value ?? "0", 10);
      if (daysDifference < VALIDATION_DAYS || isSameMonthYear) {
        daily[key] = (daily[key] || 0) + value;
      } else {
        monthly[key] = (monthly[key] || 0) + value;
      }
    }

    const labels =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? Object.keys(daily).sort()
        : Object.keys(monthly);
    const data =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? labels.map((label) => daily[label])
        : labels.map((label) => monthly[label]);

    return { data, labels };
  };

  const fetchAnalyticsData = async (
    accountId: string,
    locationId: string,
    from: string,
    to: string,
    isSameMonthYear: boolean
  ) => {
    const daysDifference: number = _applicationHelperService.getDaysDifference(
      from,
      to
    );

    try {
      setLoading(true);
      const requestObj: ILocationMetricsRequestModel = {
        startDate: from,
        endDate: to,
      };
      const getMetricsRequestHeader = {
        "x-gmb-account-id": accountId,
        "x-gmb-location-id": locationId,
      };
      let response: any = await _locationMetricsService.getLocationMetrics(
        requestObj,
        getMetricsRequestHeader
      );

      console.log("Analytics Data:", response.data);
      setAnalyticsData(response.data);

      const webSiteClicksData = transformMetricData(
        response.data,
        "WEBSITE_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setWebsiteData(webSiteClicksData);
      handleDataFromChild(webSiteClicksData, "Website clicks");

      const callData = transformMetricData(
        response.data,
        "CALL_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setCallData(callData);
      handleDataFromChild(callData, "Calls");

      const directionsData = transformMetricData(
        response.data,
        "BUSINESS_DIRECTION_REQUESTS",
        daysDifference,
        isSameMonthYear
      );
      setDirectionsData(directionsData);
      handleDataFromChild(directionsData, "Directions");

      setMessagingClicks(
        transformMetricData(
          response.data,
          "BUSINESS_CONVERSATIONS",
          daysDifference,
          isSameMonthYear
        )
      );
      setBookings(
        transformMetricData(
          response.data,
          "BUSINESS_FOOD_ORDERS",
          daysDifference,
          isSameMonthYear
        )
      );
    } catch (error: any) {
      console.error("Failed to fetch analytics data", error);
      setWebsiteData(INITIAL_GRAPH_DATA);
      setCallData(INITIAL_GRAPH_DATA);
      setDirectionsData(INITIAL_GRAPH_DATA);
      setMessagingClicks(INITIAL_GRAPH_DATA);
      setBookings(INITIAL_GRAPH_DATA);
      setCount1({ ...count1, total: 0 });
      setCount2({ ...count2, total: 0 });
      setCount3({ ...count3, total: 0 });
      setAnalyticsData(null);
      if (error.response.data.error) {
        setToastConfig(ToastSeverity.Error, error.response.data.error, true);
      } else if (error.response.data) {
        setToastConfig(ToastSeverity.Error, error.response.data.message, true);
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchLocations = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      console.log("Location List: ", locationListResponse.list);
      setLocationList(locationListResponse.list);
      setSelectedLocationId(locationListResponse.list[0].gmbLocationId);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleDataFromChild = (data: any, type: string) => {
    console.log("Data from child:", data, type);
    const total =
      data &&
      data.data &&
      data.data.reduce((sum: number, val: number) => sum + val, 0);
    if (type === "Calls") {
      setCount1({
        ...count1,
        total: total,
      });
    } else if (type === "Directions") {
      setCount2({ ...count2, total: total });
    } else if (type === "Website clicks") {
      setCount3({ ...count3, total: total });
    }
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box
              sx={{
                pr: 1,
              }}
            >
              <Box>
                <Box>
                  <Box sx={{ marginBottom: "5px" }}>
                    <h3 className="pageTitle">Dashboard</h3>
                    <Typography variant="subtitle2" className="subtitle2">
                      Hi, {userInfo && userInfo.name}. Welcome back to
                      MyLocoBiz!
                    </Typography>
                  </Box>
                  {/* <Divider style={{ margin: "10px 0", height: 0 }} /> */}
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6} lg={9}>
                      <FormControl
                        className="commonSelect"
                        variant="filled"
                        fullWidth
                      >
                        <InputLabel id="outlined-country-dropdown-label">
                          Location
                        </InputLabel>

                        <Select
                          fullWidth
                          id="locationId"
                          label="Location"
                          value={selectedLocationId ? selectedLocationId : ""}
                          onChange={(evt: SelectChangeEvent) => {
                            setSelectedLocationId(evt.target.value);
                          }}
                          sx={{
                            backgroundColor: "var(--whiteColor)",
                            borderRadius: "5px",
                          }}
                        >
                          <MenuItem value={0}>Select</MenuItem>
                          {locationList &&
                            locationList.map((location: ILocation) => (
                              <MenuItem
                                key={location.gmbLocationId}
                                value={location.gmbLocationId}
                              >
                                {location.gmbLocationName}
                              </MenuItem>
                            ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    {selectedLocationId && selectedAccountId && (
                      <Grid item xs={12} md={6} lg={3}>
                        <DateFilter
                          onDateChange={(range: any) => {
                            setSelectedDateRange(range);
                          }}
                        />
                      </Grid>
                    )}
                  </Grid>
                  <Divider style={{ margin: "10px 0", height: 0 }} />
                  <Box>
                    <Grid
                      container
                      spacing={2}
                      className="commonCardBottomSpacing"
                    >
                      <Grid item xs={12} md={4} lg={4}>
                        <Box className="commonCard dashboardTopIconCard">
                          <Box className="dashboardTopIcon">
                            <img
                              alt="MyLocoBiz - Logo"
                              className="innerImage1"
                              src={picture2Image}
                            />
                          </Box>
                          <Box className="dashboardTopInfo">
                            <Typography className="dashboardTopCount">
                              {count1.total}
                            </Typography>
                            <Typography className="dashboardTopTitle">
                              {count1.type}
                            </Typography>
                          </Box>
                          {/* <img
                            alt="MyLocoBiz - Logo"
                            className="innerImage"
                            src={picture2Image}
                          /> */}
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4} lg={4}>
                        <Box className="commonCard dashboardTopIconCard">
                          <Box className="dashboardTopIcon">
                            <img
                              alt="MyLocoBiz - Logo"
                              className="innerImage2"
                              src={picture1Image}
                            />
                          </Box>
                          <Box className="dashboardTopInfo">
                            <Typography className="dashboardTopCount">
                              {count2.total}
                            </Typography>
                            <Typography className="dashboardTopTitle">
                              {count2.type}
                            </Typography>
                          </Box>
                          {/* <img
                            alt="MyLocoBiz - Logo"
                            className="innerImage"
                            src={picture1Image}
                          /> */}
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={4} lg={4}>
                        <Box className="commonCard dashboardTopIconCard">
                          <Box className="dashboardTopIcon">
                            <img
                              alt="MyLocoBiz - Logo"
                              className="innerImage3"
                              src={picture3Image}
                            />
                          </Box>
                          <Box className="dashboardTopInfo">
                            <Typography className="dashboardTopCount">
                              {count3.total}
                            </Typography>
                            <Typography className="dashboardTopTitle">
                              {count3.type}
                            </Typography>
                          </Box>
                          {/* <img
                            alt="MyLocoBiz - Logo"
                            className="innerImage"
                            src={picture3Image}
                          /> */}
                        </Box>
                      </Grid>
                    </Grid>

                    <Grid
                      container
                      spacing={2}
                      className="commonCardBottomSpacing"
                    >
                      {analyticsData &&
                        analyticsData.multiDailyMetricTimeSeries && (
                          <Grid item xs={12} md={12}>
                            <BusinessProfileInteractionsChart
                              rawData={analyticsData}
                              isSameMonthYear={
                                selectedDateRange?.isSameMonthYear
                                  ? true
                                  : false
                              }
                            />
                          </Grid>
                        )}

                      <Grid item xs={12} md={6} lg={6}>
                        <Box className="commonCard height100 pieChartDiv">
                          <Box
                            sx={{
                              p: 2,
                              backgroundColor: "#fff",
                              borderRadius: 2,
                            }}
                          >
                            <PlatformBreakdownChart data={analyticsData} />
                          </Box>
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6} lg={6}>
                        <RevenueChartDashboard
                          data1={callData.data}
                          data2={directionsData.data}
                          labels={directionsData.labels}
                          title1="Call Data"
                          title2="Directions Data"
                          graphTitle="Directions Vs Calls"
                        />
                      </Grid>
                    </Grid>
                  </Box>
                  <Box>
                    <Grid
                      container
                      spacing={2}
                      className="commonCardBottomSpacing"
                    >
                      {/* <Grid item xs={12} md={8}>
                      <Box
                        sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
                      >
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          Business Interactions
                        </Typography>
                        <BusinessInteractionsChart data={analyticsData} />
                      </Box>
                    </Grid> */}

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...callData}
                          title="Call clicks made from your Business Profile"
                          graphTitle="Calls"
                          // onSendData={handleDataFromChild}
                          colorCode={0}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...messagingClicks}
                          title="Messaging clicks made from your Business Profile"
                          graphTitle="Messaging clicks"
                          // onSendData={handleDataFromChild}
                          colorCode={1}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...bookings}
                          title="Bookings made from your Business Profile"
                          graphTitle="Bookings"
                          // onSendData={handleDataFromChild}
                          colorCode={0}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...directionsData}
                          title="Direction requests made from your Business Profile"
                          graphTitle="Directions"
                          // onSendData={handleDataFromChild}
                          colorCode={1}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <WebsiteClicksChart
                          {...websiteData}
                          title="Website clicks made from your Business Profile"
                          graphTitle="Website clicks"
                          // onSendData={handleDataFromChild}
                          colorCode={0}
                        />
                      </Grid>

                      {/* <Grid item xs={12} md={6}>
                      <Box
                        sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
                      >
                        <Box sx={{ flexGrow: 1 }}>
                          <PlatformBreakdownChart data={analyticsData} />
                        </Box>
                      </Box>
                    </Grid> */}

                      <Grid item xs={12} md={6}>
                        <Box
                          className="height100"
                          sx={{
                            p: 2,
                            backgroundColor: "#fff",
                            borderRadius: 2,
                          }}
                        >
                          <Box sx={{ flexGrow: 1 }}>
                            <SearchBreakdown
                              locationId={selectedLocationId}
                              accountId={selectedAccountId}
                              from={selectedDateRange?.from}
                              to={selectedDateRange?.to}
                            />
                          </Box>
                        </Box>
                      </Grid>

                      {/* <Grid item xs={12}>
                      <Box
                        sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}
                      >
                        <Typography variant="h6" sx={{ mb: 2 }}>
                          Search Queries
                        </Typography>
                        <SearchQueriesList data={analyticsData} />
                      </Box>
                    </Grid> */}
                    </Grid>
                  </Box>
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default DashboardV2;
