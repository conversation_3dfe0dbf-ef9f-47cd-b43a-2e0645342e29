// Common interfaces for post status dialogs

export interface PostCreationProgress {
  percent: number;
  status: string;
}

// Google Post Status Interfaces
export interface ISelectionLocationWithPost {
  locationInfo: {
    businessName: string;
    accountName: string;
    locationName: string;
    status?: boolean;
    viewUrl?: string;
    errorMessage?: string;
  };
}

// Facebook Post Status Interfaces
export interface IFacebookPagePost {
  pageInfo: {
    pageName: string;
    pageId: string;
    status: boolean | null;
    postUrl?: string;
    errorMessage?: string;
  };
}

// Instagram Post Status Interfaces
export interface IInstagramAccountPost {
  accountInfo: {
    accountName: string;
    accountId: string;
    username: string;
    status: boolean | null;
    postUrl?: string;
    errorMessage?: string;
  };
}

// LinkedIn Post Status Interfaces
export interface ILinkedInProfilePost {
  profileInfo: {
    profileName: string;
    profileId: string;
    companyName?: string;
    status: boolean | null;
    postUrl?: string;
    errorMessage?: string;
  };
}

// Base dialog props interface
export interface BasePostStatusDialogProps {
  open: boolean;
  onClose: () => void;
  postCreationProgress: PostCreationProgress;
  allApiCallsCompleted: boolean;
  getProgressColor: () => string;
}

// Platform-specific dialog props
export interface GooglePostStatusDialogProps extends BasePostStatusDialogProps {
  selectedLocations: ISelectionLocationWithPost[];
}

export interface FacebookPostStatusDialogProps
  extends BasePostStatusDialogProps {
  selectedPages: IFacebookPagePost[];
}

export interface InstagramPostStatusDialogProps
  extends BasePostStatusDialogProps {
  selectedAccounts: IInstagramAccountPost[];
}

export interface LinkedInPostStatusDialogProps
  extends BasePostStatusDialogProps {
  selectedProfiles: ILinkedInProfilePost[];
}
