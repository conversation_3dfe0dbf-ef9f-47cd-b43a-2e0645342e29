#!/usr/bin/env node

/**
 * GMB Auto Reply Service
 * Windows service for automated Google My Business review replies and review synchronization
 */

require("dotenv").config();

const database = require("./config/database");
const schedulerService = require("./services/schedulerService");
const autoReplyModel = require("./models/autoReplyModel");
const {
  migrateAutoReplyLogTable,
} = require("./database/migrate_auto_reply_log");
const logger = require("./utils/logger");

class GMBAutoReplyService {
  constructor() {
    this.isShuttingDown = false;
    this.startTime = new Date();
  }

  /**
   * Initialize and start the service
   */
  async start() {
    try {
      logger.logService("STARTUP_START", {
        version: require("./package.json").version,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        startTime: this.startTime.toISOString(),
      });

      // Initialize database connection
      await this.initializeDatabase();

      // Setup database tables
      await this.setupDatabase();

      // Start the scheduler service
      await this.startScheduler();

      // Setup graceful shutdown handlers
      this.setupShutdownHandlers();

      logger.logService("STARTUP_COMPLETE", {
        message: "GMB Auto Reply Service started successfully",
        uptime: this.getUptime(),
      });

      console.log("GMB Auto Reply Service is running...");
      console.log("Features: Auto Reply + Review Sync + Analytics Sync");
      console.log(`Process ID: ${process.pid}`);
      console.log(`Started at: ${this.startTime.toISOString()}`);
      console.log(
        `Auto Reply Schedule: ${process.env.CRON_SCHEDULE || "*/5 * * * *"}`
      );
      console.log(
        `Review Sync Schedule: ${
          process.env.REVIEW_SYNC_SCHEDULE || "0 23 * * *"
        }`
      );
      console.log(
        `Analytics Sync Schedule: ${
          process.env.ANALYTICS_SYNC_SCHEDULE || "0 0 1 * * *"
        }`
      );
      console.log("Press Ctrl+C to stop the service");
    } catch (error) {
      logger.error("Failed to start GMB Auto Reply Service:", error);
      console.error("Failed to start service:", error.message);
      process.exit(1);
    }
  }

  /**
   * Initialize database connection
   */
  async initializeDatabase() {
    try {
      logger.logService("DB_INIT_START", {
        message: "Initializing database connection",
      });

      await database.initialize();

      logger.logService("DB_INIT_COMPLETE", {
        message: "Database connection initialized",
      });
    } catch (error) {
      logger.error("Database initialization failed:", error);
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  /**
   * Setup required database tables
   */
  async setupDatabase() {
    try {
      logger.logService("DB_SETUP_START", {
        message: "Setting up database tables",
      });

      // Run migration for auto_reply_log table (adds account_id and location_id columns)
      await migrateAutoReplyLogTable();

      // Create auto_reply_log table if it doesn't exist (fallback)
      await autoReplyModel.createAutoReplyLogTable();

      logger.logService("DB_SETUP_COMPLETE", {
        message: "Database tables setup complete",
      });
    } catch (error) {
      logger.error("Database setup failed:", error);
      throw new Error(`Database setup failed: ${error.message}`);
    }
  }

  /**
   * Start the scheduler service
   */
  async startScheduler() {
    try {
      logger.logService("SCHEDULER_START", {
        message: "Starting scheduler service",
      });

      schedulerService.start();

      logger.logService("SCHEDULER_STARTED", {
        message: "Scheduler service started",
        status: schedulerService.getStatus(),
      });
    } catch (error) {
      logger.error("Scheduler startup failed:", error);
      throw new Error(`Scheduler startup failed: ${error.message}`);
    }
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupShutdownHandlers() {
    const shutdownSignals = ["SIGINT", "SIGTERM", "SIGQUIT"];

    shutdownSignals.forEach((signal) => {
      process.on(signal, () => {
        logger.logService("SHUTDOWN_SIGNAL", {
          signal,
          message: `Received ${signal}, initiating graceful shutdown`,
        });
        this.shutdown();
      });
    });

    // Handle uncaught exceptions
    process.on("uncaughtException", (error) => {
      logger.error("Uncaught exception:", error);
      logger.logService("UNCAUGHT_EXCEPTION", {
        error: error.message,
        stack: error.stack,
      });
      this.shutdown(1);
    });

    // Handle unhandled promise rejections
    process.on("unhandledRejection", (reason, promise) => {
      logger.error("Unhandled promise rejection:", { reason, promise });
      logger.logService("UNHANDLED_REJECTION", {
        reason: reason?.message || reason,
        promise: promise.toString(),
      });
      this.shutdown(1);
    });
  }

  /**
   * Graceful shutdown
   */
  async shutdown(exitCode = 0) {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;

    try {
      logger.logService("SHUTDOWN_START", {
        exitCode,
        uptime: this.getUptime(),
        message: "Starting graceful shutdown",
      });

      console.log("Shutting down GMB Auto Reply Service...");

      // Stop the scheduler service
      if (schedulerService.isRunning) {
        logger.logService("SCHEDULER_STOP", {
          message: "Stopping scheduler service",
        });
        schedulerService.stop();
      }

      // Close database connections
      logger.logService("DB_CLOSE", {
        message: "Closing database connections",
      });
      await database.close();

      logger.logService("SHUTDOWN_COMPLETE", {
        exitCode,
        uptime: this.getUptime(),
        message: "Graceful shutdown completed",
      });

      console.log("GMB Auto Reply Service stopped successfully");
    } catch (error) {
      logger.error("Error during shutdown:", error);
      exitCode = 1;
    } finally {
      process.exit(exitCode);
    }
  }

  /**
   * Get service uptime in human readable format
   */
  getUptime() {
    const uptimeMs = Date.now() - this.startTime.getTime();
    const uptimeSeconds = Math.floor(uptimeMs / 1000);
    const hours = Math.floor(uptimeSeconds / 3600);
    const minutes = Math.floor((uptimeSeconds % 3600) / 60);
    const seconds = uptimeSeconds % 60;

    return `${hours}h ${minutes}m ${seconds}s`;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isRunning: !this.isShuttingDown,
      startTime: this.startTime.toISOString(),
      uptime: this.getUptime(),
      pid: process.pid,
      memoryUsage: process.memoryUsage(),
      scheduler: schedulerService.getStatus(),
    };
  }
}

// Create and start the service
const service = new GMBAutoReplyService();

// Start the service
service.start().catch((error) => {
  console.error("Failed to start service:", error);
  process.exit(1);
});

// Export for testing purposes
module.exports = service;
