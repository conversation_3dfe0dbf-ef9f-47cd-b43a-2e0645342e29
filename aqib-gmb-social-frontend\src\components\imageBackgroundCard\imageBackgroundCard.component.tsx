import React from "react";
import { Box, Typo<PERSON>, But<PERSON> } from "@mui/material";
import { CssBaseline, Container } from "@mui/material";
import FeedbackCard from "../feedbackCard/feedbackCard.component";

const ImageBackgroundCard = (props: {
  title: string;
  description: string;
  buttonText: string;
  imageUrl: string;
}) => {
  return (
    <Box
      sx={{
        backgroundImage: `url(${props.imageUrl})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        borderRadius: 2,
        color: "#fff",
        padding: 4,
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: 500,
        width: 300,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.3)",
      }}
    >
      <Container>
        <CssBaseline />
        <FeedbackCard />
      </Container>
    </Box>
  );
};

export default ImageBackgroundCard;
