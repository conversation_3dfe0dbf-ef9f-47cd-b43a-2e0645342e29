import {
  AUTH_REQUESTED,
  AUTH_SUCCESS,
  AUTH_LOGOUT,
  AUTH_ERROR,
  AUTH_UNAUTHORIZED,
} from "../constants/reducer.constant";
import { IRoleBasedAccessResponseModel } from "../interfaces/response/IRoleBasedAccessResponseModel";
import { ILoggedInUserResponseModel } from "../interfaces/response/ISignInResponseModel";

interface AuthState {
  userInfo: ILoggedInUserResponseModel | null;
  rbAccess: IRoleBasedAccessResponseModel | null;
  isLoading: boolean;
  success: boolean | null;
  loginMessage: string | null;
  token: string | null;
  isUnauthorized: boolean | null;
}

const initialState: AuthState = {
  userInfo: null,
  rbAccess: null,
  isLoading: false,
  success: false,
  loginMessage: "",
  token: null,
  isUnauthorized: false,
};

const authReducer = (prevState = initialState, action: any) => {
  switch (action.type) {
    case AUTH_REQUESTED:
      return {
        ...prevState,
        isLoading: true,
        success: true,
        loginMessage: null,
        isUnauthorized: null,
      };
    case AUTH_SUCCESS:
      return {
        ...prevState,
        userInfo: action.payload.result,
        token: action.payload.token,
        rbAccess: action.payload.rbAccess,
        isLoading: false,
      };
    case AUTH_LOGOUT:
      return { ...initialState };
    case AUTH_ERROR:
      return {
        ...initialState,
        userInfo: null,
        isLoading: false,
        success: false,
        loginMessage: action.payload,
      };
    case AUTH_UNAUTHORIZED:
      return {
        ...prevState,
        userInfo: null,
        isLoading: false,
        success: false,
        isUnauthorized: true,
      };
    default:
      return prevState;
  }
};

export default authReducer;
