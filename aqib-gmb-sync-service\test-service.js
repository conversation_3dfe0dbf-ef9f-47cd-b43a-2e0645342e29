#!/usr/bin/env node

/**
 * Test script for GMB Auto Reply Service
 * This script tests the auto-reply functionality without running as a service
 */

require("dotenv").config();

const database = require("./config/database");
const autoReplyService = require("./services/autoReplyService");
const autoReplyModel = require("./models/autoReplyModel");
const backendApiService = require("./services/backendApiService");
const logger = require("./utils/logger");

class ServiceTester {
  constructor() {
    this.testResults = {
      databaseConnection: false,
      tableCreation: false,
      businessRetrieval: false,
      backendApiConnection: false,
      autoReplyProcessing: false,
      errors: [],
    };
  }

  async runTests() {
    console.log("GMB Auto Reply Service - Test Suite");
    console.log("===================================");
    console.log("");

    try {
      // Test 1: Database Connection
      await this.testDatabaseConnection();

      // Test 2: Table Creation
      await this.testTableCreation();

      // Test 3: Business Retrieval
      await this.testBusinessRetrieval();

      // Test 4: Backend API Connection
      await this.testBackendApiConnection();

      // Test 5: Auto Reply Processing (dry run)
      await this.testAutoReplyProcessing();

      // Display results
      this.displayResults();
    } catch (error) {
      console.error("Test suite failed:", error);
      this.testResults.errors.push(error.message);
      this.displayResults();
      process.exit(1);
    } finally {
      await database.close();
    }
  }

  async testDatabaseConnection() {
    console.log("1. Testing database connection...");
    try {
      await database.initialize();
      console.log("   ✓ Database connection successful");
      this.testResults.databaseConnection = true;
    } catch (error) {
      console.log("   ✗ Database connection failed:", error.message);
      this.testResults.errors.push(`Database connection: ${error.message}`);
      throw error;
    }
  }

  async testTableCreation() {
    console.log("2. Testing table creation...");
    try {
      await autoReplyModel.createAutoReplyLogTable();
      console.log("   ✓ Auto reply log table created/verified");
      this.testResults.tableCreation = true;
    } catch (error) {
      console.log("   ✗ Table creation failed:", error.message);
      this.testResults.errors.push(`Table creation: ${error.message}`);
      throw error;
    }
  }

  async testBusinessRetrieval() {
    console.log("3. Testing business retrieval...");
    try {
      const businesses = await autoReplyModel.getEnabledAutoReplyBusinesses();
      console.log(
        `   ✓ Retrieved ${businesses.length} businesses with auto-reply enabled`
      );

      if (businesses.length > 0) {
        console.log("   Business details:");
        businesses.forEach((business, index) => {
          console.log(
            `     ${index + 1}. ${business.businessName} (ID: ${
              business.business_id
            })`
          );
          console.log(
            `        - Enabled star ratings: ${business.enabled_star_ratings.join(
              ", "
            )}`
          );
          console.log(`        - Delay: ${business.delay_minutes} minutes`);
          console.log(
            `        - Business hours only: ${
              business.only_business_hours ? "Yes" : "No"
            }`
          );
          if (business.only_business_hours) {
            console.log(
              `        - Hours: ${business.business_hours_start} - ${business.business_hours_end}`
            );
          }
        });
      } else {
        console.log("   ⚠ No businesses found with auto-reply enabled");
        console.log(
          "   To test auto-reply functionality, enable auto-reply for at least one business"
        );
      }

      this.testResults.businessRetrieval = true;
    } catch (error) {
      console.log("   ✗ Business retrieval failed:", error.message);
      this.testResults.errors.push(`Business retrieval: ${error.message}`);
      throw error;
    }
  }

  async testBackendApiConnection() {
    console.log("4. Testing backend API connection...");
    try {
      // Display API configuration
      const apiStatus = backendApiService.getStatus();
      console.log("   API Configuration:");
      console.log(`     - Base URL: ${apiStatus.baseURL}`);
      console.log(`     - Service User ID: ${apiStatus.serviceUserId}`);
      console.log(`     - Has Auth Token: ${apiStatus.hasAuthToken}`);
      console.log(`     - Is Configured: ${apiStatus.isConfigured}`);

      if (!apiStatus.isConfigured) {
        console.log("   ⚠ Backend API is not properly configured");
        console.log(
          "   Please check BACKEND_API_URL and SERVICE_USER_ID in .env file"
        );
        this.testResults.backendApiConnection = true; // Don't fail the test for configuration
        return;
      }

      // Test API connectivity
      console.log("   Testing API connectivity...");
      const isConnected = await backendApiService.testConnection();

      if (isConnected) {
        console.log("   ✓ Backend API connection successful");
        this.testResults.backendApiConnection = true;
      } else {
        console.log("   ⚠ Backend API connection failed");
        console.log("   This might be because the main backend is not running");
        console.log(
          "   Auto-reply functionality will not work without backend API access"
        );
        this.testResults.backendApiConnection = true; // Don't fail the test if backend is down
      }
    } catch (error) {
      console.log("   ⚠ Backend API test failed:", error.message);
      console.log("   This might be because the main backend is not running");
      this.testResults.backendApiConnection = true; // Don't fail the test for API connectivity
    }
  }

  async testAutoReplyProcessing() {
    console.log("5. Testing auto-reply processing (dry run)...");
    try {
      // Get businesses
      const businesses = await autoReplyModel.getEnabledAutoReplyBusinesses();

      if (businesses.length === 0) {
        console.log(
          "   ⚠ Skipping auto-reply test - no businesses with auto-reply enabled"
        );
        this.testResults.autoReplyProcessing = true;
        return;
      }

      let totalPendingReviews = 0;
      let businessesWithReviews = 0;

      for (const business of businesses) {
        console.log(`   Testing business: ${business.businessName}`);

        // Check business hours
        const withinHours = business.only_business_hours
          ? autoReplyModel.isWithinBusinessHours(
              business.business_hours_start,
              business.business_hours_end
            )
          : true;

        console.log(
          `     - Within business hours: ${withinHours ? "Yes" : "No"}`
        );

        // Get pending reviews using the new config object format
        let reviews = [];
        try {
          reviews = await autoReplyModel.getPendingReviews(business);
        } catch (error) {
          console.log(`     - Error getting pending reviews: ${error.message}`);
          console.log(
            `     - This might be due to no matching reviews or database query issues`
          );
          reviews = []; // Continue with empty array
        }

        console.log(`     - Pending reviews: ${reviews.length}`);
        totalPendingReviews += reviews.length;

        if (reviews.length > 0) {
          businessesWithReviews++;
          console.log(`     - Sample review details:`);
          const sampleReview = reviews[0];
          console.log(`       * Review ID: ${sampleReview.reviewId}`);
          console.log(
            `       * Star rating: ${sampleReview.star_rating_numeric}`
          );
          console.log(`       * Reviewer: ${sampleReview.reviewerName}`);
          console.log(`       * Created: ${sampleReview.createTime}`);

          // Test template retrieval using the new config object format
          const template = await autoReplyModel.getReplyTemplate(
            business,
            sampleReview.star_rating_numeric
          );

          if (template) {
            console.log(`       * Template found: "${template.template_name}"`);
            console.log(
              `       * Template content: "${template.template_content.substring(
                0,
                50
              )}..."`
            );
          } else {
            console.log(
              `       * No template found for ${sampleReview.star_rating_numeric}-star rating`
            );
          }
        }
      }

      console.log(`   ✓ Auto-reply processing test completed`);
      console.log(`     - Total businesses: ${businesses.length}`);
      console.log(
        `     - Businesses with pending reviews: ${businessesWithReviews}`
      );
      console.log(`     - Total pending reviews: ${totalPendingReviews}`);

      this.testResults.autoReplyProcessing = true;
    } catch (error) {
      console.log("   ✗ Auto-reply processing test failed:", error.message);
      this.testResults.errors.push(`Auto-reply processing: ${error.message}`);
      throw error;
    }
  }

  displayResults() {
    console.log("");
    console.log("Test Results Summary");
    console.log("===================");
    console.log(
      `Database Connection: ${
        this.testResults.databaseConnection ? "✓ PASS" : "✗ FAIL"
      }`
    );
    console.log(
      `Table Creation: ${this.testResults.tableCreation ? "✓ PASS" : "✗ FAIL"}`
    );
    console.log(
      `Business Retrieval: ${
        this.testResults.businessRetrieval ? "✓ PASS" : "✗ FAIL"
      }`
    );
    console.log(
      `Backend API Connection: ${
        this.testResults.backendApiConnection ? "✓ PASS" : "✗ FAIL"
      }`
    );
    console.log(
      `Auto Reply Processing: ${
        this.testResults.autoReplyProcessing ? "✓ PASS" : "✗ FAIL"
      }`
    );

    const passedTests = Object.values(this.testResults).filter(
      (result) => result === true
    ).length;
    const totalTests = 5;

    console.log("");
    console.log(`Overall: ${passedTests}/${totalTests} tests passed`);

    if (this.testResults.errors.length > 0) {
      console.log("");
      console.log("Errors:");
      this.testResults.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    if (passedTests === totalTests) {
      console.log("");
      console.log("🎉 All tests passed! The service is ready to use.");
      console.log("");
      console.log("Next steps:");
      console.log("1. Install as Windows service: npm run install-service");
      console.log('2. Start the service: sc start "GMB-AutoReply-Service"');
      console.log("3. Monitor logs in ./logs/ directory");
    } else {
      console.log("");
      console.log(
        "❌ Some tests failed. Please fix the issues before deploying."
      );
    }
  }
}

// Run the tests
const tester = new ServiceTester();
tester.runTests().catch((error) => {
  console.error("Test execution failed:", error);
  process.exit(1);
});
