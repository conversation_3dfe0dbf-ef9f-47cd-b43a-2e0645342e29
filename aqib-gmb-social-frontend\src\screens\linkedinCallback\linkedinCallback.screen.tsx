import React, { useEffect, useState, useContext } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  LinearProgress,
  Card,
  CardContent,
  Button,
} from "@mui/material";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import LinkedInService from "../../services/linkedin/linkedin.service";
import { useDispatch } from "react-redux";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";

const LinkedInCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { setToastConfig } = useContext(ToastContext);

  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagesCount, setPagesCount] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);

  const linkedinService = React.useMemo(
    () => new LinkedInService(dispatch),
    [dispatch]
  );

  const steps = [
    "Connecting to LinkedIn...",
    "Validating credentials...",
    "Retrieving profile information...",
    "Finalizing connection...",
  ];

  useEffect(() => {
    const handleCallback = async () => {
      // Prevent multiple simultaneous calls
      if (isProcessing) return;
      setIsProcessing(true);

      try {
        const code = searchParams.get("code");
        const state = searchParams.get("state");
        const errorParam = searchParams.get("error");
        const errorDescription = searchParams.get("error_description");

        // Handle OAuth errors
        if (errorParam) {
          setError(
            errorDescription || errorParam || "LinkedIn authentication failed"
          );
          setIsProcessing(false);
          return;
        }

        if (!code || !state) {
          setError("Missing authorization code or state parameter");
          setIsProcessing(false);
          return;
        }

        // Step 1: Connecting to LinkedIn
        setCurrentStep(0);
        setProgress(25);
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Step 2: Validating credentials
        setCurrentStep(1);
        setProgress(50);
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Step 3: Retrieving profile information
        setCurrentStep(2);
        setProgress(75);

        const response = await linkedinService.callbackValidation(code, state);

        if (response?.success) {
          // Step 4: Finalizing connection
          setCurrentStep(3);
          setProgress(100);
          await new Promise((resolve) => setTimeout(resolve, 500));

          setSuccess(true);
          setPagesCount(1); // LinkedIn typically has one profile

          setToastConfig(
            ToastSeverity.Success,
            "LinkedIn connected successfully!",
            true
          );

          console.log("LinkedIn authentication successful:", response.data);

          // Redirect to create posts page after a short delay
          setTimeout(() => {
            navigate("/post-management/create-social-post?tab=5");
          }, 2500);
        } else {
          setError(
            response?.message || "Failed to validate LinkedIn authentication"
          );
          setIsProcessing(false);
        }
      } catch (error: any) {
        console.error("LinkedIn callback error:", error);
        setError(
          error.response?.data?.message ||
            error.message ||
            "An unexpected error occurred"
        );
        setIsProcessing(false);
      }
    };

    // Only run once when component mounts or when search params change
    if (!isProcessing && !success && !error) {
      handleCallback();
    }
  }, [
    searchParams,
    isProcessing,
    success,
    error,
    linkedinService,
    setToastConfig,
  ]);

  const handleRetry = () => {
    navigate("/post-management/create-social-post?tab=5");
  };

  const handleGoBack = () => {
    navigate("/post-management/create-social-post");
  };

  if (error) {
    return (
      <Box
        sx={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f5f5f5",
          padding: 2,
        }}
      >
        <Card sx={{ maxWidth: 500, width: "100%", textAlign: "center" }}>
          <CardContent sx={{ padding: 4 }}>
            <ErrorIcon sx={{ fontSize: 64, color: "#f44336", mb: 2 }} />
            <Typography variant="h5" gutterBottom color="error">
              LinkedIn Connection Failed
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              {error}
            </Typography>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "center" }}>
              <Button
                variant="contained"
                onClick={handleRetry}
                sx={{
                  backgroundColor: "#0077B5",
                  "&:hover": { backgroundColor: "#005885" },
                }}
              >
                Try Again
              </Button>
              <Button variant="outlined" onClick={handleGoBack}>
                Go Back
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  if (success) {
    return (
      <Box
        sx={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundColor: "#f5f5f5",
          padding: 2,
        }}
      >
        <Card sx={{ maxWidth: 500, width: "100%", textAlign: "center" }}>
          <CardContent sx={{ padding: 4 }}>
            <CheckCircleIcon sx={{ fontSize: 64, color: "#4caf50", mb: 2 }} />
            <Typography variant="h5" gutterBottom color="success.main">
              LinkedIn Connected Successfully!
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              Your LinkedIn profile has been connected successfully.
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Redirecting to create posts page...
            </Typography>
            <LinearProgress
              variant="determinate"
              value={100}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: "#e0e0e0",
                "& .MuiLinearProgress-bar": {
                  backgroundColor: "#0077B5",
                },
              }}
            />
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
        padding: 2,
      }}
    >
      <Card sx={{ maxWidth: 500, width: "100%", textAlign: "center" }}>
        <CardContent sx={{ padding: 4 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 3,
            }}
          >
            <LinkedInIcon sx={{ fontSize: 48, color: "#0077B5", mr: 2 }} />
            <img
              src="/LocoBizIcon.png"
              alt="LocoBiz"
              style={{ width: 48, height: 48 }}
            />
          </Box>
          <Typography variant="h5" gutterBottom>
            Connecting to LinkedIn
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            {steps[currentStep]}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: "#e0e0e0",
              "& .MuiLinearProgress-bar": {
                backgroundColor: "#0077B5",
              },
            }}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            {progress}% Complete
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LinkedInCallback;
