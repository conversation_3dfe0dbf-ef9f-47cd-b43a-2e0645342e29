const logger = require('./logger');

/**
 * Validates that all required environment variables are set
 * @param {string} environment - The current environment (development, staging, production)
 */
function validateEnvironment(environment) {
    const requiredVars = [
        'APP_PORT',
        'APP_ENV_NAME',
        'APP_VER_PREFIX',
        'APP_LOG_LEVEL',
        'APP_DB_HOST',
        'APP_DB_USER',
        'APP_DB_PASSWORD',
        'APP_DB_NAME',
        'APP_AWS_ACCESS_KEY_ID',
        'APP_AWS_SECRET_ACCESS_KEY',
        'APP_AWS_REGION',
        'APP_AWS_S3_BUCKET',
        'APP_JWT_SECRET_KEY'
    ];

    const missingVars = [];
    const warnings = [];

    // Check required variables
    requiredVars.forEach(varName => {
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    });

    // Environment-specific validations
    if (environment === 'production') {
        // Production should use ERROR log level
        if (process.env.APP_LOG_LEVEL !== 'ERROR') {
            warnings.push('APP_LOG_LEVEL should be set to ERROR in production');
        }

        // Production should not use localhost URLs
        const urlVars = ['FRONTEND_URL', 'UI_ORIGIN', 'FACEBOOK_REDIRECT_URI', 'INSTAGRAM_REDIRECT_URI', 'LINKEDIN_REDIRECT_URI'];
        urlVars.forEach(varName => {
            if (process.env[varName] && process.env[varName].includes('localhost')) {
                warnings.push(`${varName} should not use localhost in production`);
            }
        });
    }

    if (environment === 'development') {
        // Development should use localhost URLs
        const urlVars = ['FRONTEND_URL', 'UI_ORIGIN'];
        urlVars.forEach(varName => {
            if (process.env[varName] && !process.env[varName].includes('localhost')) {
                warnings.push(`${varName} should use localhost in development`);
            }
        });
    }

    // Log results
    if (missingVars.length > 0) {
        logger.error('Missing required environment variables:', { 
            environment,
            missingVars 
        });
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(varName => {
            console.error(`   - ${varName}`);
        });
        process.exit(1);
    }

    if (warnings.length > 0) {
        logger.warn('Environment configuration warnings:', { 
            environment,
            warnings 
        });
        console.warn('⚠️  Environment configuration warnings:');
        warnings.forEach(warning => {
            console.warn(`   - ${warning}`);
        });
    }

    // Success message
    logger.info('Environment validation passed', { 
        environment,
        totalVarsChecked: requiredVars.length 
    });
    console.log(`✅ Environment validation passed for: ${environment.toUpperCase()}`);
}

/**
 * Displays current environment configuration (without sensitive data)
 */
function displayEnvironmentInfo() {
    const environment = process.env.NODE_ENV || 'development';
    
    console.log('\n📋 Current Environment Configuration:');
    console.log(`   Environment: ${environment.toUpperCase()}`);
    console.log(`   App Environment: ${process.env.APP_ENV_NAME}`);
    console.log(`   Port: ${process.env.APP_PORT}`);
    console.log(`   Log Level: ${process.env.APP_LOG_LEVEL}`);
    console.log(`   Database: ${process.env.APP_DB_NAME} @ ${process.env.APP_DB_HOST}`);
    console.log(`   S3 Bucket: ${process.env.APP_AWS_S3_BUCKET}`);
    console.log(`   Frontend URL: ${process.env.FRONTEND_URL}`);
    console.log('');
}

module.exports = {
    validateEnvironment,
    displayEnvironmentInfo
};
