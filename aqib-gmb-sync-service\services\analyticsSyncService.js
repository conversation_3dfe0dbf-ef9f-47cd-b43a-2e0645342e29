const axios = require("axios");
const database = require("../config/database");
const logger = require("../utils/logger");

class AnalyticsSyncService {
  constructor() {
    this.baseURL = process.env.BACKEND_API_URL || "http://localhost:3000";
    this.timeout = parseInt(process.env.BACKEND_API_TIMEOUT) || 30000;
    this.serviceUserId = process.env.SERVICE_USER_ID || "52";
    this.serviceAuthToken = process.env.SERVICE_AUTH_TOKEN;

    // Analytics sync configuration
    this.daysToSync = parseInt(process.env.ANALYTICS_DAYS_TO_SYNC) || 7;
    this.newLocationSyncDays =
      parseInt(process.env.ANALYTICS_NEW_LOCATION_DAYS) || 365; // 1 year for new locations
    this.batchSize = parseInt(process.env.ANALYTICS_BATCH_SIZE) || 10;
    this.retentionDays = parseInt(process.env.ANALYTICS_RETENTION_DAYS) || 365;

    // Statistics
    this.stats = {
      totalSynced: 0,
      totalFailed: 0,
      lastSyncTime: null,
      lastSyncDuration: 0,
      locationsProcessed: 0,
      metricsInserted: 0,
      errors: [],
    };

    // Supported metrics
    this.SUPPORTED_METRICS = [
      "WEBSITE_CLICKS",
      "CALL_CLICKS",
      "BUSINESS_DIRECTION_REQUESTS",
      "BUSINESS_IMPRESSIONS_DESKTOP_MAPS",
      "BUSINESS_IMPRESSIONS_DESKTOP_SEARCH",
      "BUSINESS_IMPRESSIONS_MOBILE_MAPS",
      "BUSINESS_IMPRESSIONS_MOBILE_SEARCH",
      "BUSINESS_CONVERSATIONS",
      "BUSINESS_FOOD_ORDERS",
    ];

    // Create axios instance
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "GMB-Analytics-Sync-Service/1.0",
      },
    });

    this.setupInterceptors();
  }

  /**
   * Setup axios interceptors for logging
   */
  setupInterceptors() {
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.logAnalyticsSync("API_REQUEST", {
          method: config.method?.toUpperCase(),
          url: config.url,
          headers: this.sanitizeHeaders(config.headers),
        });
        return config;
      },
      (error) => {
        logger.error("Analytics API request error:", error);
        return Promise.reject(error);
      }
    );

    this.apiClient.interceptors.response.use(
      (response) => {
        logger.logAnalyticsSync("API_RESPONSE", {
          status: response.status,
          url: response.config.url,
          success: true,
        });
        return response;
      },
      (error) => {
        logger.error("Analytics API response error:", {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Sanitize headers for logging
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    if (sanitized.Authorization) {
      sanitized.Authorization = "Bearer ***";
    }
    if (sanitized["authentication-token"]) {
      sanitized["authentication-token"] = "***";
    }
    return sanitized;
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const headers = {};
    if (this.serviceAuthToken) {
      headers["authentication-token"] = this.serviceAuthToken;
    }
    return headers;
  }

  /**
   * Sync analytics for all locations
   */
  async syncAllAnalytics() {
    const startTime = Date.now();
    this.resetStats();

    try {
      logger.logAnalyticsSync("SYNC_START", {
        message: "Starting analytics sync for all locations",
        daysToSync: this.daysToSync,
        batchSize: this.batchSize,
      });

      // Get locations that need sync
      const locations = await this.getLocationsThatNeedSync();

      if (locations.length === 0) {
        logger.logAnalyticsSync("NO_LOCATIONS", {
          message: "No locations need analytics sync at this time",
        });
        return this.getStats();
      }

      logger.logAnalyticsSync("LOCATIONS_FOUND", {
        count: locations.length,
        message: `Found ${locations.length} locations that need analytics sync`,
      });

      // Process locations in batches
      await this.processLocationsBatch(locations);

      // Clean up old data
      await this.cleanupOldData();

      // Update stats
      this.stats.lastSyncTime = new Date().toISOString();
      this.stats.lastSyncDuration = Date.now() - startTime;

      logger.logAnalyticsSync("SYNC_COMPLETE", {
        duration: this.stats.lastSyncDuration,
        locationsProcessed: this.stats.locationsProcessed,
        totalSynced: this.stats.totalSynced,
        totalFailed: this.stats.totalFailed,
        metricsInserted: this.stats.metricsInserted,
      });

      return this.getStats();
    } catch (error) {
      this.stats.lastSyncDuration = Date.now() - startTime;
      this.stats.errors.push({
        timestamp: new Date().toISOString(),
        error: error.message,
        type: "SYNC_ERROR",
      });

      logger.error("Error during analytics sync:", error);
      throw error;
    }
  }

  /**
   * Process locations in batches
   */
  async processLocationsBatch(locations) {
    for (let i = 0; i < locations.length; i += this.batchSize) {
      const batch = locations.slice(i, i + this.batchSize);

      logger.logAnalyticsSync("BATCH_START", {
        batchNumber: Math.floor(i / this.batchSize) + 1,
        totalBatches: Math.ceil(locations.length / this.batchSize),
        batchSize: batch.length,
      });

      // Process batch in parallel
      const batchPromises = batch.map((location) =>
        this.syncLocationAnalytics(location)
      );

      const batchResults = await Promise.allSettled(batchPromises);

      // Process results
      batchResults.forEach((result, index) => {
        const location = batch[index];
        if (result.status === "fulfilled" && result.value.success) {
          if (result.value.skipped) {
            // Location was skipped (no new data to sync)
            logger.logAnalyticsSync("LOCATION_SKIPPED", {
              locationId: location.gmbLocationId,
              locationName: location.gmbLocationName,
              reason: result.value.reason,
            });
          } else {
            this.stats.totalSynced++;
            this.stats.metricsInserted += result.value.metricsInserted || 0;
          }
        } else {
          this.stats.totalFailed++;
          const error =
            result.status === "rejected" ? result.reason : result.value.error;
          this.stats.errors.push({
            timestamp: new Date().toISOString(),
            locationId: location.gmbLocationId,
            locationName: location.gmbLocationName,
            error: error?.message || error,
            type: "LOCATION_SYNC_ERROR",
          });
        }
        this.stats.locationsProcessed++;
      });

      // Add delay between batches to be respectful to the API
      if (i + this.batchSize < locations.length) {
        await this.delay(2000);
      }
    }
  }

  /**
   * Sync analytics for a specific location
   */
  async syncLocationAnalytics(location) {
    const startTime = Date.now();
    let syncLogId = null;

    try {
      const { gmbLocationId, gmbAccountId, gmbLocationName, accessToken } =
        location;

      logger.logAnalyticsSync("LOCATION_SYNC_START", {
        locationId: gmbLocationId,
        locationName: gmbLocationName,
        accountId: gmbAccountId,
      });

      // Calculate date range based on last sync date
      const endDate = new Date();
      const startDate = new Date();

      if (location.last_sync_date) {
        // Existing location: sync from last sync date to current date
        const lastSyncDate = new Date(location.last_sync_date);
        startDate.setTime(lastSyncDate.getTime());
        // Add one day to avoid duplicate data
        startDate.setDate(startDate.getDate() + 1);

        logger.logAnalyticsSync("EXISTING_LOCATION_SYNC", {
          locationId: gmbLocationId,
          locationName: gmbLocationName,
          lastSyncDate: location.last_sync_date,
          message: `Syncing from last sync date: ${location.last_sync_date}`,
        });
      } else {
        // New location: sync last 1 year of data
        startDate.setDate(endDate.getDate() - this.newLocationSyncDays);

        logger.logAnalyticsSync("NEW_LOCATION_SYNC", {
          locationId: gmbLocationId,
          locationName: gmbLocationName,
          daysToSync: this.newLocationSyncDays,
          message: `New location - syncing last ${this.newLocationSyncDays} days of data`,
        });
      }

      const startDateStr = startDate.toISOString().split("T")[0];
      const endDateStr = endDate.toISOString().split("T")[0];

      // Validate date range
      if (startDate >= endDate) {
        logger.logAnalyticsSync("SKIP_SYNC", {
          locationId: gmbLocationId,
          locationName: gmbLocationName,
          reason: "No new data to sync - last sync date is current or future",
          lastSyncDate: location.last_sync_date,
        });

        return {
          success: true,
          metricsInserted: 0,
          duration: 0,
          skipped: true,
          reason: "No new data to sync",
        };
      }

      // Log sync operation start
      syncLogId = await this.logSyncOperation({
        gmbLocationId,
        gmbAccountId,
        syncDate: endDateStr,
        startDate: startDateStr,
        endDate: endDateStr,
        status: "pending",
      });

      // Fetch analytics data from API
      const analyticsData = await this.fetchAnalyticsFromAPI(
        gmbLocationId,
        gmbAccountId,
        startDateStr,
        endDateStr,
        accessToken
      );

      if (!analyticsData || !analyticsData.multiDailyMetricTimeSeries) {
        throw new Error("No analytics data received from API");
      }

      // Transform and store data
      const metricsData = this.transformAnalyticsData(
        analyticsData,
        gmbLocationId,
        gmbAccountId
      );

      let metricsInserted = 0;
      if (metricsData.length > 0) {
        metricsInserted = await this.batchInsertMetrics(metricsData);
      }

      // Update sync log as successful
      const duration = Date.now() - startTime;
      await this.updateSyncLog(syncLogId, {
        status: "success",
        metricsSynced: metricsInserted,
        apiCallsMade: 1,
        syncDurationSeconds: Math.round(duration / 1000),
      });

      // Update last sync date
      await this.updateLastSyncDate(gmbLocationId, endDateStr);

      logger.logAnalyticsSync("LOCATION_SYNC_SUCCESS", {
        locationId: gmbLocationId,
        locationName: gmbLocationName,
        metricsInserted,
        duration: Math.round(duration / 1000),
      });

      return {
        success: true,
        metricsInserted,
        duration: Math.round(duration / 1000),
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      // Update sync log as failed
      if (syncLogId) {
        await this.updateSyncLog(syncLogId, {
          status: "failed",
          errorMessage: error.message,
          syncDurationSeconds: Math.round(duration / 1000),
        });
      }

      logger.error("Location analytics sync failed:", {
        locationId: location.gmbLocationId,
        locationName: location.gmbLocationName,
        error: error.message,
        duration: Math.round(duration / 1000),
      });

      return {
        success: false,
        error: error,
        duration: Math.round(duration / 1000),
      };
    }
  }

  /**
   * Fetch analytics data from the main API
   */
  async fetchAnalyticsFromAPI(
    gmbLocationId,
    gmbAccountId,
    startDate,
    endDate,
    accessToken
  ) {
    try {
      const requestData = {
        startDate,
        endDate,
      };

      const headers = {
        ...this.getAuthHeaders(),
        "x-gmb-account-id": gmbAccountId,
        "x-gmb-location-id": gmbLocationId,
      };

      // Log the request details for debugging
      logger.logAnalyticsSync("API_REQUEST_DETAILS", {
        url: "/v1/performance/performance-locationMetrics",
        method: "POST",
        headers: this.sanitizeHeaders(headers),
        requestData,
        baseURL: this.baseURL,
        timeout: this.timeout,
      });

      const response = await this.apiClient.post(
        "/v1/performance/performance-locationMetrics",
        requestData,
        {
          headers,
          timeout: 60000, // 60 second timeout for this specific request
          maxRedirects: 5,
          validateStatus: function (status) {
            return status >= 200 && status < 500; // Don't throw for 4xx errors
          },
        }
      );

      if (response.data && response.data.data) {
        return response.data.data;
      } else {
        throw new Error("Invalid API response format");
      }
    } catch (error) {
      // Enhanced error logging for debugging
      logger.error("Analytics API request failed:", {
        errorCode: error.code,
        errorMessage: error.message,
        hasResponse: !!error.response,
        hasRequest: !!error.request,
        url: "/v1/performance/performance-locationMetrics",
        gmbLocationId,
        gmbAccountId,
        stack: error.stack,
      });

      if (error.response) {
        // Server responded with error status
        throw new Error(
          `API Error: ${error.response.status} - ${
            error.response.data?.message || "Unknown error"
          }`
        );
      } else if (error.request) {
        // Request was made but no response received
        if (error.code === "ECONNRESET") {
          throw new Error(
            `Connection reset by server. This might be due to: 1) Server overload, 2) Invalid headers, 3) Request timeout, 4) SSL/TLS issues. Original error: ${error.message}`
          );
        } else if (error.code === "ECONNREFUSED") {
          throw new Error(
            `Connection refused. Backend API may not be running on ${this.baseURL}`
          );
        } else if (
          error.code === "ETIMEDOUT" ||
          error.code === "ECONNABORTED"
        ) {
          throw new Error(
            `Request timeout after ${this.timeout}ms. Try increasing timeout or check server performance.`
          );
        } else {
          throw new Error(`Network error (${error.code}): ${error.message}`);
        }
      } else {
        // Request setup error
        throw new Error(`Request setup error: ${error.message}`);
      }
    }
  }

  /**
   * Transform Google API data to database format
   */
  transformAnalyticsData(apiData, gmbLocationId, gmbAccountId) {
    const metricsData = [];

    try {
      if (
        !apiData.multiDailyMetricTimeSeries ||
        apiData.multiDailyMetricTimeSeries.length === 0
      ) {
        logger.logAnalyticsSync("NO_METRICS_DATA", {
          locationId: gmbLocationId,
          message: "No metrics data found in API response",
        });
        return metricsData;
      }

      // Process each daily metric time series
      apiData.multiDailyMetricTimeSeries.forEach((multiMetric) => {
        if (!multiMetric.dailyMetricTimeSeries) return;

        multiMetric.dailyMetricTimeSeries.forEach((metricSeries) => {
          if (
            !metricSeries.dailyMetric ||
            !metricSeries.timeSeries ||
            !metricSeries.timeSeries.datedValues
          ) {
            return;
          }

          const metricType = metricSeries.dailyMetric;

          // Only process supported metrics
          if (!this.SUPPORTED_METRICS.includes(metricType)) {
            logger.logAnalyticsSync("UNSUPPORTED_METRIC", {
              locationId: gmbLocationId,
              metricType,
              message: `Skipping unsupported metric: ${metricType}`,
            });
            return;
          }

          // Process each dated value
          metricSeries.timeSeries.datedValues.forEach((datedValue) => {
            if (!datedValue.date || datedValue.value === undefined) return;

            const { year, month, day } = datedValue.date;
            const metricDate = `${year}-${String(month).padStart(
              2,
              "0"
            )}-${String(day).padStart(2, "0")}`;
            const metricValue = parseInt(datedValue.value || "0", 10);

            metricsData.push({
              gmbLocationId,
              gmbAccountId,
              metricDate,
              metricType,
              metricValue,
            });
          });
        });
      });

      logger.logAnalyticsSync("DATA_TRANSFORMED", {
        locationId: gmbLocationId,
        metricsCount: metricsData.length,
        message: `Transformed ${metricsData.length} metric data points`,
      });

      return metricsData;
    } catch (error) {
      logger.error("Error transforming analytics data:", error);
      throw error;
    }
  }

  /**
   * Batch insert metrics into database
   */
  async batchInsertMetrics(metricsArray) {
    if (!metricsArray || metricsArray.length === 0) {
      return 0;
    }

    try {
      const values = metricsArray.map((metric) => [
        metric.gmbLocationId,
        metric.gmbAccountId,
        metric.metricDate,
        metric.metricType,
        metric.metricValue,
      ]);

      const placeholders = values.map(() => "(?, ?, ?, ?, ?)").join(", ");
      const flatValues = values.flat();

      const query = `
        INSERT INTO location_analytics_daily
        (gmb_location_id, gmb_account_id, metric_date, metric_type, metric_value)
        VALUES ${placeholders}
        ON DUPLICATE KEY UPDATE
        metric_value = VALUES(metric_value),
        updated_at = CURRENT_TIMESTAMP
      `;

      const [result] = await database.pool.execute(query, flatValues);
      return result.affectedRows;
    } catch (error) {
      logger.error("Error batch inserting metrics:", error);
      throw error;
    }
  }

  /**
   * Get locations that need sync
   */
  async getLocationsThatNeedSync() {
    try {
      const syncDate = new Date().toISOString().split("T")[0];

      const query = `
        SELECT DISTINCT
          gl.gmbLocationId,
          gl.gmbAccountId,
          gl.gmbLocationName,
          lac.last_sync_date,
          lac.sync_frequency_days,
          got.accessToken,
          got.refreshToken
        FROM gmb_locations gl
        LEFT JOIN location_analytics_config lac ON gl.gmbLocationId = lac.gmb_location_id
        LEFT JOIN gmb_oauth_tokens got ON gl.gmbAccountId = got.gmbAccountId
        WHERE gl.statusId = 1
        AND got.statusId = 1
        AND got.accessToken IS NOT NULL
        AND (lac.sync_enabled IS NULL OR lac.sync_enabled = 1)
        AND (
          lac.last_sync_date IS NULL
          OR lac.last_sync_date < DATE_SUB(?, INTERVAL COALESCE(lac.sync_frequency_days, 1) DAY)
        )
        ORDER BY lac.last_sync_date IS NULL DESC, lac.last_sync_date ASC
      `;

      const [rows] = await database.pool.execute(query, [syncDate]);
      return rows;
    } catch (error) {
      logger.error("Error getting locations that need sync:", error);
      throw error;
    }
  }

  /**
   * Log sync operation
   */
  async logSyncOperation(syncData) {
    try {
      const query = `
        INSERT INTO location_analytics_sync_log
        (gmb_location_id, gmb_account_id, sync_date, start_date, end_date,
         status, metrics_synced, error_message, api_calls_made, sync_duration_seconds)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const [result] = await database.pool.execute(query, [
        syncData.gmbLocationId,
        syncData.gmbAccountId,
        syncData.syncDate,
        syncData.startDate,
        syncData.endDate,
        syncData.status,
        syncData.metricsSynced || 0,
        syncData.errorMessage || null,
        syncData.apiCallsMade || 0,
        syncData.syncDurationSeconds || 0,
      ]);

      return result.insertId;
    } catch (error) {
      logger.error("Error logging sync operation:", error);
      throw error;
    }
  }

  /**
   * Update sync log status
   */
  async updateSyncLog(syncLogId, updateData) {
    try {
      const setParts = [];
      const values = [];

      if (updateData.status !== undefined) {
        setParts.push("status = ?");
        values.push(updateData.status);
      }
      if (updateData.metricsSynced !== undefined) {
        setParts.push("metrics_synced = ?");
        values.push(updateData.metricsSynced);
      }
      if (updateData.errorMessage !== undefined) {
        setParts.push("error_message = ?");
        values.push(updateData.errorMessage);
      }
      if (updateData.syncDurationSeconds !== undefined) {
        setParts.push("sync_duration_seconds = ?");
        values.push(updateData.syncDurationSeconds);
      }
      if (updateData.apiCallsMade !== undefined) {
        setParts.push("api_calls_made = ?");
        values.push(updateData.apiCallsMade);
      }

      if (setParts.length === 0) {
        return { affectedRows: 0 };
      }

      setParts.push("updated_at = CURRENT_TIMESTAMP");
      values.push(syncLogId);

      const query = `UPDATE location_analytics_sync_log SET ${setParts.join(
        ", "
      )} WHERE id = ?`;
      const [result] = await database.pool.execute(query, values);

      return result;
    } catch (error) {
      logger.error("Error updating sync log:", error);
      throw error;
    }
  }

  /**
   * Update last sync date for a location
   */
  async updateLastSyncDate(gmbLocationId, syncDate) {
    try {
      const query = `
        INSERT INTO location_analytics_config
        (gmb_location_id, gmb_account_id, last_sync_date, sync_enabled)
        SELECT ?, gmbAccountId, ?, 1 FROM gmb_locations WHERE gmbLocationId = ?
        ON DUPLICATE KEY UPDATE
        last_sync_date = VALUES(last_sync_date),
        updated_at = CURRENT_TIMESTAMP
      `;

      const [result] = await database.pool.execute(query, [
        gmbLocationId,
        syncDate,
        gmbLocationId,
      ]);
      return result;
    } catch (error) {
      logger.error("Error updating last sync date:", error);
      throw error;
    }
  }

  /**
   * Clean up old analytics data
   */
  async cleanupOldData() {
    try {
      logger.logAnalyticsSync("CLEANUP_START", {
        retentionDays: this.retentionDays,
        message: `Cleaning up analytics data older than ${this.retentionDays} days`,
      });

      const query = `
        DELETE FROM location_analytics_daily
        WHERE metric_date < DATE_SUB(CURDATE(), INTERVAL ? DAY)
      `;

      const [result] = await database.pool.execute(query, [this.retentionDays]);

      if (result.affectedRows > 0) {
        logger.logAnalyticsSync("CLEANUP_COMPLETE", {
          deletedRows: result.affectedRows,
          message: `Cleaned up ${result.affectedRows} old analytics records`,
        });
      }

      return result;
    } catch (error) {
      logger.error("Error cleaning up old data:", error);
      throw error;
    }
  }

  /**
   * Utility function to add delay
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.stats = {
      totalSynced: 0,
      totalFailed: 0,
      lastSyncTime: null,
      lastSyncDuration: 0,
      locationsProcessed: 0,
      metricsInserted: 0,
      errors: [],
    };
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      ...this.stats,
      configuration: {
        daysToSync: this.daysToSync,
        newLocationSyncDays: this.newLocationSyncDays,
        batchSize: this.batchSize,
        retentionDays: this.retentionDays,
        supportedMetrics: this.SUPPORTED_METRICS.length,
      },
      isConfigured: !!(this.baseURL && this.serviceUserId),
      hasAuthToken: !!this.serviceAuthToken,
    };
  }

  /**
   * Test API connectivity
   */
  async testConnection() {
    try {
      // Try a simple GET request to the root endpoint first
      const response = await this.apiClient.get("/", {
        timeout: 5000, // 5 second timeout for connectivity test
      });

      // Any response (200, 404, etc.) means the server is reachable
      if (response.status >= 200 && response.status < 500) {
        logger.logAnalyticsSync("API_CONNECTION_SUCCESS", {
          status: response.status,
          baseURL: this.baseURL,
          message: "Backend API is reachable",
        });
        return true;
      }

      return false;
    } catch (error) {
      // Log detailed error information for debugging
      const errorInfo = {
        baseURL: this.baseURL,
        errorCode: error.code,
        errorMessage: error.message,
        timeout:
          error.code === "ECONNABORTED" ? "Connection timeout" : "Unknown",
        suggestion:
          error.code === "ECONNREFUSED"
            ? "Backend API may not be running"
            : "Check network connectivity",
      };

      logger.error("Analytics API connection test failed:", errorInfo);
      return false;
    }
  }

  /**
   * Test the specific analytics endpoint with sample data
   */
  async testAnalyticsEndpoint() {
    try {
      const testHeaders = {
        ...this.getAuthHeaders(),
        "x-gmb-account-id": "test-account",
        "x-gmb-location-id": "test-location",
      };

      const testData = {
        startDate: "2024-01-01",
        endDate: "2024-01-02",
      };

      logger.logAnalyticsSync("TESTING_ANALYTICS_ENDPOINT", {
        url: "/v1/performance/performance-locationMetrics",
        headers: this.sanitizeHeaders(testHeaders),
        data: testData,
      });

      const response = await this.apiClient.post(
        "/v1/performance/performance-locationMetrics",
        testData,
        {
          headers: testHeaders,
          timeout: 10000,
          validateStatus: function (status) {
            return status >= 200 && status < 600; // Accept any response
          },
        }
      );

      logger.logAnalyticsSync("ANALYTICS_ENDPOINT_RESPONSE", {
        status: response.status,
        statusText: response.statusText,
        hasData: !!response.data,
      });

      return {
        success: response.status >= 200 && response.status < 500,
        status: response.status,
        statusText: response.statusText,
      };
    } catch (error) {
      logger.error("Analytics endpoint test failed:", {
        errorCode: error.code,
        errorMessage: error.message,
        hasResponse: !!error.response,
        responseStatus: error.response?.status,
        responseData: error.response?.data,
      });

      return {
        success: false,
        error: error.message,
        code: error.code,
      };
    }
  }
}

module.exports = new AnalyticsSyncService();
