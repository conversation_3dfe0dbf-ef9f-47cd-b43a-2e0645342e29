import HttpHelperService from "../httpHelper.service";
import {
  UPDATE_BUSINESS_NAME,
  UPDATE_PHONE_NUMBERS,
  UPDATE_WEBSITE_URL,
  UPDATE_BUSINESS_HOURS,
  UPDATE_CATEGORIES,
  UPDATE_SERVICE_ITEMS,
  UPDATE_SERVICE_AREA,
  GENERATE_BUSINESS_DESCRIPTION,
  GENERATE_SERVICES,
} from "../../constants/endPoints.constant";

interface UpdateBusinessNameRequest {
  locationId: string;
  businessName: string;
}

interface PhoneNumber {
  number: string;
  type: string;
}

interface UpdatePhoneNumbersRequest {
  locationId: string;
  phoneNumbers: PhoneNumber[];
}

interface UpdateWebsiteUrlRequest {
  locationId: string;
  websiteUri: string;
}

interface UpdateBusinessHoursRequest {
  locationId: string;
  regularHours: any;
}

interface UpdateCategoriesRequest {
  locationId: string;
  categories: any;
}

interface UpdateServiceItemsRequest {
  locationId: string;
  serviceItems: any[];
}

interface UpdateServiceAreaRequest {
  locationId: string;
  serviceArea: any;
}

interface GenerateBusinessDescriptionRequest {
  businessName: string;
  categories?: any[];
  serviceItems?: any[];
}

interface GenerateBusinessDescriptionResponse {
  success: boolean;
  message: string;
  data: {
    description: string;
    businessName: string;
  };
}

interface GenerateServicesRequest {
  categoryName: string;
}

interface GenerateServicesResponse {
  success: boolean;
  message: string;
  data: {
    services: string[];
    categoryName: string;
  };
}

class BusinessProfileService {
  private _httpHelperService: HttpHelperService;

  constructor(dispatch: any) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  /**
   * Update business name for a Google Business Profile location
   * @param request - Business name update request
   * @returns Promise with API response
   */
  updateBusinessName = async (request: UpdateBusinessNameRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_BUSINESS_NAME,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating business name:", error);
      throw error;
    }
  };

  /**
   * Update phone numbers for a Google Business Profile location
   * @param request - Phone numbers update request
   * @returns Promise with API response
   */
  updatePhoneNumbers = async (request: UpdatePhoneNumbersRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_PHONE_NUMBERS,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating phone numbers:", error);
      throw error;
    }
  };

  /**
   * Update website URL for a Google Business Profile location
   * @param request - Website URL update request
   * @returns Promise with API response
   */
  updateWebsiteUrl = async (request: UpdateWebsiteUrlRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_WEBSITE_URL,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating website URL:", error);
      throw error;
    }
  };

  /**
   * Update business hours for a Google Business Profile location
   * @param request - Business hours update request
   * @returns Promise with API response
   */
  updateBusinessHours = async (request: UpdateBusinessHoursRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_BUSINESS_HOURS,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating business hours:", error);
      throw error;
    }
  };

  /**
   * Update categories for a Google Business Profile location
   * @param request - Categories update request
   * @returns Promise with API response
   */
  updateCategories = async (request: UpdateCategoriesRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_CATEGORIES,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating categories:", error);
      throw error;
    }
  };

  /**
   * Update service items for a Google Business Profile location
   * @param request - Service items update request
   * @returns Promise with API response
   */
  updateServiceItems = async (request: UpdateServiceItemsRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_SERVICE_ITEMS,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating service items:", error);
      throw error;
    }
  };

  /**
   * Update service area for a Google Business Profile location
   * @param request - Service area update request
   * @returns Promise with API response
   */
  updateServiceArea = async (request: UpdateServiceAreaRequest) => {
    try {
      const response = await this._httpHelperService.post(
        UPDATE_SERVICE_AREA,
        request
      );
      return response;
    } catch (error) {
      console.error("Error updating service area:", error);
      throw error;
    }
  };

  /**
   * Add a phone number to the existing list
   * @param locationId - Google Business Profile location ID
   * @param phoneNumber - Phone number to add
   * @param phoneType - Type of phone number
   * @param existingPhoneNumbers - Current phone numbers
   * @returns Promise with API response
   */
  addPhoneNumber = async (
    locationId: string,
    phoneNumber: string,
    phoneType: string,
    existingPhoneNumbers: PhoneNumber[]
  ) => {
    const newPhoneNumber: PhoneNumber = {
      number: phoneNumber,
      type: phoneType,
    };

    const updatedPhoneNumbers = [...existingPhoneNumbers, newPhoneNumber];

    return this.updatePhoneNumbers({
      locationId,
      phoneNumbers: updatedPhoneNumbers,
    });
  };

  /**
   * Remove a phone number from the existing list
   * @param locationId - Google Business Profile location ID
   * @param phoneNumberToRemove - Phone number to remove
   * @param existingPhoneNumbers - Current phone numbers
   * @returns Promise with API response
   */
  removePhoneNumber = async (
    locationId: string,
    phoneNumberToRemove: string,
    existingPhoneNumbers: PhoneNumber[]
  ) => {
    const updatedPhoneNumbers = existingPhoneNumbers.filter(
      (phone) => phone.number !== phoneNumberToRemove
    );

    return this.updatePhoneNumbers({
      locationId,
      phoneNumbers: updatedPhoneNumbers,
    });
  };

  /**
   * Update an existing phone number
   * @param locationId - Google Business Profile location ID
   * @param oldPhoneNumber - Phone number to update
   * @param newPhoneNumber - New phone number
   * @param phoneType - Type of phone number
   * @param existingPhoneNumbers - Current phone numbers
   * @returns Promise with API response
   */
  updatePhoneNumber = async (
    locationId: string,
    oldPhoneNumber: string,
    newPhoneNumber: string,
    phoneType: string,
    existingPhoneNumbers: PhoneNumber[]
  ) => {
    const updatedPhoneNumbers = existingPhoneNumbers.map((phone) =>
      phone.number === oldPhoneNumber
        ? { number: newPhoneNumber, type: phoneType }
        : phone
    );

    return this.updatePhoneNumbers({
      locationId,
      phoneNumbers: updatedPhoneNumbers,
    });
  };

  /**
   * Generate AI-powered business description
   * @param request - Business description generation request
   * @returns Promise with API response
   */
  generateBusinessDescription = async (
    request: GenerateBusinessDescriptionRequest
  ): Promise<GenerateBusinessDescriptionResponse> => {
    try {
      const response = await this._httpHelperService.post(
        GENERATE_BUSINESS_DESCRIPTION,
        request
      );
      return response;
    } catch (error) {
      console.error("Error generating business description:", error);
      throw error;
    }
  };

  /**
   * Generate AI-powered services list based on category
   * @param request - Services generation request
   * @returns Promise with API response
   */
  generateServices = async (
    request: GenerateServicesRequest
  ): Promise<GenerateServicesResponse> => {
    try {
      const response = await this._httpHelperService.post(
        GENERATE_SERVICES,
        request
      );
      return response;
    } catch (error) {
      console.error("Error generating services:", error);
      throw error;
    }
  };
}

export default BusinessProfileService;
