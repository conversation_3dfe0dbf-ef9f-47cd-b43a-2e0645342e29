import React, { useContext, useEffect, useState } from "react";
import {
  <PERSON>,
  CardContent,
  Typography,
  ImageList,
  ImageListItem,
  Divider,
  Box,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import LocationService from "../../services/location/location.service";
import { LoadingContext } from "../../context/loading.context";

const MediaGallery = (props: { mediaItems: any }) => {
  const dispatch = useDispatch();
  const _locationService = new LocationService(dispatch);
  const [grouped, setGrouped] = useState<Record<string, any[]> | null>(null);
  const { setLoading } = useContext(LoadingContext);
  useEffect(() => {
    getData();

    async function getData() {
      setLoading(true);
      const groupedItems: Record<string, any[]> = {};

      const promises = props.mediaItems.map(async (item: any) => {
        const category = item.locationAssociation?.category || "UNCATEGORIZED";
        const base64Image = await _locationService.getGoogleImageBase64(
          item.thumbnailUrl
        );
        if (!groupedItems[category]) groupedItems[category] = [];
        groupedItems[category].push({
          ...item,
          base64Image: base64Image.base64,
        });
      });

      await Promise.all(promises); // Wait for all async tasks to finish
      setGrouped(groupedItems);
      setLoading(false);
    }
  }, []);

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Media Gallery
        </Typography>
        <Divider sx={{ mb: 2 }} />

        {grouped &&
          Object.entries(grouped).map(([category, items]) => (
            <Box key={category} sx={{ mb: 4 }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  textTransform: "capitalize",
                  fontWeight: 600,
                  fontSize: 16,
                }}
              >
                {category.replace(/_/g, " ").toLowerCase()}
              </Typography>
              <ImageList cols={5} gap={12}>
                {(items as any).map((item: any, index: number) => (
                  <ImageListItem key={item.name}>
                    <img
                      src={item.base64Image}
                      alt={`${category}-${index}-${item.base64Image}`}
                      loading="eager"
                      style={{
                        borderRadius: 8,
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                  </ImageListItem>
                ))}
              </ImageList>
            </Box>
          ))}
      </CardContent>
    </Card>
  );
};

export default MediaGallery;
