"use strict";

// Load environment configuration based on NODE_ENV before loading app
const environment = (process.env.NODE_ENV || "development").trim();
const envFile = `.env.${environment}`;

console.log(`Loading environment configuration from: ${envFile}`);
require("dotenv").config({ path: envFile });

// Debug environment loading
console.log(`Environment loaded: ${process.env.APP_ENV_NAME}`);
console.log(`Log level: ${process.env.APP_LOG_LEVEL}`);
console.log(`Database: ${process.env.APP_DB_NAME}`);

const app = require("./app");
const port = process.env.APP_PORT || 3000;
const server = app.listen(port, () =>
  console.log(`Server is listening on port ${port}.`)
);
server.timeout = 20000;
