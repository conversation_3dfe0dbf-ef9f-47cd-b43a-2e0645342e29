import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Button,
} from "@mui/material";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useContext } from "react";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import InstagramService from "../../../services/instagram/instagram.service";
import { LoadingContext } from "../../../context/loading.context";

const InstagramCallback: React.FC<any> = ({ title }) => {
  const [authStatus, setAuthStatus] = useState<boolean | null>(null);
  const [countdown, setCountdown] = useState(5);
  const navigate = useNavigate();
  const location = useLocation();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const { setToastConfig } = useContext(ToastContext);
  const { setLoading } = useContext(LoadingContext);
  const dispatch = useDispatch();

  useEffect(() => {
    document.title = title || "Instagram Authentication";

    // Parse URL parameters
    const params = new URLSearchParams(location.search);
    const success = params.get("success");
    const error = params.get("error");
    const businessId = params.get("businessId");
    const userId = params.get("userId");

    if (success === "true" && businessId) {
      setAuthStatus(true);
      setToastConfig(
        ToastSeverity.Success,
        "Instagram account connected successfully!",
        true
      );

      // Update the business record in the database to reflect the Instagram connection
      updateBusinessInstagramStatus(businessId);

      startCountdown();
    } else if (error) {
      setAuthStatus(false);
      setToastConfig(
        ToastSeverity.Error,
        `Instagram connection failed: ${error}`,
        true
      );
    } else {
      // Handle the code from Facebook OAuth
      const code = params.get("code");
      const state = params.get("state");

      if (code && state) {
        try {
          const stateObj = JSON.parse(decodeURIComponent(state));
          handleInstagramCode(code, stateObj);
        } catch (error) {
          console.error("Invalid state parameter", error);
          setAuthStatus(false);
          setToastConfig(ToastSeverity.Error, "Invalid state parameter", true);
        }
      }
    }
  }, [location]);

  const handleInstagramCode = async (code: string, stateObj: any) => {
    try {
      setLoading(true);
      const _instagramService = new InstagramService(dispatch);

      // Use the new Instagram Business API callback validation
      const response = await _instagramService.validateCallback(
        code,
        JSON.stringify(stateObj)
      );

      if (response?.success) {
        setAuthStatus(true);
        setToastConfig(
          ToastSeverity.Success,
          `Instagram connected successfully! ${
            response.data?.accountsCount || 0
          } accounts found.`,
          true
        );

        // Update business Instagram status
        await updateBusinessInstagramStatus(stateObj.businessId);

        // Redirect to the return URL or default to business management
        const returnUrl =
          stateObj.returnUrl || "/business-management/manage-business";
        startCountdown(returnUrl);
      } else {
        setAuthStatus(false);
        setToastConfig(
          ToastSeverity.Error,
          response?.message || "Failed to connect Instagram account",
          true
        );
      }
    } catch (error: any) {
      console.error("Error handling Instagram code", error);
      setAuthStatus(false);
      setToastConfig(
        ToastSeverity.Error,
        error?.response?.data?.message ||
          error.message ||
          "Error connecting Instagram account",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const updateBusinessInstagramStatus = async (businessId: string) => {
    try {
      setLoading(true);
      // Call your API to update the business record
      const _instagramService = new InstagramService(dispatch);
      const response = await _instagramService.updateInstagramStatus(
        parseInt(businessId),
        true
      );

      if (response && response.success) {
        console.log(
          `Successfully updated Instagram status for business ID: ${businessId}`
        );
      } else {
        console.error("Failed to update Instagram status:", response);
        setToastConfig(
          ToastSeverity.Warning,
          "Instagram connected but failed to update status in database",
          true
        );
      }
    } catch (error) {
      console.error("Error updating Instagram status:", error);
      setToastConfig(
        ToastSeverity.Warning,
        "Instagram connected but failed to update status in database",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const startCountdown = (
    redirectUrl = "/business-management/manage-business"
  ) => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          navigate(redirectUrl);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        background:
          "linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%)",
      }}
    >
      <Paper
        elevation={3}
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          padding: 4,
          borderRadius: 2,
          maxWidth: 500,
          width: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            marginBottom: 2,
          }}
        >
          <img
            src="/src/assets/common/instagram.png"
            alt="Instagram"
            style={{ width: 60, height: 60, marginRight: 16 }}
          />
          <Typography variant="h5" component="h1" sx={{ fontWeight: "bold" }}>
            Instagram Connection
          </Typography>
        </Box>

        {authStatus === null && (
          <Box sx={{ textAlign: "center", my: 4 }}>
            <CircularProgress sx={{ color: "#e1306c" }} />
            <Typography variant="body1" sx={{ mt: 2 }}>
              Processing your authentication...
            </Typography>
          </Box>
        )}

        {authStatus === true && (
          <Box sx={{ textAlign: "center", my: 4 }}>
            <CheckCircleOutlineIcon sx={{ fontSize: 60, color: "#4CAF50" }} />
            <Typography variant="h6" sx={{ mt: 2, fontWeight: "bold" }}>
              Instagram Account Connected Successfully!
            </Typography>
            <Typography variant="body1" sx={{ mt: 1 }}>
              Your Instagram business account has been connected to MyLocoBiz.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, color: "#666" }}>
              Redirecting in {countdown} seconds...
            </Typography>
            <Button
              variant="contained"
              sx={{
                mt: 3,
                bgcolor: "#e1306c",
                "&:hover": { bgcolor: "#c13584" },
              }}
              onClick={() => navigate("/business-management/manage-business")}
            >
              Return to Business Management
            </Button>
          </Box>
        )}

        {authStatus === false && (
          <Box sx={{ textAlign: "center", my: 4 }}>
            <ErrorOutlineIcon sx={{ fontSize: 60, color: "#F44336" }} />
            <Typography variant="h6" sx={{ mt: 2, fontWeight: "bold" }}>
              Instagram Connection Failed
            </Typography>
            <Typography variant="body1" sx={{ mt: 1 }}>
              There was an error connecting your Instagram account.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, color: "#666" }}>
              Please make sure you have an Instagram Business or Creator account
              linked to a Facebook Page.
            </Typography>
            <Button
              variant="contained"
              sx={{
                mt: 3,
                bgcolor: "#e1306c",
                "&:hover": { bgcolor: "#c13584" },
              }}
              onClick={() => navigate("/business-management/manage-business")}
            >
              Return to Business Management
            </Button>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default InstagramCallback;
