import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>,
  Typo<PERSON>,
  CardContent,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableHead,
} from "@mui/material";

import { IMissingInformationContent } from "../../screens/businessManagement/businessSummary/businessSummary.screen";

import "./regularHoursTable.component.style.css";
interface BusinessHoursProps {
  regularHours: any;
  missingInformation: IMissingInformationContent[];
  onSave?: (values: any) => void;
  initialValues?: any;
}

const RegularHoursTable: React.FC<BusinessHoursProps> = ({ regularHours }) => {
  const toTitleCase = (str: string): string => {
    if (!str) return "";
    return str
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const getCustomOpenCloseHours = (period: any) => {
    let customTime = "";
    if (Object.keys(period.openTime).length > 0) {
      customTime = `${period.openTime.hours}:${
        period.openTime.minutes || "00"
      } - `;
    } else {
      customTime = `00:00 - `;
    }

    if (Object.keys(period.closeTime).length > 0) {
      customTime += `${period.closeTime.hours}:${
        period.closeTime.minutes || "00"
      }`;
    } else {
      customTime += `00:00`;
    }

    return customTime;
  };

  // Group periods by day
  const groupPeriodsByDay = (periods: any[]) => {
    const dayOrder = [
      "MONDAY",
      "TUESDAY",
      "WEDNESDAY",
      "THURSDAY",
      "FRIDAY",
      "SATURDAY",
      "SUNDAY",
    ];
    const grouped: { [key: string]: any[] } = {};

    // Initialize all days
    dayOrder.forEach((day) => {
      grouped[day] = [];
    });

    // Group periods by day
    periods.forEach((period: any) => {
      const day = period.openDay || period.day;
      if (day && grouped[day]) {
        grouped[day].push(period);
      }
    });

    return dayOrder.map((day) => ({
      day,
      periods: grouped[day],
    }));
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-start", // or "flex-start", "center", as needed
          alignItems: "center",
          gap: 2,
          flexWrap: "wrap",
        }}
      >
        {/* Table Section */}
        <Card className="boxShadowNone" sx={{ flex: 1, minWidth: 300 }}>
          <CardContent sx={{ boxShadow: "none", padding: "0px" }}>
            <Card variant="outlined" sx={{ boxShadow: "none", padding: "0px" }}>
              <Box sx={{ overflowX: "auto" }}>
                <Table sx={{ minWidth: 400 }}>
                  <TableHead>
                    <TableRow>
                      <TableCell>
                        <strong>Day</strong>
                      </TableCell>
                      <TableCell>
                        <strong>Status</strong>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {regularHours?.periods &&
                    regularHours.periods.length > 0 ? (
                      groupPeriodsByDay(regularHours.periods).map(
                        (dayGroup: any, dayIndex: number) => (
                          <TableRow key={dayIndex}>
                            <TableCell
                              sx={{ verticalAlign: "top", fontWeight: 500 }}
                            >
                              {toTitleCase(dayGroup.day)}
                            </TableCell>
                            <TableCell sx={{ verticalAlign: "top" }}>
                              {dayGroup.periods.length === 0 ? (
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Closed
                                </Typography>
                              ) : (
                                <Box>
                                  {dayGroup.periods.map(
                                    (period: any, periodIndex: number) => (
                                      <Typography
                                        key={periodIndex}
                                        variant="body2"
                                        sx={{
                                          mb:
                                            periodIndex <
                                            dayGroup.periods.length - 1
                                              ? 0.5
                                              : 0,
                                          lineHeight: 1.4,
                                        }}
                                      >
                                        {period.closeTime?.hours === 24
                                          ? "Open 24 Hours"
                                          : getCustomOpenCloseHours(period)}
                                      </Typography>
                                    )
                                  )}
                                </Box>
                              )}
                            </TableCell>
                          </TableRow>
                        )
                      )
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={2}
                          sx={{ textAlign: "center", py: 3 }}
                        >
                          <Typography variant="body2" color="text.secondary">
                            No business hours available
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </Box>
            </Card>
          </CardContent>
        </Card>
      </Box>
    </>
  );
};

export default RegularHoursTable;
