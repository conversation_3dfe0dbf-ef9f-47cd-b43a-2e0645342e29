import React from "react";
import {
  Dialog,
  DialogContent,
  Button,
  Typography,
  Box,
  IconButton,
  AppBar,
  Tool<PERSON>,
  List,
  ListItem,
  ListItemText,
  Divider,
  useMediaQuery,
  useTheme,
  ListItemButton
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { Formik, Form } from "formik";
import * as yup from "yup";
import BusinessCategoryScreen from "../businessCategory.screen";

interface Service {
  id: string;
  name: string;
  description: string;
}

interface Category {
  name: string;
  isPrimary: boolean;
  services: Service[];
}

interface AddBusinessCategoryModalProps {
  open: boolean;
  onClose: () => void;
  onAddCategory: (categoryName: string) => void;
}

const AddBusinessCategoryModal: React.FC<AddBusinessCategoryModalProps> = ({
  open,
  onClose,
  onAddCategory,
}) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  
  

  

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      fullScreen={fullScreen}
      PaperProps={{
        style: {
          backgroundColor: "white",
          borderRadius: "8px",
          maxHeight: "90vh"
        }
      }}
    >
       <BusinessCategoryScreen />    
    </Dialog>
  );
};

export default AddBusinessCategoryModal;