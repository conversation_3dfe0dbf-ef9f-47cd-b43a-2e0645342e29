export class MessageConstants {
  static UserDeletedSuccessfully: string = "User Deleted Successfully";
  static UserDeletionFailed: string = "Unable to delete user at this moment.";
  static BusinessDeletedSuccessfully: string = "Business Deleted Successfully";
  static BusinessDeletionFailed: string =
    "Unable to delete user at this moment.";
  static UserCreatedSuccessfully: string = "User Created Successfully";
  static UserUpdatedSuccessfully: string = "User Updated Successfully";
  static LocationsAddedSuccessfully: string = "Locations Added Successfully";
  static LocationsUpdatedSuccessfully: string =
    "Locations Updated Successfully";
  static UpdatedSuccessfully: string = "Updated Successfully";
  static ApiErrorStandardMessage: string =
    "We are facing technical isses. Please try again later.";
  static UserAssignLocationsFailed: string =
    "Unable to assign locations at this moment.";
  static BusinessCreatedSuccessfully: string = "Business Created Successfully";
  static BusinessUpdatedSuccessfully: string = "Business Updated Successfully";
  static BusinessUpdateFailed: string = "Business Update Failed";
  static UserUpdateFailed: string = "User Update Failed";
  static SyncLocationsConditionFail: string =
    "We have found 0 accounts in your business. Sync your Email.";
  static PostDeletedSuccessfully: string = "Post Deleted Successfully.";
  static UnableToDeletePostAtThisMoment: string =
    "Unable To Delete Post At This Moment.";
  static NoListingsMissing: string = "No Listings Missing";
  static ListingsMissing: string = "Missing";
  static NoReviewsFound: string = "Looks like there are no reviews yet.";
  static NoPostsFound: string = "Looks like there are no posts yet.";
  static PasswordResetSuccessfully: string = "Password Reset Successfully";
  static PasswordAndConfirmPasswordDoesntMatch: string =
    "Password & Confirm Password Doesn't Match";
}
