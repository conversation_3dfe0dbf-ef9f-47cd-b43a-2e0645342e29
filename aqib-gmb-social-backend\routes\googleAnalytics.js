const express = require("express");
const router = express.Router();
const { isAuthenticated } = require("../middleware/isAuthenticated");

const {
  getGoogleAnalyticsData,
  getGoogleAnalyticsCharts,
  getGoogleAnalyticsSummary,
  exportGoogleAnalyticsReport,
} = require("../controllers/googleAnalytics.controller");

// Test endpoint (no auth required)
router.get("/test", (req, res) => {
  res.json({
    success: true,
    message: "Google Analytics API is working",
    timestamp: new Date().toISOString(),
  });
});

// Get Google Analytics data with filters
router.post("/data", isAuthenticated, getGoogleAnalyticsData);

// Get Google Analytics charts data
router.post("/charts", isAuthenticated, getGoogleAnalyticsCharts);

// Get Google Analytics summary statistics
router.post("/summary", isAuthenticated, getGoogleAnalyticsSummary);

// Export Google Analytics report to Excel
router.post("/export", isAuthenticated, exportGoogleAnalyticsReport);

module.exports = router;
