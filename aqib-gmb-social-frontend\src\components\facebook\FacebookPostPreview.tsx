import React, { useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Avatar,
  IconButton,
  Divider,
  Link,
  CardMedia,
} from "@mui/material";
import {
  ThumbUpOutlined,
  ChatB<PERSON>bleOutline,
  ShareOutlined,
  ArrowBackIos,
  ArrowForwardIos,
  ImageOutlined,
  Language,
} from "@mui/icons-material";
import {
  IFacebookCreatePost,
  IFacebookSelectedPage,
} from "../../interfaces/request/IFacebookCreatePost";
import { IFacebookPageData } from "../../interfaces/response/IFacebookCreatePostResponse";

interface FacebookPostPreviewProps {
  formData: IFacebookCreatePost;
  uploadedImages: any[];
  selectedPage?: IFacebookPageData | null;
  selectedPages?: IFacebookSelectedPage[];
  enableMultiPageSelection?: boolean;
}

const FacebookPostPreview: React.FC<FacebookPostPreviewProps> = ({
  formData,
  uploadedImages,
  selectedPage,
  selectedPages = [],
  enableMultiPageSelection = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % uploadedImages.length);
  };

  const handlePrev = () => {
    setCurrentIndex(
      (prevIndex) =>
        (prevIndex - 1 + uploadedImages.length) % uploadedImages.length
    );
  };

  // Get the page to display in preview
  const getPreviewPage = () => {
    if (enableMultiPageSelection && selectedPages.length > 0) {
      return {
        page_name: selectedPages[0].pageName,
        page_picture_url: selectedPages[0].pagePictureUrl,
        page_category: selectedPages[0].pageCategory,
      };
    }
    if (selectedPage) {
      return selectedPage;
    }
    // Return a default page structure if no page is selected
    return {
      page_name: "Your Facebook Page",
      page_picture_url: null,
      page_category: "Business",
    };
  };

  const previewPage = getPreviewPage();

  // Get the message with dynamic replacement for preview
  const getPreviewMessage = () => {
    if (!formData.message) return "";

    if (enableMultiPageSelection && selectedPages.length > 0) {
      return formData.message.replace(
        /{Page Name}/g,
        selectedPages[0].pageName
      );
    }

    return formData.message;
  };

  const getPreviewDescription = () => {
    if (!formData.description) return "";

    if (enableMultiPageSelection && selectedPages.length > 0) {
      return formData.description.replace(
        /{Page Name}/g,
        selectedPages[0].pageName
      );
    }

    return formData.description;
  };

  const previewMessage = getPreviewMessage();
  const previewDescription = getPreviewDescription();

  return (
    <Box>
      <Card
        elevation={3}
        sx={{
          borderRadius: 2,
          mb: 2,
          maxWidth: "100%",
          mx: "auto",
          backgroundColor: "#ffffff",
          border: "1px solid #e4e6ea",
        }}
      >
        {/* Facebook Post Header */}
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
            <Avatar
              src={previewPage?.page_picture_url || "/default-page-avatar.png"}
              sx={{ width: 40, height: 40, mr: 1.5 }}
            />
            <Box sx={{ flex: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 600, color: "#1c1e21" }}
              >
                {previewPage?.page_name || "Your Facebook Page"}
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                <Typography variant="caption" sx={{ color: "#65676b" }}>
                  Just now
                </Typography>
                <Typography variant="caption" sx={{ color: "#65676b" }}>
                  •
                </Typography>
                <Language sx={{ fontSize: 12, color: "#65676b" }} />
              </Box>
            </Box>
          </Box>

          {/* Post Content */}
          <Box sx={{ mb: 2 }}>
            {previewMessage && (
              <Typography
                variant="body1"
                sx={{
                  color: "#1c1e21",
                  lineHeight: 1.33,
                  mb: previewDescription ? 1 : 0,
                  whiteSpace: "pre-wrap",
                }}
              >
                {previewMessage}
              </Typography>
            )}

            {previewDescription && (
              <Typography
                variant="body1"
                sx={{
                  color: "#1c1e21",
                  lineHeight: 1.33,
                  whiteSpace: "pre-wrap",
                }}
              >
                {previewDescription}
              </Typography>
            )}

            {!previewMessage && !previewDescription && (
              <Typography
                variant="body2"
                sx={{ color: "#65676b", fontStyle: "italic" }}
              >
                Start typing your message to see the preview...
              </Typography>
            )}
          </Box>

          {/* Link Preview */}
          {formData.link && (
            <Box
              sx={{
                border: "1px solid #e4e6ea",
                borderRadius: 1,
                overflow: "hidden",
                mb: 2,
              }}
            >
              <Box sx={{ p: 2, backgroundColor: "#f7f8fa" }}>
                <Typography
                  variant="caption"
                  sx={{ color: "#65676b", textTransform: "uppercase" }}
                >
                  {new URL(formData.link).hostname}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ color: "#1c1e21", fontWeight: 600 }}
                >
                  Link Preview
                </Typography>
                <Typography variant="body2" sx={{ color: "#65676b" }}>
                  {formData.link}
                </Typography>
              </Box>
            </Box>
          )}

          {/* Images */}
          {uploadedImages && uploadedImages.length > 0 && (
            <Box
              sx={{
                position: "relative",
                borderRadius: 1,
                overflow: "hidden",
                mb: 2,
              }}
            >
              <CardMedia
                component="div"
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  position: "relative",
                  backgroundColor: "#f0f2f5",
                }}
              >
                <img
                  src={
                    (uploadedImages[currentIndex] as any).isFromGallery ||
                    (uploadedImages[currentIndex] as any).isFromExistingPost
                      ? (uploadedImages[currentIndex] as any).s3Url
                      : URL.createObjectURL(
                          uploadedImages[currentIndex] as unknown as MediaSource
                        )
                  }
                  alt={`Image ${currentIndex + 1}`}
                  style={{
                    width: "100%",
                    maxHeight: "400px",
                    objectFit: "cover",
                    transition: "opacity 0.5s ease-in-out",
                  }}
                  referrerPolicy="no-referrer"
                />

                {/* Previous Button */}
                {uploadedImages.length > 1 && currentIndex > 0 && (
                  <IconButton
                    onClick={handlePrev}
                    sx={{
                      position: "absolute",
                      left: 10,
                      backgroundColor: "rgba(0,0,0,0.5)",
                      color: "white",
                      "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                    }}
                  >
                    <ArrowBackIos />
                  </IconButton>
                )}

                {/* Next Button */}
                {uploadedImages.length > 1 &&
                  currentIndex < uploadedImages.length - 1 && (
                    <IconButton
                      onClick={handleNext}
                      sx={{
                        position: "absolute",
                        right: 10,
                        backgroundColor: "rgba(0,0,0,0.5)",
                        color: "white",
                        "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                      }}
                    >
                      <ArrowForwardIos />
                    </IconButton>
                  )}

                {/* Image counter */}
                {uploadedImages.length > 1 && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: 10,
                      right: 10,
                      backgroundColor: "rgba(0,0,0,0.7)",
                      color: "white",
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      fontSize: "0.75rem",
                    }}
                  >
                    {currentIndex + 1} / {uploadedImages.length}
                  </Box>
                )}
              </CardMedia>
            </Box>
          )}

          {/* No Image Placeholder */}
          {(!uploadedImages || uploadedImages.length === 0) && (
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: 150,
                backgroundColor: "#f7f8fa",
                borderRadius: 1,
                border: "1px dashed #e4e6ea",
                mb: 2,
              }}
            >
              <ImageOutlined sx={{ fontSize: 40, color: "#bcc0c4", mb: 1 }} />
              <Typography variant="body2" sx={{ color: "#65676b" }}>
                No images added
              </Typography>
            </Box>
          )}
        </Box>

        {/* Facebook Post Actions */}
        <Divider />
        <Box sx={{ p: 1 }}>
          <Box sx={{ display: "flex", justifyContent: "space-around" }}>
            <IconButton
              disabled
              sx={{
                flex: 1,
                borderRadius: 1,
                color: "#65676b",
                "&:hover": { backgroundColor: "#f2f3f5" },
              }}
            >
              <ThumbUpOutlined sx={{ mr: 1, fontSize: 20 }} />
              <Typography variant="body2">Like</Typography>
            </IconButton>
            <IconButton
              disabled
              sx={{
                flex: 1,
                borderRadius: 1,
                color: "#65676b",
                "&:hover": { backgroundColor: "#f2f3f5" },
              }}
            >
              <ChatBubbleOutline sx={{ mr: 1, fontSize: 20 }} />
              <Typography variant="body2">Comment</Typography>
            </IconButton>
            <IconButton
              disabled
              sx={{
                flex: 1,
                borderRadius: 1,
                color: "#65676b",
                "&:hover": { backgroundColor: "#f2f3f5" },
              }}
            >
              <ShareOutlined sx={{ mr: 1, fontSize: 20 }} />
              <Typography variant="body2">Share</Typography>
            </IconButton>
          </Box>
        </Box>
      </Card>

      {/* Multi-page indicator */}
      {enableMultiPageSelection && selectedPages.length > 1 && (
        <Box
          sx={{
            p: 2,
            backgroundColor: "#e3f2fd",
            borderRadius: 1,
            border: "1px solid #bbdefb",
          }}
        >
          <Typography
            variant="body2"
            sx={{ color: "#1976d2", fontWeight: 600 }}
          >
            📄 Multi-Page Preview
          </Typography>
          <Typography variant="caption" sx={{ color: "#1565c0" }}>
            This preview shows how the post will look for "
            {selectedPages[0].pageName}". Similar posts will be created for all{" "}
            {selectedPages.length} selected pages.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default FacebookPostPreview;
