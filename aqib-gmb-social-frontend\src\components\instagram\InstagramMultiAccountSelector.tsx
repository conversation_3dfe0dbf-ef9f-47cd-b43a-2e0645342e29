import React, { useState, useEffect } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  Box,
  Typography,
  Avatar,
  Badge,
  Chip,
  SelectChangeEvent,
} from "@mui/material";
import { CheckCircle as CheckCircleIcon } from "@mui/icons-material";
import { IInstagramSelectedAccount } from "../../interfaces/request/IInstagramCreatePost";
import { IInstagramAccountData } from "../../interfaces/response/IInstagramCreatePostResponse";

interface InstagramMultiAccountSelectorProps {
  accounts: IInstagramAccountData[];
  selectedAccounts: IInstagramSelectedAccount[];
  onAccountsChange: (accounts: IInstagramSelectedAccount[]) => void;
  error?: string;
  loading?: boolean;
  disabled?: boolean;
}

const InstagramMultiAccountSelector: React.FC<
  InstagramMultiAccountSelectorProps
> = ({
  accounts,
  selectedAccounts,
  onAccountsChange,
  error,
  loading = false,
  disabled = false,
}) => {
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);

  // Update selectedAccountIds when selectedAccounts prop changes
  useEffect(() => {
    setSelectedAccountIds(selectedAccounts.map((account) => account.accountId));
  }, [selectedAccounts]);

  const handleAccountChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    setSelectedAccountIds(value);

    // Convert selected account IDs to IInstagramSelectedAccount objects
    const newSelectedAccounts: IInstagramSelectedAccount[] = value.map(
      (accountId) => {
        const account = accounts.find((a) => a.account_id === accountId);
        return {
          accountId: accountId,
          accountName: account?.account_name || "",
          accountUsername: account?.account_username,
          accountPictureUrl: account?.account_picture_url,
          accountType: account?.account_type,
          status: undefined,
          instagramUrl: undefined,
          error: undefined,
        };
      }
    );

    onAccountsChange(newSelectedAccounts);
  };

  const handleRemoveAccount = (accountId: string) => {
    const newSelectedIds = selectedAccountIds.filter((id) => id !== accountId);
    setSelectedAccountIds(newSelectedIds);

    const newSelectedAccounts = selectedAccounts.filter(
      (account) => account.accountId !== accountId
    );
    onAccountsChange(newSelectedAccounts);
  };

  const renderValue = (selected: string[]) => {
    if (selected.length === 0) {
      return <em>Select Instagram Accounts</em>;
    }
    return `${selected.length} account${
      selected.length > 1 ? "s" : ""
    } selected`;
  };

  return (
    <Box>
      <FormControl
        variant="filled"
        fullWidth
        error={!!error}
        disabled={loading || disabled || accounts.length === 0}
      >
        <InputLabel id="instagram-account-select-label">
          Select Instagram Accounts
        </InputLabel>
        <Select
          labelId="instagram-account-select-label"
          multiple
          value={selectedAccountIds}
          onChange={handleAccountChange}
          renderValue={renderValue}
          sx={{
            backgroundColor: "var(--whiteColor)",
            borderRadius: "5px",
          }}
        >
          <MenuItem value="">
            <em>Select Instagram Accounts</em>
          </MenuItem>
          {accounts.map((account) => {
            const isSelected =
              selectedAccountIds.indexOf(account.account_id) > -1;
            return (
              <MenuItem key={account.account_id} value={account.account_id}>
                <Checkbox checked={isSelected} />
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    flex: 1,
                  }}
                >
                  {account.account_picture_url ? (
                    <Badge
                      overlap="circular"
                      anchorOrigin={{ vertical: "top", horizontal: "right" }}
                      badgeContent={
                        isSelected ? (
                          <CheckCircleIcon
                            sx={{
                              color: "primary.main",
                              fontSize: 16,
                              backgroundColor: "white",
                              borderRadius: "50%",
                            }}
                          />
                        ) : null
                      }
                    >
                      <Avatar
                        src={account.account_picture_url}
                        sx={{ width: 32, height: 32 }}
                      />
                    </Badge>
                  ) : (
                    <Avatar sx={{ width: 32, height: 32 }}>
                      {account.account_name.charAt(0).toUpperCase()}
                    </Avatar>
                  )}
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {account.account_name}
                    </Typography>
                    {account.account_username && (
                      <Typography variant="caption" color="text.secondary">
                        @{account.account_username}
                      </Typography>
                    )}
                    {account.account_type && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ ml: 1 }}
                      >
                        • {account.account_type}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>

      {error && (
        <Typography
          variant="caption"
          color="error"
          sx={{ mt: 0.5, display: "block" }}
        >
          {error}
        </Typography>
      )}

      {/* Display selected accounts as chips */}
      {selectedAccounts.length > 0 && (
        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 1 }}>
          {selectedAccounts.map((account) => (
            <Chip
              key={account.accountId}
              label={
                <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                  <span>{account.accountName}</span>
                  {account.accountUsername && (
                    <Typography variant="caption" color="text.secondary">
                      @{account.accountUsername}
                    </Typography>
                  )}
                </Box>
              }
              onDelete={() => handleRemoveAccount(account.accountId)}
              size="small"
              color="primary"
              variant="outlined"
              avatar={
                account.accountPictureUrl ? (
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: "top", horizontal: "right" }}
                    badgeContent={
                      <CheckCircleIcon
                        sx={{
                          color: "primary.main",
                          fontSize: 12,
                          backgroundColor: "white",
                          borderRadius: "50%",
                        }}
                      />
                    }
                  >
                    <Avatar
                      src={account.accountPictureUrl}
                      sx={{ width: 20, height: 20 }}
                    />
                  </Badge>
                ) : (
                  <Avatar sx={{ width: 20, height: 20 }}>
                    {account.accountName.charAt(0).toUpperCase()}
                  </Avatar>
                )
              }
            />
          ))}
        </Box>
      )}

      {/* Multi-account posting indicator */}
      {selectedAccounts.length > 1 && (
        <Typography
          variant="caption"
          color="primary"
          sx={{ mt: 1, display: "block" }}
        >
          📄 Multi-Account Posting: {selectedAccounts.length} accounts selected
        </Typography>
      )}

      {/* No accounts message */}
      {accounts.length === 0 && !loading && (
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mt: 1, display: "block" }}
        >
          No Instagram accounts found. Please connect your Instagram account
          first.
        </Typography>
      )}

      {/* Loading message */}
      {loading && (
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mt: 1, display: "block" }}
        >
          Loading Instagram accounts...
        </Typography>
      )}
    </Box>
  );
};

export default InstagramMultiAccountSelector;
