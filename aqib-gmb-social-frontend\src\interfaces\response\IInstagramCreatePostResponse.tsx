export interface IInstagramCreatePostResponse {
  success: boolean;
  message: string;
  data?: {
    id: number;
    instagramPostId: string;
    accountId: string;
    caption: string;
    mediaUrl: string;
    instagramUrl: string;
    status: string;
    createdTime: string;
  };
  error?: string;
  instagramError?: any;
}

export interface IInstagramAccountData {
  id: number;
  instagram_oauth_token_id: number;
  account_id: string;
  account_name: string;
  account_username?: string;
  account_picture_url?: string;
  account_type?: string;
  is_active: number;
  created_at: string;
  updated_at: string;
  instagram_user_name?: string;
  instagram_user_email?: string;
}

export interface IInstagramAccountsResponse {
  success: boolean;
  message: string;
  data?: {
    accounts: IInstagramAccountData[];
  };
  error?: string;
}

export interface IInstagramAuthResponse {
  success: boolean;
  message: string;
  authUrl?: string;
  data?: {
    user: {
      id: string;
      name: string;
      email: string;
      picture: string;
    };
    accountsCount: number;
  };
  error?: string;
  instagramError?: any;
}

export interface IInstagramPostsResponse {
  success: boolean;
  message: string;
  data?: any[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error?: string;
}
