const axios = require("axios");
const crypto = require("crypto");
const logger = require("../utils/logger");

class TwitterService {
  constructor() {
    this.clientId = process.env.TWITTER_CLIENT_ID;
    this.clientSecret = process.env.TWITTER_CLIENT_SECRET;
    this.redirectUri = process.env.TWITTER_REDIRECT_URI;
    this.apiVersion = process.env.TWITTER_API_VERSION || "v2";
    this.baseURL = "https://api.x.com";
    this.authURL = "https://twitter.com/i/oauth2/authorize";
    this.tokenURL = "https://api.x.com/2/oauth2/token";

    // Store PKCE parameters temporarily (in production, use Redis or database)
    this.pkceStore = new Map();

    // Log configuration (without sensitive data)
    logger.info("Twitter service initialized", {
      hasClientId: !!this.clientId,
      hasClientSecret: !!this.clientSecret,
      redirectUri: this.redirectUri,
      tokenURL: this.tokenURL,
      authURL: this.authURL,
    });
  }

  /**
   * Generate PKCE code verifier and challenge
   * @returns {Object} PKCE parameters
   */
  generatePKCE() {
    const codeVerifier = crypto.randomBytes(32).toString("base64url");
    const codeChallenge = crypto
      .createHash("sha256")
      .update(codeVerifier)
      .digest("base64url");

    return {
      codeVerifier,
      codeChallenge,
      codeChallengeMethod: "S256",
    };
  }

  /**
   * Generate Twitter OAuth URL
   * @param {string} state - State parameter for security
   * @returns {string} OAuth URL
   */
  generateAuthUrl(state) {
    try {
      const scopes = [
        "tweet.read",
        "tweet.write",
        "users.read",
        "offline.access",
      ].join(" ");

      // Generate PKCE parameters
      const pkce = this.generatePKCE();

      // Store PKCE verifier for later use (keyed by state)
      this.pkceStore.set(state, pkce.codeVerifier);

      const params = new URLSearchParams({
        response_type: "code",
        client_id: this.clientId,
        redirect_uri: this.redirectUri,
        scope: scopes,
        state: state,
        code_challenge: pkce.codeChallenge,
        code_challenge_method: pkce.codeChallengeMethod,
      });

      const authUrl = `${this.authURL}?${params.toString()}`;

      logger.info("Twitter OAuth URL generated", {
        redirectUri: this.redirectUri,
        scopes: scopes,
        hasPKCE: true,
      });

      return authUrl;
    } catch (error) {
      logger.error("Error generating Twitter OAuth URL:", {
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * Exchange authorization code for access token
   * @param {string} code - Authorization code
   * @param {string} state - State parameter to retrieve PKCE verifier
   * @returns {Object} Token data
   */
  async exchangeCodeForToken(code, state) {
    try {
      // Retrieve the PKCE verifier using the state
      const codeVerifier = this.pkceStore.get(state);
      if (!codeVerifier) {
        logger.error("PKCE verifier not found", {
          state,
          availableStates: Array.from(this.pkceStore.keys()),
        });
        throw new Error(
          "PKCE verifier not found. Authentication session may have expired."
        );
      }

      const tokenRequestData = {
        grant_type: "authorization_code",
        client_id: this.clientId,
        client_secret: this.clientSecret,
        redirect_uri: this.redirectUri,
        code: code,
        code_verifier: codeVerifier,
      };

      logger.info("Attempting Twitter token exchange", {
        tokenURL: this.tokenURL,
        redirectUri: this.redirectUri,
        clientId: this.clientId,
        hasCode: !!code,
        hasCodeVerifier: !!codeVerifier,
        codeLength: code?.length,
        codeVerifierLength: codeVerifier?.length,
        state: state,
      });

      // Log the actual request data (without sensitive info)
      logger.info("Token request data", {
        grant_type: tokenRequestData.grant_type,
        client_id: tokenRequestData.client_id,
        redirect_uri: tokenRequestData.redirect_uri,
        hasClientSecret: !!tokenRequestData.client_secret,
        hasCode: !!tokenRequestData.code,
        hasCodeVerifier: !!tokenRequestData.code_verifier,
      });

      const response = await axios.post(
        this.tokenURL,
        new URLSearchParams(tokenRequestData),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      // Clean up the stored PKCE verifier
      this.pkceStore.delete(state);

      logger.info("Twitter access token obtained", {
        hasAccessToken: !!response.data.access_token,
        tokenType: response.data.token_type,
        expiresIn: response.data.expires_in,
      });

      return response.data;
    } catch (error) {
      logger.error("Error exchanging code for Twitter token:", {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        responseHeaders: error.response?.headers,
        requestURL: error.config?.url,
        requestData: error.config?.data,
      });
      throw error;
    }
  }

  /**
   * Get user information
   * @param {string} accessToken - Access token
   * @returns {Object} User data
   */
  async getUserInfo(accessToken) {
    try {
      const response = await axios.get(`${this.baseURL}/2/users/me`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        params: {
          "user.fields":
            "id,name,username,description,profile_image_url,public_metrics,verified",
        },
      });

      logger.info("Twitter user info retrieved", {
        userId: response.data.data.id,
        username: response.data.data.username,
        name: response.data.data.name,
      });

      return response.data.data;
    } catch (error) {
      logger.error("Error getting Twitter user info:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create a tweet
   * @param {string} accessToken - Access token
   * @param {Object} tweetData - Tweet data
   * @returns {Object} Tweet response
   */
  async createTweet(accessToken, tweetData) {
    try {
      const payload = {
        text: tweetData.text,
      };

      // Add media if provided
      if (tweetData.media_ids && tweetData.media_ids.length > 0) {
        payload.media = {
          media_ids: tweetData.media_ids,
        };
      }

      // Add reply settings if provided
      if (tweetData.reply_to) {
        payload.reply = {
          in_reply_to_tweet_id: tweetData.reply_to,
        };
      }

      // Add quote tweet if provided
      if (tweetData.quote_tweet_id) {
        payload.quote_tweet_id = tweetData.quote_tweet_id;
      }

      const response = await axios.post(`${this.baseURL}/2/tweets`, payload, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      });

      logger.info("Tweet created successfully", {
        tweetId: response.data.data.id,
        text: tweetData.text.substring(0, 50) + "...",
      });

      return response.data.data;
    } catch (error) {
      logger.error("Error creating tweet:", {
        error: error.message,
        response: error.response?.data,
        text: tweetData.text?.substring(0, 50) + "...",
      });
      throw error;
    }
  }

  /**
   * Upload media to Twitter
   * @param {string} accessToken - Access token
   * @param {Buffer} mediaBuffer - Media buffer
   * @param {string} mediaType - Media type (image/jpeg, image/png, etc.)
   * @returns {Object} Media upload response
   */
  async uploadMedia(accessToken, mediaBuffer, mediaType) {
    try {
      // Twitter uses a different endpoint for media upload (v1.1)
      const uploadUrl = "https://upload.twitter.com/1.1/media/upload.json";

      const formData = new FormData();
      formData.append("media", mediaBuffer, {
        contentType: mediaType,
        filename: `media.${mediaType.split("/")[1]}`,
      });

      const response = await axios.post(uploadUrl, formData, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          ...formData.getHeaders(),
        },
      });

      logger.info("Media uploaded to Twitter successfully", {
        mediaId: response.data.media_id_string,
        mediaType: mediaType,
        size: mediaBuffer.length,
      });

      return response.data;
    } catch (error) {
      logger.error("Error uploading media to Twitter:", {
        error: error.message,
        response: error.response?.data,
        mediaType: mediaType,
      });
      throw error;
    }
  }

  /**
   * Generate Twitter post URL
   * @param {string} username - Twitter username
   * @param {string} tweetId - Tweet ID
   * @returns {string} Twitter URL
   */
  generatePostUrl(username, tweetId) {
    return `https://twitter.com/${username}/status/${tweetId}`;
  }

  /**
   * Get tweet metrics
   * @param {string} accessToken - Access token
   * @param {string} tweetId - Tweet ID
   * @returns {Object} Tweet metrics
   */
  async getTweetMetrics(accessToken, tweetId) {
    try {
      const response = await axios.get(`${this.baseURL}/2/tweets/${tweetId}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        params: {
          "tweet.fields": "public_metrics,created_at",
        },
      });

      logger.info("Tweet metrics retrieved", {
        tweetId: tweetId,
        metrics: response.data.data.public_metrics,
      });

      return response.data.data;
    } catch (error) {
      logger.error("Error getting tweet metrics:", {
        error: error.message,
        response: error.response?.data,
        tweetId: tweetId,
      });
      throw error;
    }
  }

  /**
   * Refresh access token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} New token data
   */
  async refreshAccessToken(refreshToken) {
    try {
      const response = await axios.post(
        this.tokenURL,
        new URLSearchParams({
          grant_type: "refresh_token",
          client_id: this.clientId,
          client_secret: this.clientSecret,
          refresh_token: refreshToken,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      logger.info("Twitter access token refreshed", {
        hasAccessToken: !!response.data.access_token,
        expiresIn: response.data.expires_in,
      });

      return response.data;
    } catch (error) {
      logger.error("Error refreshing Twitter token:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }
}

module.exports = new TwitterService();
