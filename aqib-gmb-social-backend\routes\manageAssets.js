const express = require("express");
const router = express.Router();
const multer = require("multer");
const { isAuthenticated } = require("../middleware/isAuthenticated");
const {
  welcome,
  uploadAssets,
  getAssets,
  getAssetById,
  deleteAsset,
  updateMaxUploadSize,
  refreshAssetUrls,
} = require("../controllers/manageAssets.controller");

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB per file limit
    files: 10, // Maximum 10 files per upload
  },
  fileFilter: (req, file, cb) => {
    // Validate file types
    const allowedMimeTypes = [
      // Images
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
      // Videos
      "video/mp4",
      "video/avi",
      "video/mov",
      "video/wmv",
      "video/flv",
      "video/webm",
    ];

    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Unsupported file type: ${file.mimetype}`), false);
    }
  },
});

/**
 * @swagger
 * components:
 *   schemas:
 *     Asset:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Asset ID
 *         business_id:
 *           type: integer
 *           description: Business ID
 *         user_id:
 *           type: integer
 *           description: User ID who uploaded the asset
 *         file_name:
 *           type: string
 *           description: Unique file name
 *         original_file_name:
 *           type: string
 *           description: Original file name
 *         file_type:
 *           type: string
 *           enum: [image, video]
 *           description: Type of file
 *         file_size:
 *           type: integer
 *           description: File size in bytes
 *         s3_key:
 *           type: string
 *           description: S3 object key
 *         s3_url:
 *           type: string
 *           description: S3 public URL
 *         mime_type:
 *           type: string
 *           description: MIME type of the file
 *         upload_date:
 *           type: string
 *           format: date-time
 *           description: Upload timestamp
 *         status:
 *           type: string
 *           enum: [active, deleted]
 *           description: Asset status
 */

/**
 * @swagger
 * /manage-assets:
 *   get:
 *     summary: Welcome endpoint for manage assets
 *     tags: [Manage Assets]
 *     responses:
 *       200:
 *         description: Welcome message
 */
router.get("/", welcome);

/**
 * @swagger
 * /manage-assets/upload/{businessId}:
 *   post:
 *     summary: Upload assets for a business
 *     tags: [Manage Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: businessId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Business ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Files to upload (images and videos)
 *     responses:
 *       200:
 *         description: Files uploaded successfully
 *       400:
 *         description: Bad request or storage limit exceeded
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/upload/:businessId",
  isAuthenticated,
  upload.array("files", 10),
  uploadAssets
);

/**
 * @swagger
 * /manage-assets/business/{businessId}:
 *   get:
 *     summary: Get assets for a business
 *     tags: [Manage Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: businessId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Business ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Records per page
 *     responses:
 *       200:
 *         description: Assets retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No assets found
 */
router.get("/business/:businessId", isAuthenticated, getAssets);

/**
 * @swagger
 * /manage-assets/asset/{assetId}:
 *   get:
 *     summary: Get single asset by ID
 *     tags: [Manage Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assetId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Asset ID
 *     responses:
 *       200:
 *         description: Asset retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Asset not found
 */
router.get("/asset/:assetId", isAuthenticated, getAssetById);

/**
 * @swagger
 * /manage-assets/asset/{assetId}:
 *   delete:
 *     summary: Delete an asset
 *     tags: [Manage Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assetId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Asset ID
 *     responses:
 *       200:
 *         description: Asset deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Asset not found
 */
router.delete("/asset/:assetId", isAuthenticated, deleteAsset);

/**
 * @swagger
 * /manage-assets/business/{businessId}/max-size:
 *   put:
 *     summary: Update max upload size for a business
 *     tags: [Manage Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: businessId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Business ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               maxSizeMB:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 10240
 *                 description: Maximum upload size in MB (1MB to 10GB)
 *     responses:
 *       200:
 *         description: Max upload size updated successfully
 *       400:
 *         description: Invalid size value
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Business not found
 */
router.put(
  "/business/:businessId/max-size",
  isAuthenticated,
  updateMaxUploadSize
);

/**
 * @swagger
 * /manage-assets/asset/{assetId}/refresh-urls:
 *   put:
 *     summary: Refresh asset URLs (generate new signed URLs)
 *     tags: [Manage Assets]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assetId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Asset ID
 *     responses:
 *       200:
 *         description: Asset URLs refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Asset'
 *       400:
 *         description: Asset ID is required
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Asset not found
 */
router.put("/asset/:assetId/refresh-urls", isAuthenticated, refreshAssetUrls);

module.exports = router;
