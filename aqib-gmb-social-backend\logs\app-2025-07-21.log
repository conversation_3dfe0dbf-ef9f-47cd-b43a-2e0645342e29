{"timestamp":"2025-07-21T08:43:32.758Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T08:43:32.773Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T08:43:36.748Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T08:46:38.982Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T08:46:38.996Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T08:46:39.595Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:16:41.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T09:14:20.816Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T09:14:20.832Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T09:14:21.530Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:44:22.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T09:14:45.683Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T09:14:45.696Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T09:14:46.091Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:44:47.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T09:14:56.764Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T09:14:56.776Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T09:14:57.203Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:44:58.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T10:08:31.074Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T10:08:31.098Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T10:08:32.060Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T04:38:33.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T10:19:17.663Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T10:19:17.681Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T10:19:18.250Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T04:49:19.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T12:08:16.349Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T12:08:16.434Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T12:08:22.815Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T06:38:24.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T12:50:50.202Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T12:50:50.225Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T12:50:50.934Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T07:20:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T12:55:43.965Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"15be17ac-e218-4c81-8b46-7ff227ae0da2","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-21T12:55:43.967Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"15be17ac-e218-4c81-8b46-7ff227ae0da2","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-21T12:55:44.549Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"15be17ac-e218-4c81-8b46-7ff227ae0da2","userId":132,"email":"<EMAIL>"}
{"timestamp":"2025-07-21T12:55:46.941Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T12:55:47.080Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T12:55:48.796Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bf74c658-2005-4f97-afb7-7456ed3dee2e","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T12:55:48.801Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"bf74c658-2005-4f97-afb7-7456ed3dee2e","userId":"132"}
{"timestamp":"2025-07-21T12:55:48.815Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"99e03271-8538-4070-860e-2c204bb78f94","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T12:55:48.828Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2a117c0b-20f5-4bcf-82ad-648c0cf8e5b7","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T12:55:48.838Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dce0e32c-7a51-4e9d-a3df-d3277f4403c9","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T12:55:48.852Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T12:55:48.993Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T12:55:48.995Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"dce0e32c-7a51-4e9d-a3df-d3277f4403c9","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T12:55:49.002Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T12:55:49.007Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T12:55:49.776Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T12:55:49.778Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T12:55:49.783Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T12:55:49.785Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T12:55:49.787Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T12:57:15.037Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T12:57:15.139Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T12:57:16.548Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"5ab576b9-aa85-4551-b7fa-81a283f26caf","userId":"132"}
{"timestamp":"2025-07-21T12:57:16.552Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"4fca9bd3-c9b0-42d7-95a3-d492bfa3cd15","userId":"132","unreadOnly":"false"}
{"timestamp":"2025-07-21T12:57:16.587Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"132","rowCount":1,"sampleRow":{"id":7,"name":"Sri Eye Care 5 Kms","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-21T12:58:03.342Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"5472eaa5-916c-46bc-9bf9-da232c7faa46","query":"sri","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:06.769Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"5472eaa5-916c-46bc-9bf9-da232c7faa46","count":8}
{"timestamp":"2025-07-21T12:58:09.311Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"e008c2ff-93bf-4901-9368-8d2abd97770a","query":"sri ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:11.800Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"e008c2ff-93bf-4901-9368-8d2abd97770a","count":4}
{"timestamp":"2025-07-21T12:58:13.914Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"1074d6af-62be-439e-bd44-1233801ed892","query":"sri e","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:15.440Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"356bf1cb-28e6-435c-b998-9a4ddb30741e","query":"sri eye","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:16.111Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"1074d6af-62be-439e-bd44-1233801ed892","count":0}
{"timestamp":"2025-07-21T12:58:20.794Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"356bf1cb-28e6-435c-b998-9a4ddb30741e","count":1}
{"timestamp":"2025-07-21T12:58:22.843Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"5b2e0e96-05eb-4497-897c-cb6d9e6bfd55","query":"Sri Eye Care Speciality Eye Hospital","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:25.610Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"b1bc5f3f-b05c-4ba4-afd1-30fc8ce454a3","keyword":"eye hospital near me","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"6x6","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-21T12:58:26.363Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"5b2e0e96-05eb-4497-897c-cb6d9e6bfd55","count":1}
{"timestamp":"2025-07-21T13:03:57.245Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"267dfde3-eb64-46ef-bc9e-dac5032507ba","keyword":"eye hospital near me","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-21T13:03:57.250Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"267dfde3-eb64-46ef-bc9e-dac5032507ba","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital near me","lat":13.0196844,"lng":77.6285592,"grid_size":"5","original_grid_size":"5x5","radius":1,"measurement":"km","original_unit":"kilometers","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-21T13:04:05.472Z","level":"INFO","message":"Local Falcon scan API response","environment":"DEVELOPMENT","requestId":"267dfde3-eb64-46ef-bc9e-dac5032507ba","status":200,"dataKeys":["code","code_desc","success","message","parameters","data"]}
{"timestamp":"2025-07-21T13:15:04.202Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"abacc25d-b4d0-424f-a0dd-963cedd8fb63","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T13:15:04.204Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"abacc25d-b4d0-424f-a0dd-963cedd8fb63","userId":"132"}
{"timestamp":"2025-07-21T13:15:04.210Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9741bee1-09df-49eb-84c5-fce83f92fe57","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-07-21T13:15:04.260Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"abacc25d-b4d0-424f-a0dd-963cedd8fb63","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T13:15:05.682Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"28a30d53-f827-499b-b5be-44054ea356bb","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T13:15:05.687Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a3b26c97-b71b-4091-9cf0-143bf7f09272","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T13:15:05.688Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"a3b26c97-b71b-4091-9cf0-143bf7f09272","userId":"132"}
{"timestamp":"2025-07-21T13:15:05.693Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"178b130d-bae9-4fef-90f4-7a03d22e18b8","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T13:15:05.704Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"afe595e0-6959-45c2-a929-cfded2deb0d6","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T13:15:05.710Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T13:15:05.715Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:15:05.722Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T13:15:05.725Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"178b130d-bae9-4fef-90f4-7a03d22e18b8","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T13:15:05.730Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:15:06.884Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T13:15:06.886Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T13:15:06.888Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T13:15:06.889Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T13:15:06.890Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T13:21:16.250Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T13:21:16.378Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T13:21:50.563Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f797900a-12d6-41e8-87bd-ead0fc6cacca","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T13:21:50.575Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-37ef-430d-bb48-1b5bea38db07","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T13:21:50.583Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bbdcb7c0-f65f-4387-a923-d2e50b82fd59","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T13:21:50.601Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-2ad3-4261-b37e-1c4b47d49377","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T13:21:50.605Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"********-2ad3-4261-b37e-1c4b47d49377","userId":"132"}
{"timestamp":"2025-07-21T13:21:50.614Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:21:50.621Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T13:21:50.624Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"********-37ef-430d-bb48-1b5bea38db07","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T13:21:50.631Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T13:21:50.655Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:21:51.575Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T13:21:51.577Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T13:21:51.578Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T13:21:51.580Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T13:21:51.581Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T13:27:05.820Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T13:27:05.967Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T15:52:32.923Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T15:52:33.114Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T17:43:13.972Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T17:43:14.043Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T17:43:18.981Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T12:13:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:17:12.332Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:17:12.357Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:17:19.115Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T13:47:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:17:31.898Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:17:31.899Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:17:31.901Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:17:31.903Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:17:31.904Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:18:07.647Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:18:07.782Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
