{"timestamp":"2025-07-21T08:43:32.758Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T08:43:32.773Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T08:43:36.748Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T08:46:38.982Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T08:46:38.996Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T08:46:39.595Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:16:41.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T09:14:20.816Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T09:14:20.832Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T09:14:21.530Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:44:22.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T09:14:45.683Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T09:14:45.696Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T09:14:46.091Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:44:47.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T09:14:56.764Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T09:14:56.776Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T09:14:57.203Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T03:44:58.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T10:08:31.074Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T10:08:31.098Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T10:08:32.060Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3001"}
{"timestamp":"2025-07-21T04:38:33.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T10:19:17.663Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T10:19:17.681Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T10:19:18.250Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T04:49:19.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T12:08:16.349Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T12:08:16.434Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T12:08:22.815Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T06:38:24.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T12:50:50.202Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T12:50:50.225Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T12:50:50.934Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T07:20:52.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T12:55:43.965Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"15be17ac-e218-4c81-8b46-7ff227ae0da2","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-21T12:55:43.967Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"15be17ac-e218-4c81-8b46-7ff227ae0da2","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-21T12:55:44.549Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"15be17ac-e218-4c81-8b46-7ff227ae0da2","userId":132,"email":"<EMAIL>"}
{"timestamp":"2025-07-21T12:55:46.941Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T12:55:47.080Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T12:55:48.796Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bf74c658-2005-4f97-afb7-7456ed3dee2e","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T12:55:48.801Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"bf74c658-2005-4f97-afb7-7456ed3dee2e","userId":"132"}
{"timestamp":"2025-07-21T12:55:48.815Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"99e03271-8538-4070-860e-2c204bb78f94","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T12:55:48.828Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2a117c0b-20f5-4bcf-82ad-648c0cf8e5b7","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T12:55:48.838Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dce0e32c-7a51-4e9d-a3df-d3277f4403c9","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T12:55:48.852Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T12:55:48.993Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T12:55:48.995Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"dce0e32c-7a51-4e9d-a3df-d3277f4403c9","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T12:55:49.002Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T12:55:49.007Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T12:55:49.776Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T12:55:49.778Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T12:55:49.783Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T12:55:49.785Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T12:55:49.787Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"715f2ca6-c610-45a6-ab59-b77eee4e64e5","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T12:57:15.037Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T12:57:15.139Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T12:57:16.548Z","level":"INFO","message":"Getting Local Falcon configurations","environment":"DEVELOPMENT","requestId":"5ab576b9-aa85-4551-b7fa-81a283f26caf","userId":"132"}
{"timestamp":"2025-07-21T12:57:16.552Z","level":"INFO","message":"Getting Local Falcon alerts","environment":"DEVELOPMENT","requestId":"4fca9bd3-c9b0-42d7-95a3-d492bfa3cd15","userId":"132","unreadOnly":"false"}
{"timestamp":"2025-07-21T12:57:16.587Z","level":"INFO","message":"Raw configurations from database","environment":"DEVELOPMENT","userId":"132","rowCount":1,"sampleRow":{"id":7,"name":"Sri Eye Care 5 Kms","settingsType":"object","settingsValue":{}}}
{"timestamp":"2025-07-21T12:58:03.342Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"5472eaa5-916c-46bc-9bf9-da232c7faa46","query":"sri","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:06.769Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"5472eaa5-916c-46bc-9bf9-da232c7faa46","count":8}
{"timestamp":"2025-07-21T12:58:09.311Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"e008c2ff-93bf-4901-9368-8d2abd97770a","query":"sri ","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:11.800Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"e008c2ff-93bf-4901-9368-8d2abd97770a","count":4}
{"timestamp":"2025-07-21T12:58:13.914Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"1074d6af-62be-439e-bd44-1233801ed892","query":"sri e","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:15.440Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"356bf1cb-28e6-435c-b998-9a4ddb30741e","query":"sri eye","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:16.111Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"1074d6af-62be-439e-bd44-1233801ed892","count":0}
{"timestamp":"2025-07-21T12:58:20.794Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"356bf1cb-28e6-435c-b998-9a4ddb30741e","count":1}
{"timestamp":"2025-07-21T12:58:22.843Z","level":"INFO","message":"Searching places with Local Falcon API","environment":"DEVELOPMENT","requestId":"5b2e0e96-05eb-4497-897c-cb6d9e6bfd55","query":"Sri Eye Care Speciality Eye Hospital","lat":0,"lng":0,"radius":1,"unit":"kilometers"}
{"timestamp":"2025-07-21T12:58:25.610Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"b1bc5f3f-b05c-4ba4-afd1-30fc8ce454a3","keyword":"eye hospital near me","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"6x6","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-21T12:58:26.363Z","level":"INFO","message":"Local Falcon places API response received","environment":"DEVELOPMENT","requestId":"5b2e0e96-05eb-4497-897c-cb6d9e6bfd55","count":1}
{"timestamp":"2025-07-21T13:03:57.245Z","level":"INFO","message":"Running grid scan","environment":"DEVELOPMENT","requestId":"267dfde3-eb64-46ef-bc9e-dac5032507ba","keyword":"eye hospital near me","businessName":"Sri Eye Care Speciality Eye Hospital","lat":13.0196844,"lng":77.6285592,"gridSize":"5x5","radius":1,"unit":"kilometers","placeId":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}
{"timestamp":"2025-07-21T13:03:57.250Z","level":"INFO","message":"Calling Local Falcon scan API","environment":"DEVELOPMENT","requestId":"267dfde3-eb64-46ef-bc9e-dac5032507ba","endpoint":"https://api.localfalcon.com/v1/scan/","parameters":{"keyword":"eye hospital near me","lat":13.0196844,"lng":77.6285592,"grid_size":"5","original_grid_size":"5x5","radius":1,"measurement":"km","original_unit":"kilometers","place_id":"ChIJceJaLR8XrjsRLtW3QoeKOxc"}}
{"timestamp":"2025-07-21T13:04:05.472Z","level":"INFO","message":"Local Falcon scan API response","environment":"DEVELOPMENT","requestId":"267dfde3-eb64-46ef-bc9e-dac5032507ba","status":200,"dataKeys":["code","code_desc","success","message","parameters","data"]}
{"timestamp":"2025-07-21T13:15:04.202Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"abacc25d-b4d0-424f-a0dd-963cedd8fb63","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T13:15:04.204Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"abacc25d-b4d0-424f-a0dd-963cedd8fb63","userId":"132"}
{"timestamp":"2025-07-21T13:15:04.210Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9741bee1-09df-49eb-84c5-fce83f92fe57","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-07-21T13:15:04.260Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"abacc25d-b4d0-424f-a0dd-963cedd8fb63","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T13:15:05.682Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"28a30d53-f827-499b-b5be-44054ea356bb","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T13:15:05.687Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a3b26c97-b71b-4091-9cf0-143bf7f09272","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T13:15:05.688Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"a3b26c97-b71b-4091-9cf0-143bf7f09272","userId":"132"}
{"timestamp":"2025-07-21T13:15:05.693Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"178b130d-bae9-4fef-90f4-7a03d22e18b8","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T13:15:05.704Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"afe595e0-6959-45c2-a929-cfded2deb0d6","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T13:15:05.710Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T13:15:05.715Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:15:05.722Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T13:15:05.725Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"178b130d-bae9-4fef-90f4-7a03d22e18b8","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T13:15:05.730Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:15:06.884Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T13:15:06.886Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T13:15:06.888Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T13:15:06.889Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T13:15:06.890Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"2565fc21-41eb-4953-bd19-9e437b5849aa","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T13:21:16.250Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T13:21:16.378Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T13:21:50.563Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f797900a-12d6-41e8-87bd-ead0fc6cacca","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T13:21:50.575Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-37ef-430d-bb48-1b5bea38db07","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T13:21:50.583Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bbdcb7c0-f65f-4387-a923-d2e50b82fd59","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T13:21:50.601Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-2ad3-4261-b37e-1c4b47d49377","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T13:21:50.605Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"********-2ad3-4261-b37e-1c4b47d49377","userId":"132"}
{"timestamp":"2025-07-21T13:21:50.614Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:21:50.621Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T13:21:50.624Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"********-37ef-430d-bb48-1b5bea38db07","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T13:21:50.631Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T13:21:50.655Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T13:21:51.575Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T13:21:51.577Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T13:21:51.578Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T13:21:51.580Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T13:21:51.581Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"902ee381-806b-489f-bbd0-064ad984511f","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T13:27:05.820Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T13:27:05.967Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T15:52:32.923Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-21","endDate":"2025-07-21"}
{"timestamp":"2025-07-21T15:52:33.114Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T17:43:13.972Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T17:43:14.043Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T17:43:18.981Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T12:13:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:17:12.332Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:17:12.357Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:17:19.115Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T13:47:20.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:17:31.898Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:17:31.899Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:17:31.901Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:17:31.903Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:17:31.904Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"da248ab3-6294-403c-8d8b-bb0fc3dd51cb","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:18:07.647Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:18:07.782Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T19:22:55.225Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"781e3285-0a18-4d9a-b8ae-8d1a6f21ca4a","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:22:55.270Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:22:55.294Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"42d925ba-3e36-481b-a462-65b004278cb3","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:22:55.296Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"42d925ba-3e36-481b-a462-65b004278cb3","userId":"132"}
{"timestamp":"2025-07-21T19:22:55.316Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-aa8a-4bf7-911b-283ada2c6fa9","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:22:55.339Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:22:55.378Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:22:55.465Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"af3e9efa-6682-477d-b3c6-d618201f50e0","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:22:55.523Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:22:55.525Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"af3e9efa-6682-477d-b3c6-d618201f50e0","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:23:55.822Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:23:55.855Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:23:57.789Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T13:53:59.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:24:11.970Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"93652e28-1158-4af2-a3ef-1a24158e0003","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:24:11.987Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f5b37233-cd34-4479-a4ed-706db06f8455","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:24:12.049Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"986e571a-d67c-4dd2-b841-89ed9b1b6248","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:24:12.082Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8da17236-df38-4a2d-a77f-6943bb086c6c","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:24:12.090Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"8da17236-df38-4a2d-a77f-6943bb086c6c","userId":"132"}
{"timestamp":"2025-07-21T19:24:12.227Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:24:12.243Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:24:12.258Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:24:12.297Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:24:12.299Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"f5b37233-cd34-4479-a4ed-706db06f8455","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:24:55.313Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1865fe11-94b5-4abe-b8d5-a3a1823281f4","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:24:55.317Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"149141ff-c4a8-452e-8830-73af69dd7fd2","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:24:55.319Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"149141ff-c4a8-452e-8830-73af69dd7fd2","userId":"132"}
{"timestamp":"2025-07-21T19:24:55.324Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c9b67c1c-67bc-46f1-905e-61744fd9af8d","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:24:55.335Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"179caf4d-b066-4df5-a512-ea2cf8efb026","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:24:55.342Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:24:55.357Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:24:55.373Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:24:55.376Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"179caf4d-b066-4df5-a512-ea2cf8efb026","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:24:55.394Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T19:24:56.769Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"42674bc5-ed7b-4a7f-974e-aa6e80612bd3","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:24:56.771Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"42674bc5-ed7b-4a7f-974e-aa6e80612bd3","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:24:56.773Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"42674bc5-ed7b-4a7f-974e-aa6e80612bd3","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:24:56.774Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:24:56.776Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"42674bc5-ed7b-4a7f-974e-aa6e80612bd3","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:27:47.578Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:27:47.732Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T19:27:49.035Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a774ef76-1eb5-42f3-b91b-93edf6c102f1","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:27:49.060Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ea4b9a7c-1f31-49a1-8a03-bab24960c1cd","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:27:49.066Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"ea4b9a7c-1f31-49a1-8a03-bab24960c1cd","userId":"132"}
{"timestamp":"2025-07-21T19:27:49.088Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f9ca6e53-ffa6-4ceb-8085-f0401b1e8440","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:27:49.097Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8e4d9bd8-820c-497f-bcb3-7162c3d39caa","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:27:49.104Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T19:27:49.117Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:27:49.156Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:27:49.204Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:27:49.227Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"8e4d9bd8-820c-497f-bcb3-7162c3d39caa","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:27:50.041Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e176bdf8-90ff-42b6-9b17-65a430fa30e5","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:27:50.042Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"e176bdf8-90ff-42b6-9b17-65a430fa30e5","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:27:50.043Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"e176bdf8-90ff-42b6-9b17-65a430fa30e5","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:27:50.044Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:27:50.046Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"e176bdf8-90ff-42b6-9b17-65a430fa30e5","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:30:06.870Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:30:06.963Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T19:30:09.060Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"05b75604-e6d7-4e0d-8337-b74e4f2186fd","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:30:09.080Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"66a29fdf-6092-4c32-acd1-81b5181447a9","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:30:09.087Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5d4fd2dd-46ef-4d75-a60b-d600d3805df7","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:30:09.096Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"51f081dc-1227-4aa5-93fb-b61081184ec6","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:30:09.098Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"51f081dc-1227-4aa5-93fb-b61081184ec6","userId":"132"}
{"timestamp":"2025-07-21T19:30:09.107Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T19:30:09.128Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:30:09.130Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"66a29fdf-6092-4c32-acd1-81b5181447a9","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:30:09.135Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:30:09.142Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:30:10.540Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"12d5ee11-b3b8-49bb-87c3-a72dc3f74b9c","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:30:10.541Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"12d5ee11-b3b8-49bb-87c3-a72dc3f74b9c","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:30:10.543Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"12d5ee11-b3b8-49bb-87c3-a72dc3f74b9c","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:30:10.544Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:30:10.545Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"12d5ee11-b3b8-49bb-87c3-a72dc3f74b9c","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:36:11.662Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:36:11.797Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T19:36:12.850Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"263d3339-ed46-49a6-8e02-6629f69483b2","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:36:12.863Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3290cf39-1961-4359-9828-b620dc5176b3","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:36:12.870Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"3290cf39-1961-4359-9828-b620dc5176b3","userId":"132"}
{"timestamp":"2025-07-21T19:36:12.890Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8ed84783-3571-4736-ab8b-aa6aefe54204","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:36:12.900Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"59fa98a2-234b-4ef5-afe9-0711fbf2f6bd","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:36:12.907Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T19:36:12.917Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:36:12.934Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:36:12.942Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:36:12.943Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"59fa98a2-234b-4ef5-afe9-0711fbf2f6bd","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:36:14.074Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dc82aa9e-4329-4d24-bc90-586980f4dcac","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:36:14.075Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"dc82aa9e-4329-4d24-bc90-586980f4dcac","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:36:14.077Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"dc82aa9e-4329-4d24-bc90-586980f4dcac","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:36:14.078Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:36:14.080Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"dc82aa9e-4329-4d24-bc90-586980f4dcac","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:36:42.478Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:36:42.491Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:36:42.975Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T14:06:44.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:37:19.202Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:37:19.320Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T19:37:20.494Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a480e2cd-c32c-4707-9bb7-0bffd653fb47","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:37:20.499Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fa72573a-62f3-4b51-a9a1-f0838568442a","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:37:20.505Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b4a25a69-530d-4a94-86f5-b624c600803e","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:37:20.506Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"b4a25a69-530d-4a94-86f5-b624c600803e","userId":"132"}
{"timestamp":"2025-07-21T19:37:20.516Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ab710e81-9cf9-4465-995c-789441aa0b8d","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:37:20.528Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-21T19:37:20.634Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:37:20.641Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:37:20.643Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"ab710e81-9cf9-4465-995c-789441aa0b8d","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:37:20.647Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:37:21.729Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"27a430f9-1690-4f33-9c11-9b894b1f8fb4","controller":"facebook","action":"authenticate"}
{"timestamp":"2025-07-21T19:37:21.729Z","level":"INFO","message":"Facebook authenticate request","environment":"DEVELOPMENT","requestId":"27a430f9-1690-4f33-9c11-9b894b1f8fb4","body":{"userId":132},"headers":{"host":"localhost:3000","connection":"keep-alive","content-length":"14","sec-ch-ua-platform":"\"Windows\"","authorization":"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","authentication-token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************.R5Fc_kOG5v4BbVGtq2pZvEe8FyQ-VCkIzcNdpBQXiyU","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"*/*","content-type":"application/json","origin":"http://localhost:5173","sec-fetch-site":"same-site","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:5173/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-GB,en-US;q=0.9,en;q=0.8","dnt":"1"},"method":"POST","url":"/authenticate"}
{"timestamp":"2025-07-21T19:37:21.731Z","level":"INFO","message":"Extracted values","environment":"DEVELOPMENT","requestId":"27a430f9-1690-4f33-9c11-9b894b1f8fb4","userId":132,"userIdType":"number"}
{"timestamp":"2025-07-21T19:37:21.732Z","level":"INFO","message":"Facebook OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email"}
{"timestamp":"2025-07-21T19:37:21.733Z","level":"INFO","message":"Facebook authentication URL generated","environment":"DEVELOPMENT","requestId":"27a430f9-1690-4f33-9c11-9b894b1f8fb4","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-21T19:38:55.374Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:38:55.390Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:38:56.117Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T14:08:57.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:39:36.889Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-22","endDate":"2025-07-22"}
{"timestamp":"2025-07-21T19:39:37.018Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-21T19:39:38.655Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"83c29af4-d749-4423-aaa7-536c2bc01e5c","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:39:38.657Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"83c29af4-d749-4423-aaa7-536c2bc01e5c","userId":"132"}
{"timestamp":"2025-07-21T19:39:38.667Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fa906f2a-e79e-4b62-8026-8f25b7828f14","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:39:38.676Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4a214d41-3bb3-4bd6-bf94-b174f3512e6a","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:39:38.691Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"896c1520-072b-4def-b10a-5c83a26ef597","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:39:38.698Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:39:38.803Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:39:38.805Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"896c1520-072b-4def-b10a-5c83a26ef597","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:39:38.808Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:39:38.841Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:39:56.690Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"123c85ac-e4e3-420a-8d41-21bf9096ae02","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:39:56.690Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"123c85ac-e4e3-420a-8d41-21bf9096ae02","userId":"132"}
{"timestamp":"2025-07-21T19:39:56.770Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"123c85ac-e4e3-420a-8d41-21bf9096ae02","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:39:56.835Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7de02ac4-0ad8-4d5b-ae58-73a1987ed58f","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:39:57.003Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T19:40:04.544Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753126804544-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:40:07.340Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753126804544-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753126804544-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:40:07.390Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"453a874d-318e-4c42-bdae-1611fe9234e2","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:40:07.438Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:40:07.440Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"453a874d-318e-4c42-bdae-1611fe9234e2","pageId":"727479083781060","postData":{"message":"To the Visionaries Behind Every Clear Sight\n\nTo the Visionaries Behind Every Clear Sight — Thank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:40:07.445Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:40:11.625Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A_9qh-bkYdleU1dLl_6RIz9"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:212:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:40:11.627Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"453a874d-318e-4c42-bdae-1611fe9234e2","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at BrotliDecompress.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at BrotliDecompress.emit (node:events:526:35)\n    at BrotliDecompress.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:201:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A_9qh-bkYdleU1dLl_6RIz9"}},"statusCode":400}
{"timestamp":"2025-07-21T19:40:16.149Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753126816148-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:40:18.972Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753126816148-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753126816148-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:40:19.027Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3802c67b-6abe-43bf-97c8-fdb247eed4cb","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:40:19.069Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:40:19.071Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"3802c67b-6abe-43bf-97c8-fdb247eed4cb","pageId":"727479083781060","postData":{"message":"To the Visionaries Behind Every Clear Sight\n\nTo the Visionaries Behind Every Clear Sight — Thank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:40:19.073Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:40:20.398Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A1VsYb-Ar2Qe2tFpVzHQS-D"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:212:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:40:20.400Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"3802c67b-6abe-43bf-97c8-fdb247eed4cb","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at BrotliDecompress.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at BrotliDecompress.emit (node:events:526:35)\n    at BrotliDecompress.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:201:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A1VsYb-Ar2Qe2tFpVzHQS-D"}},"statusCode":400}
{"timestamp":"2025-07-21T19:44:22.955Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:44:22.973Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:44:23.401Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T14:14:25.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:44:39.103Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127079103-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:44:41.980Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127079103-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127079103-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:44:42.062Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0facb537-3844-4283-9f1b-2a8e9011ac1b","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:44:42.098Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:44:42.102Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"0facb537-3844-4283-9f1b-2a8e9011ac1b","pageId":"727479083781060","postData":{"message":"To the Visionaries Behind Every Clear Sight\n\nTo the Visionaries Behind Every Clear Sight — Thank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:44:42.106Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:44:42.107Z","level":"INFO","message":"Downloading image from URL for Facebook upload","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127079103-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753731881&Signature=Etr6ib9XPbQpqRafU3nbbmYAQds%3D"}
{"timestamp":"2025-07-21T19:44:44.891Z","level":"INFO","message":"Image downloaded and prepared for Facebook upload","environment":"DEVELOPMENT","imageSize":0,"contentType":"image/png"}
{"timestamp":"2025-07-21T19:44:45.310Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"Axb5R9giquRgkDCb_KBC1zh"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:257:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:44:45.312Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"0facb537-3844-4283-9f1b-2a8e9011ac1b","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:245:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"Axb5R9giquRgkDCb_KBC1zh"}},"statusCode":400}
{"timestamp":"2025-07-21T19:47:39.043Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-21T19:47:39.059Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-21T19:47:39.800Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-21T14:17:41.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-21T19:47:55.030Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6ed38229-05d1-45e8-8675-af5ff672e103","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:47:55.138Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a17e1f68-ac96-446f-9cb7-65a42e95049f","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:47:55.157Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d33b939f-39f2-45de-a4e2-ebb07200afe7","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:47:55.189Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"d33b939f-39f2-45de-a4e2-ebb07200afe7","userId":"132"}
{"timestamp":"2025-07-21T19:47:55.205Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b43b8eab-295c-47c9-a468-fcee2993d04b","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:47:55.337Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:47:55.345Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:47:55.359Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:47:55.371Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:47:55.373Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"b43b8eab-295c-47c9-a468-fcee2993d04b","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:48:15.586Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2c8f7635-6c4a-4e0b-a9cb-b95c0d83f759","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:48:15.587Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"2c8f7635-6c4a-4e0b-a9cb-b95c0d83f759","userId":"132"}
{"timestamp":"2025-07-21T19:48:15.677Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"2c8f7635-6c4a-4e0b-a9cb-b95c0d83f759","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:48:15.764Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"13a7b7f2-9e9a-4ff2-b038-ceb137ea639c","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:48:15.935Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T19:48:19.386Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127299386-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:48:22.286Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127299386-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127299386-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:48:22.401Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d9d8acfa-4f15-426e-b85a-e60156372b89","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:48:22.448Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:48:22.452Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"d9d8acfa-4f15-426e-b85a-e60156372b89","pageId":"727479083781060","postData":{"message":"To the Visionaries Behind Every Clear Sight\n\nThank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:48:22.457Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:48:22.458Z","level":"INFO","message":"Attempting Facebook photo upload with URL method","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127299386-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732102&Signature=AWDnmGzQqy3AeSA8cwe49TBX4PU%3D"}
{"timestamp":"2025-07-21T19:48:23.412Z","level":"WARN","message":"URL method failed, trying file upload method","environment":"DEVELOPMENT","error":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A-plV8jlDkiX-1M6dMCRK5k"}}}
{"timestamp":"2025-07-21T19:48:23.414Z","level":"INFO","message":"Downloading image from URL for file upload","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127299386-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732102&Signature=AWDnmGzQqy3AeSA8cwe49TBX4PU%3D"}
{"timestamp":"2025-07-21T19:48:26.579Z","level":"ERROR","message":"Both URL and file upload methods failed","environment":"DEVELOPMENT","urlError":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A-plV8jlDkiX-1M6dMCRK5k"}},"fileError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AyPB7aBiz7MJPMSIQH__lPk"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:264:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:48:26.581Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AyPB7aBiz7MJPMSIQH__lPk"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:282:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:48:26.586Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"d9d8acfa-4f15-426e-b85a-e60156372b89","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:255:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AyPB7aBiz7MJPMSIQH__lPk"}},"statusCode":400}
{"timestamp":"2025-07-21T19:48:47.005Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d10bd15e-5b69-42ca-a53e-2c71be6ac05c","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:48:47.009Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"d10bd15e-5b69-42ca-a53e-2c71be6ac05c","userId":"132"}
{"timestamp":"2025-07-21T19:48:47.094Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"d10bd15e-5b69-42ca-a53e-2c71be6ac05c","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:48:47.139Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1e8d67f5-ec00-4b08-b837-52608ed9e1cd","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:48:47.286Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T19:48:53.584Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127333584-event-updates.jpg","mimeType":"image/jpeg","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:48:56.702Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127333584-event-updates.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127333584-event-updates.jpg","etag":"\"325ae4aba7fb885bd7378210cd52c4a6\""}
{"timestamp":"2025-07-21T19:48:56.760Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"656be2de-9b9f-40ba-92a9-ffb3b9b8c12c","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:48:56.800Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:48:56.802Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"656be2de-9b9f-40ba-92a9-ffb3b9b8c12c","pageId":"727479083781060","postData":{"message":"To the Visionaries Behind Every Clear Sight\n\nThank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:48:56.804Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:48:56.805Z","level":"INFO","message":"Attempting Facebook photo upload with URL method","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127333584-event-updates.jpg?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732136&Signature=k5DsthI1puDLBXI0Vc5mxsXcHbw%3D"}
{"timestamp":"2025-07-21T19:49:04.486Z","level":"INFO","message":"Facebook photo upload successful with URL method","environment":"DEVELOPMENT","photoId":"122102644718945391"}
{"timestamp":"2025-07-21T19:49:04.488Z","level":"INFO","message":"Photo uploaded to Facebook successfully","environment":"DEVELOPMENT","pageId":"727479083781060","photoId":"122102644718945391"}
{"timestamp":"2025-07-21T19:49:04.611Z","level":"INFO","message":"Facebook post saved successfully","environment":"DEVELOPMENT","userId":132,"pageId":"727479083781060","postId":1}
{"timestamp":"2025-07-21T19:49:04.612Z","level":"INFO","message":"Facebook post created successfully","environment":"DEVELOPMENT","requestId":"656be2de-9b9f-40ba-92a9-ffb3b9b8c12c","userId":"132","pageId":"727479083781060","postId":"122102644718945391","dbPostId":1}
{"timestamp":"2025-07-21T19:50:17.403Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0cc009b1-6c73-4260-9465-142bd068a2c6","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:50:17.409Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"0cc009b1-6c73-4260-9465-142bd068a2c6","userId":"132"}
{"timestamp":"2025-07-21T19:50:17.466Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1fd5fc8f-0d1b-4502-9ece-790d30eb422b","controller":"accounts","action":"AccountsList"}
{"timestamp":"2025-07-21T19:50:17.501Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"0cc009b1-6c73-4260-9465-142bd068a2c6","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:50:19.806Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0ec3020c-7c50-4663-91b4-529a4a05745a","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:50:19.819Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"146c9ac4-5286-4e84-96aa-29e29f708edc","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:50:19.829Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ec6babcf-6001-43ae-a6d4-6596f408d3fc","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:50:19.833Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"ec6babcf-6001-43ae-a6d4-6596f408d3fc","userId":"132"}
{"timestamp":"2025-07-21T19:50:19.845Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ddb31b98-2f75-4454-95e4-f0e12d0469fa","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:50:19.851Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:50:19.858Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:50:19.940Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:50:19.970Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:50:19.976Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"ddb31b98-2f75-4454-95e4-f0e12d0469fa","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:50:35.369Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"eb9dcaf6-fea4-487d-9029-1263ea6974c8","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:50:35.372Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"eb9dcaf6-fea4-487d-9029-1263ea6974c8","userId":"132"}
{"timestamp":"2025-07-21T19:50:35.435Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"eb9dcaf6-fea4-487d-9029-1263ea6974c8","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:50:35.471Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b04ee120-e99e-43d5-b0b0-4cb2d97bd2c3","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:50:35.606Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T19:50:38.242Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127438241-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:50:41.020Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127438241-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127438241-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:50:41.072Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7d41de94-409d-4da3-9b63-913e92fc1875","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:50:41.105Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:50:41.106Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"7d41de94-409d-4da3-9b63-913e92fc1875","pageId":"727479083781060","postData":{"message":"To the Visionaries Behind Every Clear Sight\n\nThank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:50:41.108Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:50:41.109Z","level":"INFO","message":"Attempting Facebook photo upload with URL method","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127438241-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732241&Signature=s0XPNA6ge7f67U4d25TCBPBibKw%3D"}
{"timestamp":"2025-07-21T19:50:41.965Z","level":"WARN","message":"URL method failed, trying file upload method","environment":"DEVELOPMENT","error":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"Aa399pGzI013Lti7ezvcknY"}}}
{"timestamp":"2025-07-21T19:50:41.968Z","level":"INFO","message":"Downloading image from URL for file upload","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127438241-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732241&Signature=s0XPNA6ge7f67U4d25TCBPBibKw%3D"}
{"timestamp":"2025-07-21T19:50:45.021Z","level":"ERROR","message":"Both URL and file upload methods failed","environment":"DEVELOPMENT","urlError":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"Aa399pGzI013Lti7ezvcknY"}},"fileError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"A12il5AVlWqECtiw4aZLA7c"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:264:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:50:45.023Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"A12il5AVlWqECtiw4aZLA7c"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:282:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:50:45.023Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"7d41de94-409d-4da3-9b63-913e92fc1875","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:255:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"A12il5AVlWqECtiw4aZLA7c"}},"statusCode":400}
{"timestamp":"2025-07-21T19:52:42.611Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e863aa60-54bf-4cf0-8629-8e6fc0a9da11","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:52:42.641Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"38a3f142-332c-4afb-a2a5-06f54b2420da","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:52:42.660Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7708b29a-c58f-489d-bbdd-df3858c6e6d6","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:52:42.672Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:52:42.702Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:52:42.728Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"38a3f142-332c-4afb-a2a5-06f54b2420da","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:52:42.832Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:52:42.928Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d6dd6422-af2e-4966-b1e7-598a54174caf","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:52:42.952Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"d6dd6422-af2e-4966-b1e7-598a54174caf","userId":"132"}
{"timestamp":"2025-07-21T19:52:42.990Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:52:52.833Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"45b5fe6c-4b05-42bb-9aaa-322d1a23d962","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:52:52.835Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"45b5fe6c-4b05-42bb-9aaa-322d1a23d962","userId":"132"}
{"timestamp":"2025-07-21T19:52:52.907Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"45b5fe6c-4b05-42bb-9aaa-322d1a23d962","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:52:52.983Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"77158a0a-01f3-41bb-89cf-bec6c6c375c6","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:52:53.120Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T19:53:06.984Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127586984-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:53:09.753Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127586984-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127586984-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:53:09.800Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bd7c93ea-f26d-4026-8be7-975369fe9c98","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:53:09.838Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:53:09.839Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"bd7c93ea-f26d-4026-8be7-975369fe9c98","pageId":"727479083781060","postData":{"message":"Thank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:53:09.840Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:53:09.841Z","level":"INFO","message":"Attempting Facebook photo upload with URL method","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127586984-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732389&Signature=6TnztNlIjvX2k7cvYNLIxViGmnc%3D"}
{"timestamp":"2025-07-21T19:53:10.693Z","level":"WARN","message":"URL method failed, trying file upload method","environment":"DEVELOPMENT","error":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A2Oaxs9c5CIEkspUEdPaIvi"}}}
{"timestamp":"2025-07-21T19:53:10.694Z","level":"INFO","message":"Downloading image from URL for file upload","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127586984-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732389&Signature=6TnztNlIjvX2k7cvYNLIxViGmnc%3D"}
{"timestamp":"2025-07-21T19:53:13.768Z","level":"ERROR","message":"Both URL and file upload methods failed","environment":"DEVELOPMENT","urlError":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"A2Oaxs9c5CIEkspUEdPaIvi"}},"fileError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AWGF1FKMT5lazPmJXP8opG3"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:264:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:53:13.771Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AWGF1FKMT5lazPmJXP8opG3"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:282:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:53:13.772Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"bd7c93ea-f26d-4026-8be7-975369fe9c98","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:255:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AWGF1FKMT5lazPmJXP8opG3"}},"statusCode":400}
{"timestamp":"2025-07-21T19:56:51.528Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"76484b19-09f3-4f2c-be9e-072b28eee4ac","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:56:51.533Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"76484b19-09f3-4f2c-be9e-072b28eee4ac","userId":"132"}
{"timestamp":"2025-07-21T19:56:51.541Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0429b3dc-5485-4b57-baf5-bc3bd8f6f6e0","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:56:51.548Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3954919b-4c93-4251-9d62-f52c2956189c","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:56:51.566Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"36373fe3-1373-4b53-90c8-7dc2cfad8765","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:56:51.571Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:56:51.578Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:56:51.587Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:56:51.608Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:56:51.621Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"36373fe3-1373-4b53-90c8-7dc2cfad8765","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:56:51.797Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"54412e8d-c70c-4a4b-a47b-ac9d4cc6e784","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:56:51.809Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"05f897bf-f98a-4601-9bc2-aee7829006d1","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:56:51.811Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"05f897bf-f98a-4601-9bc2-aee7829006d1","userId":"132"}
{"timestamp":"2025-07-21T19:56:51.822Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"67cc5627-dede-4951-90b2-2c86b33e8488","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:56:51.835Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8e70ca50-2496-49c2-8863-30c646d260c5","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:56:51.838Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:56:51.855Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:56:51.860Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:56:51.875Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:56:51.878Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"8e70ca50-2496-49c2-8863-30c646d260c5","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:58:48.217Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4b5a28e0-e8fa-4c40-a235-ca9e4086a1ab","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:58:48.223Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6287f340-78c4-4c8d-a662-394684e57d5a","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:58:48.224Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"6287f340-78c4-4c8d-a662-394684e57d5a","userId":"132"}
{"timestamp":"2025-07-21T19:58:48.228Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"cc63d551-bd7f-4cc9-aa07-c9601049b640","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:58:48.237Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b2651262-6967-4414-ab67-abd87926c5d0","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:58:48.252Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:58:48.274Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:58:48.282Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:58:48.285Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:58:48.287Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"b2651262-6967-4414-ab67-abd87926c5d0","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:58:48.541Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"063eb9ab-5c8f-40af-8729-b5a00356bd11","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:58:48.550Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ecce58dd-f188-44b0-a6c2-99fa216e7ec4","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:58:48.552Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"ecce58dd-f188-44b0-a6c2-99fa216e7ec4","userId":"132"}
{"timestamp":"2025-07-21T19:58:48.558Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7058706d-82f3-4cf0-a517-d75ebbf53b42","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:58:48.564Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ce7e79e6-7c15-4460-aff9-f77431fb7907","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:58:48.576Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:58:48.589Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:58:48.592Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:58:48.599Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:58:48.601Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"ce7e79e6-7c15-4460-aff9-f77431fb7907","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:59:21.391Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9425380d-d647-4e59-9290-8163adc29ed9","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-21T19:59:21.402Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d72e47df-487f-478e-ba21-7c8429ff2fe3","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-21T19:59:21.405Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"d72e47df-487f-478e-ba21-7c8429ff2fe3","userId":"132"}
{"timestamp":"2025-07-21T19:59:21.416Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d8d68511-6c78-4607-b2f5-0e1ff0f00802","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-21T19:59:21.423Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e80bcdc6-6342-4b27-af0d-7f3f7d11d964","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-21T19:59:21.433Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:59:21.446Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:59:21.450Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-21T19:59:21.462Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-21T19:59:21.502Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"e80bcdc6-6342-4b27-af0d-7f3f7d11d964","userId":"132","accountCount":0}
{"timestamp":"2025-07-21T19:59:34.607Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9db854d7-aae3-4d4c-b346-f890d7bbb3c3","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:59:34.608Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"9db854d7-aae3-4d4c-b346-f890d7bbb3c3","userId":"132"}
{"timestamp":"2025-07-21T19:59:34.682Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"9db854d7-aae3-4d4c-b346-f890d7bbb3c3","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:59:34.819Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"32814ce4-6e0c-40bd-8338-bcc7bda1a30e","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:59:34.957Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T19:59:42.725Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127982725-Screenshot 2025-07-04 110601.png","mimeType":"image/png","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T19:59:45.480Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753127982725-Screenshot 2025-07-04 110601.png","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127982725-Screenshot%202025-07-04%20110601.png","etag":"\"d41d8cd98f00b204e9800998ecf8427e\""}
{"timestamp":"2025-07-21T19:59:45.528Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"350aee9b-dfad-4928-917f-1005ef3bef32","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T19:59:45.564Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T19:59:45.567Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"350aee9b-dfad-4928-917f-1005ef3bef32","pageId":"727479083781060","postData":{"message":"Thank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T19:59:45.571Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T19:59:45.572Z","level":"INFO","message":"Attempting Facebook photo upload with URL method","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127982725-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732785&Signature=jTz1guMMLWJUrIrNYYEaOA4ppEo%3D"}
{"timestamp":"2025-07-21T19:59:46.311Z","level":"WARN","message":"URL method failed, trying file upload method","environment":"DEVELOPMENT","error":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"AT1c6CoK07SVV2xbNSFGikD"}}}
{"timestamp":"2025-07-21T19:59:46.312Z","level":"INFO","message":"Downloading image from URL for file upload","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753127982725-Screenshot%202025-07-04%20110601.png?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732785&Signature=jTz1guMMLWJUrIrNYYEaOA4ppEo%3D"}
{"timestamp":"2025-07-21T19:59:49.348Z","level":"ERROR","message":"Both URL and file upload methods failed","environment":"DEVELOPMENT","urlError":{"error":{"message":"Invalid parameter","type":"OAuthException","code":100,"error_subcode":1366046,"is_transient":false,"error_user_title":"Can't Read Files","error_user_msg":"Your photos couldn't be uploaded. Photos should be less than 4 MB and saved as JPG, PNG, GIF, TIFF, HEIF or WebP files.","fbtrace_id":"AT1c6CoK07SVV2xbNSFGikD"}},"fileError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AEFG4zE98iSgYP02nvDuTZH"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:264:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:59:49.350Z","level":"ERROR","message":"Error uploading photo to Facebook:","environment":"DEVELOPMENT","pageId":"727479083781060","error":"Request failed with status code 400","response":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AEFG4zE98iSgYP02nvDuTZH"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:282:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)"}
{"timestamp":"2025-07-21T19:59:49.352Z","level":"ERROR","message":"Error in Facebook createPost","environment":"DEVELOPMENT","requestId":"350aee9b-dfad-4928-917f-1005ef3bef32","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async FacebookService.uploadPhoto (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\facebook.service.js:255:24)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\facebook.controller.js:471:32)","facebookError":{"error":{"message":"(#324) Requires upload file","type":"OAuthException","code":324,"fbtrace_id":"AEFG4zE98iSgYP02nvDuTZH"}},"statusCode":400}
{"timestamp":"2025-07-21T19:59:54.747Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bf9d0080-6a61-4e38-9467-20d39383d17d","controller":"business","action":"businessList","userId":"132"}
{"timestamp":"2025-07-21T19:59:54.748Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"bf9d0080-6a61-4e38-9467-20d39383d17d","userId":"132"}
{"timestamp":"2025-07-21T19:59:54.818Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"bf9d0080-6a61-4e38-9467-20d39383d17d","userId":"132","businessCount":1}
{"timestamp":"2025-07-21T19:59:54.869Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e040ff7a-0c5c-4c84-91b2-66e60a97f3e6","controller":"ManageAssets","action":"getAssets"}
{"timestamp":"2025-07-21T19:59:55.001Z","level":"INFO","message":"Asset thumbnail debug","environment":"DEVELOPMENT","assetId":6,"fileName":"1728bb1e-ef30-4ab1-82ef-b4c36f68cc94.png","thumbnail_s3_key":"public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_s3_url":"https://gmb-social-assets.s3.us-east-1.amazonaws.com/public-thumbnails/62/1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_file_name":"1689c240-ec2b-49de-8481-cd01ec4b56d9.jpg","thumbnail_size":14357}
{"timestamp":"2025-07-21T20:00:01.079Z","level":"INFO","message":"Uploading post image to S3 temp folder","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753128001079-event-updates.jpg","mimeType":"image/jpeg","userId":132,"bucketName":"gmb-social-assets"}
{"timestamp":"2025-07-21T20:00:04.203Z","level":"INFO","message":"Post image uploaded successfully to S3","environment":"DEVELOPMENT","s3Key":"temp/post-images/132/1753128001079-event-updates.jpg","location":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753128001079-event-updates.jpg","etag":"\"325ae4aba7fb885bd7378210cd52c4a6\""}
{"timestamp":"2025-07-21T20:00:04.260Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f664545c-dbab-40a6-9248-f470078f95a9","controller":"facebook","action":"createPost"}
{"timestamp":"2025-07-21T20:00:04.293Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-21T20:00:04.295Z","level":"INFO","message":"Sending post data to Facebook","environment":"DEVELOPMENT","requestId":"f664545c-dbab-40a6-9248-f470078f95a9","pageId":"727479083781060","postData":{"message":"Thank You. On this global day of gratitude, we salute the doctors who bring light to lives through their care and commitment. Happy International Doctors’ Day from Sri Eye Care. 👁️🌍"},"hasMedia":true}
{"timestamp":"2025-07-21T20:00:04.297Z","level":"INFO","message":"Uploading photo to Facebook","environment":"DEVELOPMENT","pageId":"727479083781060","hasUrl":true,"hasCaption":true}
{"timestamp":"2025-07-21T20:00:04.299Z","level":"INFO","message":"Attempting Facebook photo upload with URL method","environment":"DEVELOPMENT","url":"https://gmb-social-assets.s3.amazonaws.com/temp/post-images/132/1753128001079-event-updates.jpg?AWSAccessKeyId=AKIA6GBMBOAGSGR5Q7DQ&Expires=1753732804&Signature=e2fm8U1QFu%2BXmU5J2%2BIbvu%2BclDU%3D"}
{"timestamp":"2025-07-21T20:00:08.641Z","level":"INFO","message":"Facebook photo upload successful with URL method","environment":"DEVELOPMENT","photoId":"122102649296945391"}
{"timestamp":"2025-07-21T20:00:08.643Z","level":"INFO","message":"Photo uploaded to Facebook successfully","environment":"DEVELOPMENT","pageId":"727479083781060","photoId":"122102649296945391"}
{"timestamp":"2025-07-21T20:00:08.677Z","level":"INFO","message":"Facebook post saved successfully","environment":"DEVELOPMENT","userId":132,"pageId":"727479083781060","postId":2}
{"timestamp":"2025-07-21T20:00:08.678Z","level":"INFO","message":"Facebook post created successfully","environment":"DEVELOPMENT","requestId":"f664545c-dbab-40a6-9248-f470078f95a9","userId":"132","pageId":"727479083781060","postId":"122102649296945391","dbPostId":2}
