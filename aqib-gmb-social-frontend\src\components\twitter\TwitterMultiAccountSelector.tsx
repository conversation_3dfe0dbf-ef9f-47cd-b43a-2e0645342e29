import React, { useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>box,
  Avatar,
  Chip,
  <PERSON>ton,
  <PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON><PERSON><PERSON>,
  Divider,
} from "@mui/material";
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Twitter as TwitterIcon,
  Verified as VerifiedIcon,
} from "@mui/icons-material";
import { ITwitterSelectedAccount } from "../../interfaces/request/ITwitterCreatePost";
import { ITwitterAccountData } from "../../interfaces/response/ITwitterCreatePostResponse";

interface TwitterMultiAccountSelectorProps {
  accounts: ITwitterAccountData[];
  selectedAccounts: ITwitterSelectedAccount[];
  onSelectionChange: (accounts: ITwitterSelectedAccount[]) => void;
  loading?: boolean;
}

const TwitterMultiAccountSelector: React.FC<TwitterMultiAccountSelectorProps> = ({
  accounts,
  selectedAccounts,
  onSelectionChange,
  loading = false,
}) => {
  const [expanded, setExpanded] = useState(true);

  const handleAccountToggle = (account: ITwitterAccountData) => {
    const isSelected = selectedAccounts.some(
      (selected) => selected.accountId === account.account_id
    );

    let newSelection: ITwitterSelectedAccount[];

    if (isSelected) {
      // Remove from selection
      newSelection = selectedAccounts.filter(
        (selected) => selected.accountId !== account.account_id
      );
    } else {
      // Add to selection
      const newAccount: ITwitterSelectedAccount = {
        accountId: account.account_id,
        accountName: account.account_name,
        accountUsername: account.account_username,
        accountPictureUrl: account.account_picture_url,
        isVerified: account.is_verified,
        followersCount: account.followers_count,
        followingCount: account.following_count,
        tweetCount: account.tweet_count,
      };
      newSelection = [...selectedAccounts, newAccount];
    }

    onSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedAccounts.length === accounts.length) {
      // Deselect all
      onSelectionChange([]);
    } else {
      // Select all
      const allAccounts: ITwitterSelectedAccount[] = accounts.map((account) => ({
        accountId: account.account_id,
        accountName: account.account_name,
        accountUsername: account.account_username,
        accountPictureUrl: account.account_picture_url,
        isVerified: account.is_verified,
        followersCount: account.followers_count,
        followingCount: account.following_count,
        tweetCount: account.tweet_count,
      }));
      onSelectionChange(allAccounts);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading Twitter accounts...</Typography>
        </CardContent>
      </Card>
    );
  }

  if (!accounts || accounts.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography color="textSecondary">
            No Twitter accounts found. Please connect your Twitter account first.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent sx={{ pb: 1 }}>
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2 }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <TwitterIcon sx={{ mr: 1, color: "#1DA1F2" }} />
            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
              Twitter Accounts ({accounts.length})
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            {accounts.length > 1 && (
              <Button
                size="small"
                onClick={handleSelectAll}
                variant="outlined"
                sx={{ minWidth: "auto", px: 2 }}
              >
                {selectedAccounts.length === accounts.length ? "Deselect All" : "Select All"}
              </Button>
            )}
            <IconButton
              size="small"
              onClick={() => setExpanded(!expanded)}
              sx={{ ml: 1 }}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        {/* Selected accounts summary */}
        {selectedAccounts.length > 0 && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="textSecondary" sx={{ mb: 1, display: "block" }}>
              Selected ({selectedAccounts.length}):
            </Typography>
            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
              {selectedAccounts.map((account) => (
                <Chip
                  key={account.accountId}
                  label={`@${account.accountUsername}`}
                  size="small"
                  avatar={
                    <Avatar
                      src={account.accountPictureUrl}
                      sx={{ width: 20, height: 20 }}
                    />
                  }
                  onDelete={() => {
                    const newSelection = selectedAccounts.filter(
                      (selected) => selected.accountId !== account.accountId
                    );
                    onSelectionChange(newSelection);
                  }}
                  sx={{
                    backgroundColor: "#e3f2fd",
                    color: "#1976d2",
                    "& .MuiChip-deleteIcon": {
                      color: "#1976d2",
                    },
                  }}
                />
              ))}
            </Box>
          </Box>
        )}

        <Collapse in={expanded}>
          <Box sx={{ maxHeight: 300, overflowY: "auto" }}>
            {accounts.map((account, index) => {
              const isSelected = selectedAccounts.some(
                (selected) => selected.accountId === account.account_id
              );

              return (
                <Box key={account.account_id}>
                  {index > 0 && <Divider sx={{ my: 1 }} />}
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      p: 1,
                      borderRadius: 1,
                      cursor: "pointer",
                      backgroundColor: isSelected ? "#e3f2fd" : "transparent",
                      "&:hover": {
                        backgroundColor: isSelected ? "#e3f2fd" : "#f5f5f5",
                      },
                    }}
                    onClick={() => handleAccountToggle(account)}
                  >
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleAccountToggle(account)}
                      sx={{ mr: 2 }}
                    />
                    <Avatar
                      src={account.account_picture_url}
                      sx={{ width: 40, height: 40, mr: 2 }}
                    >
                      <TwitterIcon />
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          {account.account_name}
                        </Typography>
                        {account.is_verified && (
                          <VerifiedIcon
                            sx={{ ml: 0.5, fontSize: 16, color: "#1DA1F2" }}
                          />
                        )}
                      </Box>
                      <Typography variant="caption" color="textSecondary">
                        @{account.account_username}
                      </Typography>
                      <Box sx={{ display: "flex", gap: 2, mt: 0.5 }}>
                        <Typography variant="caption" color="textSecondary">
                          {formatNumber(account.followers_count)} followers
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {formatNumber(account.tweet_count)} tweets
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              );
            })}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default TwitterMultiAccountSelector;
