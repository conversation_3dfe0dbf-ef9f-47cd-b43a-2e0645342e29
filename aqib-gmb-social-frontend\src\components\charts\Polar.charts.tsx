import React from "react";
import { Bar, PolarArea } from "react-chartjs-2";
import { Chart, registerables } from "chart.js";
Chart.register(...registerables);

export const PolarChart = (props: { chartData: any }) => {
  const data = {
    labels: ["Scheduled", "Posted", "Failed"],
    datasets: [
      {
        label: "Jobs",
        data: [200, 106, 94],
        backgroundColor: [
          "rgb(255, 205, 86)",
          "rgb(75, 192, 124)",
          "rgb(255, 99, 132)",
        ],
      },
    ],
  };

  return (
    <div className="chart-container">
      <PolarArea data={data} options={{}} />
    </div>
  );
};
