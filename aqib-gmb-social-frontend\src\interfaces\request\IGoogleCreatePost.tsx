export interface IGoogleCreatePost {
  languageCode: string;
  summary: string;
  callToAction?: CallToAction | null;
  event?: Event | null;
  offer?: Offer | null;
  media: Medum[];
  topicType: string;
}

export interface Event {
  title: string;
  schedule: Schedule | null;
}

export interface CallToAction {
  actionType: string;
  url: string;
}

export interface Offer {
  couponCode: string;
  redeemOnlineUrl: string;
  termsConditions: string;
}

export interface Schedule {
  //   startDate: StartDate;
  startTime: string;
  //   endDate: EndDate;
  endTime: string;
}

// export interface StartDate {
//   year: number;
//   month: number;
//   day: number;
// }

// export interface StartTime {
//   hours: number;
//   minutes: number;
//   seconds: number;
//   nanos: number;
// }

// export interface EndDate {
//   year: number;
//   month: number;
//   day: number;
// }

// export interface EndTime {
//   hours: number;
//   minutes: number;
//   seconds: number;
//   nanos: number;
// }

export interface Medum {
  mediaFormat: string;
  sourceUrl: string;
}
