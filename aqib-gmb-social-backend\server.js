"use strict";

// Load environment configuration based on NODE_ENV before loading app
const environment = (process.env.NODE_ENV || "development").trim();
const envFile = `.env.${environment}`;

console.log(`Loading environment configuration from: ${envFile}`);
require("dotenv").config({ path: envFile });

const app = require("./app");

// Use IIS-injected port or fallback
const port = process.env.PORT || process.env.APP_PORT || 3000;

const server = app.listen(port, () =>
  console.log(`IIS-hosted server is listening on port ${port}.`)
);

// Optional: increase request timeout
server.timeout = 20000;
