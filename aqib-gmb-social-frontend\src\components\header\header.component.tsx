import React from "react";
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import Button from "@mui/material/Button";
import { useDispatch, useSelector } from "react-redux";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { logOut } from "../../actions/auth.actions";
import UserAvatar from "../userAvatar/userAvatar.component";
import LogoutIcon from "@mui/icons-material/Logout";

// Image import
import profileImage from "../../assets/common/profile.png";

function HeaderComponent() {
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const dispatch = useDispatch();
  const logoutUser = () => dispatch<any>(logOut());
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const openSubMenu = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box className="display-contents">
      <Box className="commonTopBarR">
        {/* <Box className="notification">
          <span className="notificationIcon">
            <IconButton aria-label="settings" className="iconBtn">
              <SettingsOutlinedIcon />
            </IconButton>
          </span>
          <span className="notificationValue">19</span>
        </Box> */}
        {/* <Box className="notification">
          <span className="notificationIcon">
            <IconButton
              aria-label="settings"
              className="iconBtn"
              onClick={() => logoutUser()}
            >
              <LogoutIcon />
            </IconButton>
          </span>
        </Box> */}
        <Box className="profileName">
          <Box className="">
            <span>Hello</span>,&nbsp;
            <span className="userName">{userInfo && userInfo.name}</span>
          </Box>
        </Box>
        <Box>
          {userInfo && (
            <>
              {/* <Button
                id="basic-button"
                aria-controls={openSubMenu ? "basic-menu" : undefined}
                aria-haspopup="true"
                aria-expanded={openSubMenu ? "true" : undefined}
                onClick={handleClick}
              > */}
              <Box sx={{ paddingLeft: "8px" }}>
                <UserAvatar fullname={userInfo.name} />
              </Box>

              {/* <img
              alt="MyLocoBiz - Profile Photo"
              className="width100 profilePic"
              src={profileImage}
            /> */}
              {/* </Button> */}
              {/* <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={openSubMenu}
                onClose={handleClose}
                MenuListProps={{
                  "aria-labelledby": "basic-button",
                }}
              > */}
              {/* <MenuItem onClick={handleClose}>Profile</MenuItem>
                <MenuItem onClick={handleClose}>My account</MenuItem> */}
              {/* <MenuItem onClick={() => logoutUser()}>Logout</MenuItem>
              </Menu> */}
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
}

export default HeaderComponent;
