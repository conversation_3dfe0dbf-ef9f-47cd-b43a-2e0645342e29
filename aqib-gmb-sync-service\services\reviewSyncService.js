const axios = require("axios");
const database = require("../config/database");
const reviewSyncModel = require("../models/reviewSyncModel");
const logger = require("../utils/logger");

class ReviewSyncService {
  constructor() {
    this.baseURL = process.env.BACKEND_API_URL || "http://localhost:3000";
    this.timeout = parseInt(process.env.BACKEND_API_TIMEOUT) || 30000;
    this.serviceUserId = process.env.SERVICE_USER_ID || "52";
    this.serviceAuthToken = process.env.SERVICE_AUTH_TOKEN;
    this.batchSize = parseInt(process.env.BATCH_SIZE) || 50;

    // Statistics tracking
    this.stats = {
      lastRunTime: null,
      totalTokensProcessed: 0,
      totalLocationsProcessed: 0,
      totalReviewsSynced: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      errors: [],
      isRunning: false,
      startTime: null,
      endTime: null,
    };

    // Create axios instance with default config
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        "Content-Type": "application/json",
        "User-Agent": "GMB-ReviewSync-Service/1.0",
        "authentication-token": this.serviceAuthToken,
      },
    });

    // Add request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.logAutoReply("REVIEW_SYNC_API_REQUEST", {
          method: config.method?.toUpperCase(),
          url: config.url,
          headers: this.sanitizeHeaders(config.headers),
        });
        return config;
      },
      (error) => {
        logger.error("Review sync API request error:", error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.apiClient.interceptors.response.use(
      (response) => {
        logger.logAutoReply("REVIEW_SYNC_API_RESPONSE", {
          status: response.status,
          url: response.config.url,
          success: true,
        });
        return response;
      },
      (error) => {
        logger.error("Review sync API response error:", {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Sanitize headers for logging (remove sensitive data)
   */
  sanitizeHeaders(headers) {
    const sanitized = { ...headers };
    if (sanitized.Authorization) {
      sanitized.Authorization = "Bearer ***";
    }
    if (sanitized["authentication-token"]) {
      sanitized["authentication-token"] = "***";
    }
    return sanitized;
  }

  /**
   * Main method to sync all reviews for all OAuth tokens
   */
  async syncAllReviews() {
    if (this.stats.isRunning) {
      logger.logAutoReply("REVIEW_SYNC_ALREADY_RUNNING", {
        message: "Review sync is already running, skipping this execution",
      });
      return this.stats;
    }

    this.stats.isRunning = true;
    this.stats.startTime = new Date();
    this.stats.errors = [];
    this.stats.totalTokensProcessed = 0;
    this.stats.totalLocationsProcessed = 0;
    this.stats.totalReviewsSynced = 0;
    this.stats.successfulSyncs = 0;
    this.stats.failedSyncs = 0;

    try {
      logger.logAutoReply("REVIEW_SYNC_START", {
        message: "Starting review sync for all OAuth tokens",
        timestamp: this.stats.startTime.toISOString(),
      });

      // Ensure sync log table exists
      await reviewSyncModel.createSyncLogTable();

      // Get all active OAuth tokens
      const oauthTokens = await reviewSyncModel.getAllOAuthTokens();

      if (!oauthTokens || oauthTokens.length === 0) {
        logger.logAutoReply("REVIEW_SYNC_NO_TOKENS", {
          message: "No OAuth tokens found for review sync",
        });
        return this.stats;
      }

      logger.logAutoReply("REVIEW_SYNC_TOKENS_FOUND", {
        tokenCount: oauthTokens.length,
        message: "Found OAuth tokens for review sync",
      });

      // Process each OAuth token
      for (const token of oauthTokens) {
        await this.processOAuthToken(token);
        this.stats.totalTokensProcessed++;
      }

      this.stats.endTime = new Date();
      this.stats.lastRunTime = this.stats.endTime;

      logger.logAutoReply("REVIEW_SYNC_COMPLETE", {
        message: "Review sync completed successfully",
        stats: this.getStats(),
        duration: this.stats.endTime - this.stats.startTime,
      });
    } catch (error) {
      this.stats.errors.push({
        error: error.message,
        timestamp: new Date().toISOString(),
        context: "syncAllReviews",
      });

      logger.error("Error in review sync process:", {
        error: error.message,
        stack: error.stack,
        stats: this.getStats(),
      });

      throw error;
    } finally {
      this.stats.isRunning = false;
    }

    return this.stats;
  }

  /**
   * Process a single OAuth token and sync reviews for all its locations
   */
  async processOAuthToken(token) {
    try {
      logger.logAutoReply("PROCESS_OAUTH_TOKEN_START", {
        accountId: token.gmbAccountId,
        userId: token.userId,
        message: "Starting to process OAuth token",
      });

      // Get all locations for this account
      const locations = await reviewSyncModel.getLocationsForAccount(
        token.gmbAccountId
      );

      if (!locations || locations.length === 0) {
        logger.logAutoReply("NO_LOCATIONS_FOR_ACCOUNT", {
          accountId: token.gmbAccountId,
          message: "No locations found for account",
        });
        return;
      }

      // Process each location
      for (const location of locations) {
        await this.syncReviewsForLocation(token, location);
        this.stats.totalLocationsProcessed++;
      }

      logger.logAutoReply("PROCESS_OAUTH_TOKEN_COMPLETE", {
        accountId: token.gmbAccountId,
        locationsProcessed: locations.length,
        message: "Completed processing OAuth token",
      });
    } catch (error) {
      this.stats.errors.push({
        error: error.message,
        timestamp: new Date().toISOString(),
        context: "processOAuthToken",
        accountId: token.gmbAccountId,
      });

      logger.error("Error processing OAuth token:", {
        accountId: token.gmbAccountId,
        error: error.message,
        stack: error.stack,
      });

      // Continue with next token instead of failing completely
    }
  }

  /**
   * Sync reviews for a specific location using the existing backend API
   */
  async syncReviewsForLocation(token, location) {
    const startTime = Date.now();
    let syncData = {
      accountId: token.gmbAccountId,
      locationId: location.gmbLocationId,
      userId: token.userId,
      status: "failed",
      reviewsCount: 0,
      errorMessage: null,
      duration: null,
    };

    try {
      logger.logAutoReply("SYNC_LOCATION_START", {
        accountId: token.gmbAccountId,
        locationId: location.gmbLocationId,
        locationName: location.gmbLocationName,
        message: "Starting review sync for location",
      });

      // Call the existing refresh reviews API
      const headers = {
        "x-gmb-account-id": token.gmbAccountId,
        "x-gmb-location-id": location.gmbLocationId,
        "authentication-token": this.serviceAuthToken,
      };

      const response = await this.apiClient.get(
        "/v1/gmb-reviews/reviews-list",
        {
          headers,
        }
      );

      const endTime = Date.now();
      syncData.duration = endTime - startTime;

      if (response.data && response.data.success) {
        this.stats.successfulSyncs++;
        const reviewCount = response.data.data ? response.data.data.length : 0;
        this.stats.totalReviewsSynced += reviewCount;

        syncData.status = "success";
        syncData.reviewsCount = reviewCount;

        logger.logAutoReply("SYNC_LOCATION_SUCCESS", {
          accountId: token.gmbAccountId,
          locationId: location.gmbLocationId,
          locationName: location.gmbLocationName,
          reviewCount: reviewCount,
          duration: syncData.duration,
          message: "Successfully synced reviews for location",
        });
      } else {
        this.stats.failedSyncs++;
        const errorMsg = response.data?.message || "Unknown error";
        syncData.errorMessage = errorMsg;

        this.stats.errors.push({
          error: errorMsg,
          timestamp: new Date().toISOString(),
          context: "syncReviewsForLocation",
          accountId: token.gmbAccountId,
          locationId: location.gmbLocationId,
        });

        logger.logAutoReply("SYNC_LOCATION_FAILED", {
          accountId: token.gmbAccountId,
          locationId: location.gmbLocationId,
          locationName: location.gmbLocationName,
          error: errorMsg,
          duration: syncData.duration,
          message: "Failed to sync reviews for location",
        });
      }
    } catch (error) {
      this.stats.failedSyncs++;
      const endTime = Date.now();
      syncData.duration = endTime - startTime;
      syncData.errorMessage = error.message;

      this.stats.errors.push({
        error: error.message,
        timestamp: new Date().toISOString(),
        context: "syncReviewsForLocation",
        accountId: token.gmbAccountId,
        locationId: location.gmbLocationId,
      });

      logger.error("Error syncing reviews for location:", {
        accountId: token.gmbAccountId,
        locationId: location.gmbLocationId,
        locationName: location.gmbLocationName,
        error: error.message,
        duration: syncData.duration,
        stack: error.stack,
      });

      // Continue with next location instead of failing completely
    } finally {
      // Log the sync operation to database
      await reviewSyncModel.logSyncOperation(syncData);
    }
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      ...this.stats,
      duration:
        this.stats.startTime && this.stats.endTime
          ? this.stats.endTime - this.stats.startTime
          : null,
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.stats = {
      lastRunTime: this.stats.lastRunTime,
      totalTokensProcessed: 0,
      totalLocationsProcessed: 0,
      totalReviewsSynced: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      errors: [],
      isRunning: false,
      startTime: null,
      endTime: null,
    };
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      baseURL: this.baseURL,
      timeout: this.timeout,
      serviceUserId: this.serviceUserId,
      hasAuthToken: !!this.serviceAuthToken,
      isConfigured: !!(
        this.baseURL &&
        this.serviceUserId &&
        this.serviceAuthToken
      ),
      stats: this.getStats(),
    };
  }
}

module.exports = new ReviewSyncService();
