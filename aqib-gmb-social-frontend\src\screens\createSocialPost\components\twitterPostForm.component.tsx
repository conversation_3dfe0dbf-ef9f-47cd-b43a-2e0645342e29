import React, { useState } from "react";
import {
  Box,
  TextField,
  FormControlLabel,
  Switch,
  Button,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  InputAdornment,
  Alert,
} from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import TwitterIcon from "@mui/icons-material/Twitter";
import VerifiedIcon from "@mui/icons-material/Verified";
import TwitterMultiAccountSelector from "../../../components/twitter/TwitterMultiAccountSelector";
import {
  ITwitterCreatePost,
  ITwitterSelectedAccount,
  ITwitterPostFormProps,
} from "../../../interfaces/request/ITwitterCreatePost";

const TWITTER_CHARACTER_LIMIT = 280;

const TwitterPostForm: React.FC<ITwitterPostFormProps> = ({
  formData,
  onFormChange,
  uploadedImages,
  onImageUpload,
  onGalleryOpen,
  onImageRemove,
  errors,
  twitterAccounts,
  loading = false,
  selectedAccounts = [],
  onSelectedAccountsChange,
  onSubmit,
  isTwitterConnected = true,
}) => {
  const [scheduleForLater, setScheduleForLater] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Dayjs | null>(
    dayjs().add(1, "hour")
  );

  const handleMultiAccountsChange = (accounts: ITwitterSelectedAccount[]) => {
    if (onSelectedAccountsChange) {
      onSelectedAccountsChange(accounts);
    }
    // Update accountIds in formData for compatibility
    onFormChange({
      ...formData,
      accountIds: accounts.map((a) => a.accountId),
    });
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newText = event.target.value;
    if (newText.length <= TWITTER_CHARACTER_LIMIT) {
      onFormChange({
        ...formData,
        text: newText,
      });
    }
  };

  const handleScheduleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setScheduleForLater(event.target.checked);
    if (!event.target.checked) {
      onFormChange({
        ...formData,
        scheduledPublishTime: undefined,
      });
    }
  };

  const handleScheduledDateChange = (newDate: Dayjs | null) => {
    setScheduledDate(newDate);
    if (newDate && scheduleForLater) {
      onFormChange({
        ...formData,
        scheduledPublishTime: newDate.toISOString(),
      });
    }
  };

  const getCharacterCount = () => {
    return formData.text?.length || 0;
  };

  const getCharacterCountColor = () => {
    const count = getCharacterCount();
    if (count > TWITTER_CHARACTER_LIMIT * 0.9) return "error";
    if (count > TWITTER_CHARACTER_LIMIT * 0.8) return "warning";
    return "primary";
  };

  const isFormValid = () => {
    return (
      formData.text &&
      formData.text.trim().length > 0 &&
      formData.text.length <= TWITTER_CHARACTER_LIMIT &&
      selectedAccounts.length > 0
    );
  };

  if (!isTwitterConnected) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            Connect Your Twitter Account
          </Typography>
          <Typography variant="body2" sx={{ mb: 2 }}>
            To create Twitter posts, you need to connect your Twitter account first.
          </Typography>
          <Button
            variant="contained"
            startIcon={<TwitterIcon />}
            sx={{
              backgroundColor: "#1DA1F2",
              "&:hover": { backgroundColor: "#1a91da" },
            }}
          >
            Connect Twitter Account
          </Button>
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 2 }}>
      {/* Account Selection */}
      <Box className="commonFormPart" sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ mb: 2, display: "flex", alignItems: "center" }}>
          <TwitterIcon sx={{ mr: 1, color: "#1DA1F2" }} />
          Select Twitter Accounts
        </Typography>
        <TwitterMultiAccountSelector
          accounts={twitterAccounts}
          selectedAccounts={selectedAccounts}
          onSelectionChange={handleMultiAccountsChange}
          loading={loading}
        />
        {errors.accountIds && (
          <Typography variant="caption" color="error" sx={{ mt: 1, display: "block" }}>
            {errors.accountIds}
          </Typography>
        )}
      </Box>

      {/* Tweet Text */}
      <Box className="commonFormPart" sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Tweet Text
        </Typography>
        <TextField
          fullWidth
          multiline
          rows={4}
          placeholder="What's happening?"
          value={formData.text || ""}
          onChange={handleTextChange}
          error={!!errors.text}
          helperText={errors.text}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <Typography
                  variant="caption"
                  color={getCharacterCountColor()}
                  sx={{ fontWeight: "bold" }}
                >
                  {getCharacterCount()}/{TWITTER_CHARACTER_LIMIT}
                </Typography>
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: getCharacterCount() > TWITTER_CHARACTER_LIMIT ? "error.main" : "grey.300",
              },
            },
          }}
        />
        {getCharacterCount() > TWITTER_CHARACTER_LIMIT && (
          <Typography variant="caption" color="error" sx={{ mt: 1, display: "block" }}>
            Tweet exceeds character limit by {getCharacterCount() - TWITTER_CHARACTER_LIMIT} characters
          </Typography>
        )}
      </Box>

      {/* Media Upload */}
      <Box className="commonFormPart" sx={{ mb: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Media
        </Typography>
        <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
          <Button
            variant="outlined"
            startIcon={<CloudUploadIcon />}
            onClick={onImageUpload}
            size="small"
          >
            Upload from Computer
          </Button>
          <Button
            variant="outlined"
            startIcon={<PhotoLibraryIcon />}
            onClick={onGalleryOpen}
            size="small"
          >
            Gallery Picker
          </Button>
        </Box>

        {uploadedImages.length > 0 && (
          <Grid container spacing={2}>
            {uploadedImages.map((image, index) => (
              <Grid item xs={6} sm={4} md={3} key={index}>
                <Card sx={{ position: "relative" }}>
                  <CardContent sx={{ p: 1, "&:last-child": { pb: 1 } }}>
                    <img
                      src={image.url || image.preview}
                      alt={`Upload ${index + 1}`}
                      style={{
                        width: "100%",
                        height: "80px",
                        objectFit: "cover",
                        borderRadius: "4px",
                      }}
                    />
                    <IconButton
                      size="small"
                      onClick={() => onImageRemove(index)}
                      sx={{
                        position: "absolute",
                        top: 4,
                        right: 4,
                        backgroundColor: "rgba(0,0,0,0.5)",
                        color: "white",
                        "&:hover": { backgroundColor: "rgba(0,0,0,0.7)" },
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Schedule Options */}
      <Box className="commonFormPart" sx={{ mb: 2 }}>
        <FormControlLabel
          control={
            <Switch
              checked={scheduleForLater}
              onChange={handleScheduleToggle}
              color="primary"
            />
          }
          label="Schedule for later"
        />

        {scheduleForLater && (
          <Box sx={{ mt: 2 }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                label="Scheduled Date & Time"
                value={scheduledDate}
                onChange={handleScheduledDateChange}
                minDateTime={dayjs()}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.scheduledPublishTime,
                    helperText: errors.scheduledPublishTime,
                  },
                }}
              />
            </LocalizationProvider>
          </Box>
        )}
      </Box>

      {/* Submit Button */}
      <Box sx={{ mt: 3 }}>
        <Button
          variant="contained"
          onClick={onSubmit}
          disabled={!isFormValid() || loading}
          startIcon={<TwitterIcon />}
          sx={{
            backgroundColor: "#1DA1F2",
            "&:hover": { backgroundColor: "#1a91da" },
            "&:disabled": { backgroundColor: "grey.300" },
            minHeight: "50px",
            px: 3,
          }}
        >
          {loading ? "Creating Tweet..." : scheduleForLater ? "Schedule Tweet" : "Post Tweet"}
        </Button>
      </Box>
    </Box>
  );
};

export default TwitterPostForm;
