import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import {
  REVIEW_SETTINGS_TEMPLATES,
  REVIEW_SETTINGS_TEMPLATE_BY_ID,
  REVIEW_SETTINGS_MAP_TEMPLATE,
  REVIEW_SETTINGS_AUTO_REPLY,
  REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE,
  REVIEW_SETTINGS_GENERATE_AI_TEXT,
} from "../../constants/endPoints.constant";

// Interfaces
export interface IReplyTemplate {
  id?: number;
  created_by: number;
  star_rating: number;
  template_name: string;
  template_content: string;
  is_default: boolean;
  business_id?: number;
  business_template_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ICreateReplyTemplateRequest {
  starRating: number;
  templateName: string;
  templateContent: string;
  isDefault: boolean;
  businessId?: number;
}

export interface IUpdateReplyTemplateRequest {
  starRating: number;
  templateName: string;
  templateContent: string;
  isDefault: boolean;
}

export interface IAutoReplySettings {
  id?: number;
  business_id: number;
  account_id?: string;
  location_id?: string;
  is_enabled: boolean;
  enabled_star_ratings: number[];
  delay_minutes: number;
  only_business_hours: boolean;
  enable_ai_auto_reply: boolean;
  business_hours_start: string;
  business_hours_end: string;
  created_at?: string;
  updated_at?: string;
}

export interface IMapTemplateToBusinessesRequest {
  businessIds: number[];
}

export interface IReplyTemplatesResponse {
  message: string;
  data: IReplyTemplate[];
}

export interface IAutoReplySettingsResponse {
  message: string;
  data: IAutoReplySettings | null;
}

export interface IApiResponse {
  message: string;
  data?: any;
}

export interface IGenerateAITextRequest {
  starRating: number;
}

export interface IGenerateAITextResponse {
  message: string;
  data: {
    generatedText: string;
    starRating: number;
    genericComment: string;
  };
}

class ReviewSettingsService {
  _httpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  // Get all reply templates for a user
  getReplyTemplates = async (
    userId: number,
    businessId?: number,
    accountId?: string,
    locationId?: string
  ): Promise<IReplyTemplatesResponse> => {
    let url = REVIEW_SETTINGS_TEMPLATES(userId);
    const params = new URLSearchParams();

    // If locationId is provided, use only locationId (it's unique)
    if (locationId) {
      params.append("locationId", locationId);
    } else if (accountId) {
      // If only accountId is provided
      params.append("accountId", accountId);
    } else if (businessId) {
      // If only businessId is provided
      params.append("businessId", businessId.toString());
    }
    // If none are provided, get all templates for the user

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    console.log("=== GET TEMPLATES SERVICE DEBUG ===");
    console.log("Final URL:", url);
    console.log("LocationId:", locationId);
    console.log("AccountId:", accountId);
    console.log("BusinessId:", businessId);

    return await this._httpHelperService.get(url);
  };

  // Create a new reply template
  createReplyTemplate = async (
    userId: number,
    templateData: ICreateReplyTemplateRequest,
    accountId?: string,
    locationId?: string
  ): Promise<IApiResponse> => {
    let url = REVIEW_SETTINGS_TEMPLATES(userId);
    const params = new URLSearchParams();

    // If locationId is provided, use only locationId (it's unique)
    if (locationId) {
      params.append("locationId", locationId);
    } else if (accountId) {
      params.append("accountId", accountId);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    console.log("=== CREATE TEMPLATE SERVICE DEBUG ===");
    console.log("Final URL:", url);
    console.log("Template Data:", templateData);

    return await this._httpHelperService.post(url, templateData);
  };

  // Update a reply template
  updateReplyTemplate = async (
    userId: number,
    templateId: number,
    templateData: IUpdateReplyTemplateRequest
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.put(
      REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId),
      templateData
    );
  };

  // Delete a reply template
  deleteReplyTemplate = async (
    userId: number,
    templateId: number
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.delete(
      REVIEW_SETTINGS_TEMPLATE_BY_ID(userId, templateId)
    );
  };

  // Map template to businesses/accounts/locations
  mapTemplateToBusinesses = async (
    userId: number,
    templateId: number,
    businessIds: number[],
    accountId?: string,
    locationId?: string
  ): Promise<IApiResponse> => {
    let url = REVIEW_SETTINGS_MAP_TEMPLATE(userId, templateId);
    const params = new URLSearchParams();

    if (accountId) params.append("accountId", accountId);
    if (locationId) params.append("locationId", locationId);

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    console.log("=== TEMPLATE MAPPING SERVICE DEBUG ===");
    console.log("Final URL:", url);
    console.log("Business IDs:", businessIds);

    return await this._httpHelperService.post(url, { businessIds });
  };

  // Get auto-reply settings for a business/account/location
  getAutoReplySettings = async (
    businessId: number,
    accountId?: string,
    locationId?: string
  ): Promise<IAutoReplySettingsResponse> => {
    let url = REVIEW_SETTINGS_AUTO_REPLY(businessId);
    const params = new URLSearchParams();

    if (accountId) params.append("accountId", accountId);
    if (locationId) params.append("locationId", locationId);

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    return await this._httpHelperService.get(url);
  };

  // Update auto-reply settings
  updateAutoReplySettings = async (
    businessId: number,
    settings: Omit<
      IAutoReplySettings,
      "id" | "business_id" | "created_at" | "updated_at"
    >,
    accountId?: string,
    locationId?: string
  ): Promise<IApiResponse> => {
    let url = REVIEW_SETTINGS_AUTO_REPLY(businessId);
    const params = new URLSearchParams();

    if (accountId) params.append("accountId", accountId);
    if (locationId) params.append("locationId", locationId);

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    console.log("=== REVIEW SETTINGS SERVICE DEBUG ===");
    console.log("Final URL:", url);
    console.log("Settings payload:", settings);

    return await this._httpHelperService.put(url, settings);
  };

  // Get template for auto-reply (used by auto-reply system)
  getTemplateForAutoReply = async (
    businessId: number,
    starRating: number
  ): Promise<IApiResponse> => {
    return await this._httpHelperService.get(
      REVIEW_SETTINGS_AUTO_REPLY_TEMPLATE(businessId, starRating)
    );
  };

  // Generate AI text for reply templates
  generateAIText = async (
    starRating: number
  ): Promise<IGenerateAITextResponse> => {
    return await this._httpHelperService.post(
      REVIEW_SETTINGS_GENERATE_AI_TEXT,
      { starRating }
    );
  };
}

export default ReviewSettingsService;
