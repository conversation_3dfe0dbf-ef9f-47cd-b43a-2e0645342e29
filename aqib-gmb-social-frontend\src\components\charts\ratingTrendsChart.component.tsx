import React from "react";
import { Box, Typography, useTheme } from "@mui/material";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Line } from "react-chartjs-2";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface IRatingTrendsChartProps {
  data: Record<string, {
    total: number;
    sum: number;
    avgRating: number;
    ratings: Record<number, number>;
  }>;
  title?: string;
}

const RatingTrendsChart: React.FC<IRatingTrendsChartProps> = ({
  data,
  title = "Weekly Average Rating Trends",
}) => {
  const theme = useTheme();

  // Prepare chart data
  const weeks = Object.keys(data).sort();
  const avgRatingData = weeks.map((week) => data[week]?.avgRating || 0);
  const totalReviewsData = weeks.map((week) => data[week]?.total || 0);

  const chartData = {
    labels: weeks.map((week) => {
      const date = new Date(week);
      return `Week of ${date.toLocaleDateString()}`;
    }),
    datasets: [
      {
        type: "line" as const,
        label: "Average Rating",
        data: avgRatingData,
        borderColor: theme.palette.primary.main,
        backgroundColor: theme.palette.primary.main + "20",
        fill: false,
        tension: 0.4,
        pointRadius: 5,
        pointHoverRadius: 8,
        pointBackgroundColor: theme.palette.primary.main,
        pointBorderColor: "#fff",
        pointBorderWidth: 2,
        yAxisID: "y",
      },
      {
        type: "bar" as const,
        label: "Review Volume",
        data: totalReviewsData,
        backgroundColor: theme.palette.secondary.main + "40",
        borderColor: theme.palette.secondary.main,
        borderWidth: 1,
        yAxisID: "y1",
      },
    ],
  };

  const options = {
    responsive: true,
    interaction: {
      mode: "index" as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: function (context: any) {
            return context[0].label;
          },
          label: function (context: any) {
            const weekKey = weeks[context.dataIndex];
            const weekData = data[weekKey];
            
            if (context.dataset.label === "Average Rating") {
              return [
                `Average Rating: ${context.parsed.y.toFixed(2)} ⭐`,
                `Total Reviews: ${weekData?.total || 0}`,
              ];
            } else {
              return `Review Volume: ${context.parsed.y}`;
            }
          },
          afterLabel: function (context: any) {
            if (context.dataset.label === "Average Rating") {
              const weekKey = weeks[context.dataIndex];
              const weekData = data[weekKey];
              if (weekData?.ratings) {
                const ratings = weekData.ratings;
                return [
                  "",
                  "Rating Distribution:",
                  `5⭐: ${ratings[5] || 0}`,
                  `4⭐: ${ratings[4] || 0}`,
                  `3⭐: ${ratings[3] || 0}`,
                  `2⭐: ${ratings[2] || 0}`,
                  `1⭐: ${ratings[1] || 0}`,
                ];
              }
            }
            return [];
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Week",
        },
      },
      y: {
        type: "linear" as const,
        display: true,
        position: "left" as const,
        title: {
          display: true,
          text: "Average Rating",
        },
        min: 1,
        max: 5,
        ticks: {
          stepSize: 0.5,
          callback: function (value: any) {
            return value.toFixed(1) + " ⭐";
          },
        },
      },
      y1: {
        type: "linear" as const,
        display: true,
        position: "right" as const,
        title: {
          display: true,
          text: "Review Volume",
        },
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  // Calculate summary statistics
  const overallAvgRating = weeks.length > 0 
    ? avgRatingData.reduce((sum, rating) => sum + rating, 0) / weeks.length 
    : 0;
  
  const maxRating = Math.max(...avgRatingData, 0);
  const minRating = avgRatingData.length > 0 ? Math.min(...avgRatingData) : 0;
  const totalReviews = totalReviewsData.reduce((sum, count) => sum + count, 0);

  // Calculate trend
  const firstHalf = avgRatingData.slice(0, Math.ceil(avgRatingData.length / 2));
  const secondHalf = avgRatingData.slice(Math.ceil(avgRatingData.length / 2));
  const firstHalfAvg = firstHalf.length > 0 ? firstHalf.reduce((sum, rating) => sum + rating, 0) / firstHalf.length : 0;
  const secondHalfAvg = secondHalf.length > 0 ? secondHalf.reduce((sum, rating) => sum + rating, 0) / secondHalf.length : 0;
  const ratingChange = secondHalfAvg - firstHalfAvg;
  
  const getTrendInfo = () => {
    if (Math.abs(ratingChange) < 0.1) return { text: "Stable", color: theme.palette.grey[600] };
    if (ratingChange > 0) return { text: `Improving (+${ratingChange.toFixed(2)})`, color: theme.palette.success.main };
    return { text: `Declining (${ratingChange.toFixed(2)})`, color: theme.palette.error.main };
  };

  const trend = getTrendInfo();

  // Calculate rating distribution for the entire period
  const overallDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  weeks.forEach(week => {
    const weekData = data[week];
    if (weekData?.ratings) {
      Object.entries(weekData.ratings).forEach(([rating, count]) => {
        overallDistribution[parseInt(rating) as keyof typeof overallDistribution] += count;
      });
    }
  });

  return (
    <Box sx={{ p: 2, backgroundColor: "#fff", borderRadius: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {title}
        </Typography>
        <Box sx={{ display: "flex", gap: 4, mb: 2, flexWrap: "wrap" }}>
          <Typography variant="body2" color="text.secondary">
            Overall Average: <strong>{overallAvgRating.toFixed(2)} ⭐</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Best Week: <strong>{maxRating.toFixed(2)} ⭐</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Lowest Week: <strong>{minRating.toFixed(2)} ⭐</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Total Reviews: <strong>{totalReviews}</strong>
          </Typography>
        </Box>
        <Typography 
          variant="body2" 
          sx={{ 
            color: trend.color,
            fontWeight: "medium"
          }}
        >
          Trend: {trend.text}
        </Typography>
      </Box>

      {weeks.length > 0 ? (
        <Line data={chartData} options={options} />
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: 300,
            color: "text.secondary",
          }}
        >
          <Typography variant="body1">
            No data available for the selected period
          </Typography>
        </Box>
      )}

      {totalReviews > 0 && (
        <Box sx={{ mt: 3, p: 2, backgroundColor: "#f5f5f5", borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            <strong>Overall Rating Distribution:</strong>
          </Typography>
          <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
            {[5, 4, 3, 2, 1].map(rating => (
              <Typography key={rating} variant="body2" color="text.secondary">
                {rating}⭐: {overallDistribution[rating as keyof typeof overallDistribution]} 
                ({totalReviews > 0 ? ((overallDistribution[rating as keyof typeof overallDistribution] / totalReviews) * 100).toFixed(1) : 0}%)
              </Typography>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default RatingTrendsChart;
