import React, { useState, useRef } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Autocomplete,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Card,
  CardContent,
  Stack,
  Alert,
} from "@mui/material";
import {
  Delete as DeleteIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  Settings as SettingsIcon,
} from "@mui/icons-material";
import { LoadingButton } from "@mui/lab";
import {
  LocalFalconConfiguration,
  LocalFalconSearchRequest,
  LocalFalconScanRequest,
  LocalFalconBusiness,
} from "../../services/localFalcon/localFalcon.service";

interface LocalFalconControlsProps {
  onSearch: (searchRequest: LocalFalconSearchRequest) => void;
  onScan: (scanRequest: LocalFalconScanRequest) => void;
  onSaveConfiguration: () => void;
  loading: boolean;
  currentLocation: any;
  savedConfigurations: LocalFalconConfiguration[];
  onLoadConfiguration: (config: LocalFalconConfiguration) => void;
  onDeleteConfiguration: (configId: number) => void;
  configuration: LocalFalconConfiguration;
  onConfigurationChange: (updates: Partial<LocalFalconConfiguration>) => void;
  businesses: LocalFalconBusiness[];
  onBusinessSearch: (query: string) => void;
  localFalconService: any; // Add service for API calls
}

const LocalFalconControls: React.FC<LocalFalconControlsProps> = ({
  onScan,
  onSaveConfiguration,
  loading,
  savedConfigurations,
  onLoadConfiguration,
  onDeleteConfiguration,
  configuration,
  onConfigurationChange,
  businesses,
  onBusinessSearch,
}) => {
  const [selectedBusiness, setSelectedBusiness] =
    useState<LocalFalconBusiness | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [configToDelete, setConfigToDelete] = useState<number | null>(null);

  const [businessLoading, setBusinessLoading] = useState(false);

  // Business search state management
  const [isBusinessRequestInProgress, setIsBusinessRequestInProgress] =
    useState(false);
  const pendingBusinessQueryRef = useRef<string | null>(null);
  const currentBusinessQueryRef = useRef<string>("");

  // Grid size options from 3x3 to 21x21
  const gridSizeOptions = [];
  for (let i = 3; i <= 21; i += 2) {
    gridSizeOptions.push(`${i}x${i}`);
  }

  const unitOptions = [
    { value: "meters", label: "Meters" },
    { value: "kilometers", label: "Kilometers" },
    { value: "miles", label: "Miles" },
  ];

  /**
   * Handles business search with request queuing to prevent overlapping API calls
   *
   * Flow:
   * 1. User types -> handleBusinessSearch called for each character
   * 2. If request in progress -> store query as pending and return
   * 3. If no request in progress -> start new request
   * 4. Since we can't await the parent's API call, we use a timeout to simulate completion
   * 5. After timeout, check if newer query is pending and process it
   * 6. This ensures only the latest user input is processed after current request "completes"
   */
  const handleBusinessSearch = (query: string) => {
    // If query is too short, clear results and return
    if (query.length < 3) {
      currentBusinessQueryRef.current = "";
      pendingBusinessQueryRef.current = null;
      setIsBusinessRequestInProgress(false);
      return;
    }

    // If a request is already in progress, store this query as pending
    // This prevents multiple simultaneous API calls
    if (isBusinessRequestInProgress) {
      pendingBusinessQueryRef.current = query;
      console.log(
        `Business search: Request in progress, queuing query: "${query}"`
      );
      return;
    }

    // Start the request
    setIsBusinessRequestInProgress(true);
    currentBusinessQueryRef.current = query;
    pendingBusinessQueryRef.current = null;

    console.log(`Business search: Starting request for query: "${query}"`);

    try {
      setBusinessLoading(true);
      onBusinessSearch(query);

      // Since we can't await the parent's API call, simulate completion with timeout
      // Most API responses should complete within 1-2 seconds
      setTimeout(() => {
        setBusinessLoading(false);

        // After the simulated completion, check if there's a pending query
        const pendingQuery = pendingBusinessQueryRef.current;
        if (pendingQuery && pendingQuery !== query) {
          // There's a newer query waiting, process it
          console.log(
            `Business search: Processing pending query: "${pendingQuery}"`
          );
          setIsBusinessRequestInProgress(false);
          handleBusinessSearch(pendingQuery);
        } else {
          // No pending query, we're done
          console.log(
            `Business search: Request completed for query: "${query}"`
          );
          setIsBusinessRequestInProgress(false);
        }
      }, 1500); // Wait 1.5 seconds for API response to complete
    } catch (error) {
      console.error("Error in business search:", error);
      setIsBusinessRequestInProgress(false);
      setBusinessLoading(false);
    }
  };

  const handleRunScan = () => {
    if (
      !configuration.keyword ||
      !configuration.businessName ||
      !configuration.centerLat ||
      !configuration.centerLng
    ) {
      return;
    }

    const scanRequest: LocalFalconScanRequest = {
      keyword: configuration.keyword,
      businessName: configuration.businessName,
      lat: configuration.centerLat,
      lng: configuration.centerLng,
      gridSize: configuration.gridSize,
      radius: configuration.radius,
      unit: configuration.unit,
      placeId: configuration.placeId,
    };

    onScan(scanRequest);
  };

  const handleDeleteClick = (configId: number) => {
    setConfigToDelete(configId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (configToDelete) {
      onDeleteConfiguration(configToDelete);
    }
    setDeleteDialogOpen(false);
    setConfigToDelete(null);
  };

  const isConfigurationValid = () => {
    return (
      configuration.keyword &&
      configuration.businessName &&
      configuration.centerLat &&
      configuration.centerLng &&
      configuration.gridSize &&
      configuration.radius > 0
    );
  };

  return (
    <Box>
      {/* Search Configuration */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          {!isConfigurationValid() && (
            <Alert severity="info" sx={{ mb: 2 }}>
              Please fill in all required fields: keyword, business name,
              location, grid size, and radius.
            </Alert>
          )}

          <TextField
            fullWidth
            label="Keyword"
            value={configuration.keyword}
            onChange={(e) => onConfigurationChange({ keyword: e.target.value })}
            sx={{ mb: 2 }}
            placeholder="e.g., restaurant, dentist, plumber"
          />

          <Autocomplete
            fullWidth
            options={businesses}
            getOptionLabel={(option) => option.name}
            value={selectedBusiness}
            onChange={(_, newValue) => {
              setSelectedBusiness(newValue);
              if (newValue) {
                onConfigurationChange({
                  businessName: newValue.name,
                  placeId: newValue.placeId,
                  centerLat: newValue.lat,
                  centerLng: newValue.lng,
                });
              }
            }}
            onInputChange={(_, newInputValue) => {
              handleBusinessSearch(newInputValue);
            }}
            loading={businessLoading}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Business Name"
                placeholder="Search for your business"
                helperText={
                  isBusinessRequestInProgress && pendingBusinessQueryRef.current
                    ? "Request in progress, newer query queued..."
                    : businessLoading
                    ? "Searching for businesses..."
                    : "Type to search for your business"
                }
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {businessLoading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props}>
                <Box>
                  <Typography variant="body2">{option.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {option.address}
                  </Typography>
                </Box>
              </Box>
            )}
            sx={{ mb: 2 }}
          />

          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <FormControl fullWidth>
              <InputLabel>Grid Size</InputLabel>
              <Select
                value={configuration.gridSize}
                label="Grid Size"
                onChange={(e) =>
                  onConfigurationChange({ gridSize: e.target.value })
                }
              >
                {gridSizeOptions.map((size) => (
                  <MenuItem key={size} value={size}>
                    {size} ({parseInt(size) * parseInt(size)} points)
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>
          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Radius"
              type="number"
              value={configuration.radius}
              onChange={(e) =>
                onConfigurationChange({ radius: Number(e.target.value) })
              }
              inputProps={{ min: 0.1, step: 0.1 }}
            />

            <FormControl fullWidth>
              <InputLabel>Unit</InputLabel>
              <Select
                value={configuration.unit}
                label="Unit"
                onChange={(e) =>
                  onConfigurationChange({
                    unit: e.target.value as "miles" | "kilometers" | "meters",
                  })
                }
              >
                {unitOptions.map((unit) => (
                  <MenuItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Stack>

          <LoadingButton
            fullWidth
            variant="contained"
            onClick={handleRunScan}
            loading={loading}
            disabled={!isConfigurationValid()}
            startIcon={<PlayArrowIcon />}
            sx={{ mb: 2 }}
          >
            Run Local Falcon Scan
          </LoadingButton>
        </CardContent>
      </Card>

      {/* Configuration Management */}
      <Card>
        <CardContent>
          <Typography
            variant="subtitle1"
            gutterBottom
            sx={{ display: "flex", alignItems: "center", gap: 1 }}
          >
            Save Configuration
          </Typography>

          <TextField
            fullWidth
            label="Configuration Name"
            value={configuration.name}
            onChange={(e) => onConfigurationChange({ name: e.target.value })}
            sx={{ mb: 2 }}
            placeholder="e.g., Downtown Restaurant Scan"
          />

          <LoadingButton
            fullWidth
            variant="outlined"
            onClick={onSaveConfiguration}
            loading={loading}
            disabled={!configuration.name.trim() || !isConfigurationValid()}
            startIcon={<SaveIcon />}
            sx={{ mb: 2 }}
          >
            Save Configuration
          </LoadingButton>

          {savedConfigurations.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" gutterBottom>
                Saved Configurations
              </Typography>
              <List dense>
                {savedConfigurations.map((config) => (
                  <ListItem key={config.id} divider>
                    <ListItemText
                      primary={config.name}
                      secondary={`${config.keyword} - ${config.businessName} (${config.gridSize})`}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => onLoadConfiguration(config)}
                        disabled={loading}
                        size="small"
                      >
                        <RefreshIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteClick(config.id!)}
                        disabled={loading}
                        size="small"
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Configuration</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this configuration? This action
            cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LocalFalconControls;
