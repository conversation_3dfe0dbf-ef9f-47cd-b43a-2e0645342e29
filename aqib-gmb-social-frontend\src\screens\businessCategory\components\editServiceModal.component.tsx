import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";

interface EditServiceModalProps {
  open: boolean;
  onClose: () => void;
  service: {
    name: string;
    description: string;
    price?: string;
  } | null;
  onSave: (updatedService: {
    name: string;
    description: string;
    price?: string;
  }) => void;
  onDelete: () => void;
}

const EditServiceModal: React.FC<EditServiceModalProps> = ({
  open,
  onClose,
  service,
  onSave,
  onDelete,
}) => {
  const [serviceName, setServiceName] = useState("");
  const [serviceDescription, setServiceDescription] = useState("");
  const [priceOption, setPriceOption] = useState("No price");
  const [servicePrice, setServicePrice] = useState("");

  useEffect(() => {
    if (service) {
      setServiceName(service.name || "");
      setServiceDescription(service.description || "");
      setPriceOption(service.price ? "Set price" : "No price");
      setServicePrice(service.price || "");
    }
  }, [service]);

  const handleSave = () => {
    const updatedService = {
      name: serviceName,
      description: serviceDescription,
      price: priceOption === "Set price" ? servicePrice : undefined,
    };
    onSave(updatedService);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  const handleDelete = () => {
    onDelete();
    onClose();
  };

  if (!service) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          bgcolor: "#ffffff",
          color: "#2F2F2F",
          borderRadius: "8px",
          maxHeight: "90vh",
          boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "16px 20px",
          bgcolor: "#EDEDED",
          color: "#2F2F2F",
          borderBottom: "1px solid #EBEBEB",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <IconButton
            onClick={onClose}
            sx={{
              color: "#2F2F2F",
              mr: 2,
              "&:hover": {
                bgcolor: "rgba(47, 47, 47, 0.1)",
              },
            }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: "1.1rem",
              color: "#2F2F2F",
            }}
          >
            Edit service details
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <IconButton
            sx={{
              color: "#2F2F2F",
              mr: 1,
              "&:hover": {
                bgcolor: "rgba(47, 47, 47, 0.1)",
              },
            }}
          >
            <MoreVertIcon />
          </IconButton>
          <IconButton
            onClick={onClose}
            sx={{
              color: "#2F2F2F",
              "&:hover": {
                bgcolor: "rgba(47, 47, 47, 0.1)",
              },
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent
        sx={{
          px: 3,
          py: 3,
          bgcolor: "#ffffff",
          color: "#2F2F2F",
        }}
      >
        {/* Service Name */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="caption"
            sx={{
              color: "#757575",
              fontSize: "0.75rem",
              mb: 1,
              display: "block",
              fontWeight: 500,
            }}
          >
            Service
          </Typography>
          <TextField
            fullWidth
            value={serviceName}
            onChange={(e) => setServiceName(e.target.value)}
            placeholder="Enter service name"
            variant="outlined"
            sx={{
              "& .MuiOutlinedInput-root": {
                bgcolor: "#ffffff",
                color: "#2F2F2F",
                borderRadius: "5px",
                "& fieldset": {
                  borderColor: "#EBEBEB",
                },
                "&:hover fieldset": {
                  borderColor: "#309898",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "#309898",
                },
              },
              "& .MuiInputBase-input": {
                color: "#2F2F2F",
              },
              "& .MuiInputBase-input::placeholder": {
                color: "#757575",
                opacity: 1,
              },
            }}
          />
          <Typography
            variant="caption"
            sx={{
              color: "#757575",
              fontSize: "0.7rem",
              float: "right",
              mt: 0.5,
            }}
          >
            {serviceName.length}/120
          </Typography>
        </Box>

        {/* Price Section */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="caption"
            sx={{
              color: "#757575",
              fontSize: "0.75rem",
              mb: 1,
              display: "block",
              fontWeight: 500,
            }}
          >
            Price
          </Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <FormControl sx={{ minWidth: 150 }}>
              <Select
                value={priceOption}
                onChange={(e) => setPriceOption(e.target.value)}
                sx={{
                  bgcolor: "#ffffff",
                  color: "#2F2F2F",
                  borderRadius: "5px",
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#EBEBEB",
                  },
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#309898",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#309898",
                  },
                  "& .MuiSvgIcon-root": {
                    color: "#2F2F2F",
                  },
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      bgcolor: "#ffffff",
                      color: "#2F2F2F",
                      boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
                    },
                  },
                }}
              >
                <MenuItem value="No price">No price</MenuItem>
                <MenuItem value="Set price">Set price</MenuItem>
              </Select>
            </FormControl>
            {priceOption === "Set price" && (
              <TextField
                placeholder="Service price (INR)"
                value={servicePrice}
                onChange={(e) => setServicePrice(e.target.value)}
                sx={{
                  flex: 1,
                  "& .MuiOutlinedInput-root": {
                    bgcolor: "#ffffff",
                    color: "#2F2F2F",
                    borderRadius: "5px",
                    "& fieldset": {
                      borderColor: "#EBEBEB",
                    },
                    "&:hover fieldset": {
                      borderColor: "#309898",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#309898",
                    },
                  },
                  "& .MuiInputBase-input": {
                    color: "#2F2F2F",
                  },
                  "& .MuiInputBase-input::placeholder": {
                    color: "#757575",
                    opacity: 1,
                  },
                }}
              />
            )}
          </Box>
        </Box>

        {/* Service Description */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="caption"
            sx={{
              color: "#757575",
              fontSize: "0.75rem",
              mb: 1,
              display: "block",
              fontWeight: 500,
            }}
          >
            Service description
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            value={serviceDescription}
            onChange={(e) => setServiceDescription(e.target.value)}
            placeholder="Enter service description"
            variant="outlined"
            sx={{
              "& .MuiOutlinedInput-root": {
                bgcolor: "#ffffff",
                color: "#2F2F2F",
                borderRadius: "5px",
                "& fieldset": {
                  borderColor: "#EBEBEB",
                },
                "&:hover fieldset": {
                  borderColor: "#309898",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "#309898",
                },
              },
              "& .MuiInputBase-input": {
                color: "#2F2F2F",
              },
              "& .MuiInputBase-input::placeholder": {
                color: "#757575",
                opacity: 1,
              },
            }}
          />
          <Typography
            variant="caption"
            sx={{
              color: "#757575",
              fontSize: "0.7rem",
              float: "right",
              mt: 0.5,
            }}
          >
            {serviceDescription.length}/300
          </Typography>
        </Box>

        {/* Delete Service Button */}
        <Button
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
          sx={{
            color: "#F24245",
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            p: 0,
            "&:hover": {
              bgcolor: "#********",
            },
          }}
        >
          Delete service
        </Button>
      </DialogContent>

      <DialogActions
        sx={{
          p: 3,
          justifyContent: "space-between",
          bgcolor: "#ffffff",
          borderTop: "1px solid #EBEBEB",
        }}
      >
        <Button
          onClick={handleCancel}
          sx={{
            color: "#757575",
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            px: 3,
            py: 1,
            border: "1px solid #EBEBEB",
            borderRadius: "5px",
            "&:hover": {
              bgcolor: "#f4f4f4",
              borderColor: "#EBEBEB",
            },
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          sx={{
            bgcolor: "#309898",
            color: "#ffffff",
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            px: 3,
            py: 1,
            borderRadius: "5px",
            "&:hover": {
              bgcolor: "#267373",
            },
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditServiceModal;
