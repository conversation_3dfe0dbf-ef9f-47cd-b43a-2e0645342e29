import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  useTheme,
} from "@mui/material";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import TrendingFlatIcon from "@mui/icons-material/TrendingFlat";

interface IPerformanceSummaryCardProps {
  currentPeriod: {
    totalReviews: number;
    totalReplies: number;
    responseRate: number;
    avgRating: number;
    avgResponseTime: number;
  };
  trends: {
    reviewsChange: number;
    repliesChange: number;
    responseRateChange: number;
    avgRatingChange: number;
    avgResponseTimeChange: number;
  };
  dateRange: { fromDate: string; toDate: string };
}

const PerformanceSummaryCard: React.FC<IPerformanceSummaryCardProps> = ({
  currentPeriod,
  trends,
  dateRange,
}) => {
  const theme = useTheme();

  const formatPercentage = (value: number): string => {
    return `${value >= 0 ? "+" : ""}${value.toFixed(1)}%`;
  };

  const getTrendIcon = (value: number) => {
    if (value > 0) return <TrendingUpIcon fontSize="small" />;
    if (value < 0) return <TrendingDownIcon fontSize="small" />;
    return <TrendingFlatIcon fontSize="small" />;
  };

  const getTrendColor = (value: number, isPositiveGood: boolean = true) => {
    if (value === 0) return theme.palette.grey[500];
    const isGood = isPositiveGood ? value > 0 : value < 0;
    return isGood ? theme.palette.success.main : theme.palette.error.main;
  };

  const formatResponseTime = (hours: number): string => {
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 24) return `${hours.toFixed(1)}h`;
    return `${(hours / 24).toFixed(1)}d`;
  };

  const formatDateRange = (fromDate: string, toDate: string): string => {
    const from = new Date(fromDate);
    const to = new Date(toDate);
    return `${from.toLocaleDateString()} - ${to.toLocaleDateString()}`;
  };

  const metrics = [
    {
      title: "Total Reviews",
      value: currentPeriod.totalReviews.toLocaleString(),
      change: trends.reviewsChange,
      isPositiveGood: true,
    },
    {
      title: "Total Replies",
      value: currentPeriod.totalReplies.toLocaleString(),
      change: trends.repliesChange,
      isPositiveGood: true,
    },
    {
      title: "Response Rate",
      value: `${currentPeriod.responseRate.toFixed(1)}%`,
      change: trends.responseRateChange,
      isPositiveGood: true,
    },
    {
      title: "Average Rating",
      value: `${currentPeriod.avgRating.toFixed(1)} ⭐`,
      change: trends.avgRatingChange,
      isPositiveGood: true,
    },
    {
      title: "Avg Response Time",
      value: formatResponseTime(currentPeriod.avgResponseTime),
      change: trends.avgResponseTimeChange,
      isPositiveGood: false, // Lower response time is better
    },
  ];

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Performance Summary
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Analysis period: {formatDateRange(dateRange.fromDate, dateRange.toDate)}
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {metrics.map((metric, index) => (
            <Grid item xs={12} sm={6} md={2.4} key={index}>
              <Box
                sx={{
                  p: 2,
                  backgroundColor: "#f8f9fa",
                  borderRadius: 2,
                  textAlign: "center",
                }}
              >
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {metric.title}
                </Typography>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  {metric.value}
                </Typography>
                <Chip
                  icon={getTrendIcon(metric.change)}
                  label={formatPercentage(metric.change)}
                  size="small"
                  sx={{
                    backgroundColor: getTrendColor(metric.change, metric.isPositiveGood),
                    color: "white",
                    fontWeight: "bold",
                    "& .MuiChip-icon": {
                      color: "white",
                    },
                  }}
                />
              </Box>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 3, p: 2, backgroundColor: "#e3f2fd", borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>Note:</strong> Percentage changes are compared to the previous period of the same duration.
            Positive changes in reviews, replies, response rate, and rating indicate improvement.
            Negative changes in response time indicate faster responses (improvement).
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PerformanceSummaryCard;
