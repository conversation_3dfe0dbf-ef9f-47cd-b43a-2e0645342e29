export interface ITwitterCreatePost {
  accountId: string;
  text: string;
  mediaUrls?: string[];
  replyTo?: string;
  quoteTweetId?: string;
  scheduledPublishTime?: string;
}

export interface ITwitterSelectedAccount {
  accountId: string;
  accountName: string;
  accountUsername: string;
  accountPictureUrl?: string;
  isVerified: boolean;
  followersCount: number;
  followingCount: number;
  tweetCount: number;
}

export interface ITwitterAccountPost {
  accountInfo: ITwitterSelectedAccount & {
    status?: boolean | null;
    error?: string;
  };
}

export interface ITwitterFormData {
  text: string;
  mediaUrls: string[];
  replyTo?: string;
  quoteTweetId?: string;
  scheduledPublishTime?: string;
  accountIds: string[];
}

export interface ITwitterPostFormProps {
  formData: ITwitterCreatePost;
  onFormChange: (data: ITwitterCreatePost) => void;
  uploadedImages: any[];
  onImageUpload: () => void;
  onGalleryOpen: () => void;
  onImageRemove: (index: number) => void;
  errors: any;
  twitterAccounts: any[];
  loading?: boolean;
  selectedAccounts?: ITwitterSelectedAccount[];
  onSelectedAccountsChange?: (accounts: ITwitterSelectedAccount[]) => void;
  onSubmit: () => void;
  isTwitterConnected?: boolean;
}
