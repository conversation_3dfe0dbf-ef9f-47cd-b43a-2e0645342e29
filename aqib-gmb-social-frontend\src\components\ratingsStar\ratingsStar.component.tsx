import Avatar from "@mui/material/Avatar";
import StarRoundedIcon from "@mui/icons-material/StarRounded";
import { STARRATINGMAP } from "../../constants/dbConstant.constant";
import Box from "@mui/material/Box";
import { useEffect, useState } from "react";

const RatingsStar = (props: { starRating: string; size?: number }) => {
  const { size } = props;
  const [iconSize, setIconSize] = useState<number>(30);

  useEffect(() => {
    if (size && size > 0) {
      setIconSize(size);
    }
  }, []);

  return (
    <Box>
      {Array.from({
        length: 5,
      }).map((_, index) => {
        {
          return index <
            STARRATINGMAP[props.starRating as keyof typeof STARRATINGMAP] ? (
            <span key={index}>
              <StarRoundedIcon
                sx={{
                  fontSize: iconSize,
                  color: "#FFD700",
                }}
              />
            </span>
          ) : (
            <span key={index}>
              <StarRoundedIcon sx={{ fontSize: iconSize, color: "#a5a5a5" }} />
            </span>
          );
        }
      })}
    </Box>
  );
};

export default RatingsStar;
