const express = require("express");
const path = require("path");
const multer = require("multer");

// Environment configuration is loaded by app.local.js before this point
const swaggerUi = require("swagger-ui-express");
const swaggerSpec = require("./config/swagger");
const logger = require("./utils/logger");
const { requestLogger, errorLogger } = require("./middleware/requestLogger");
const app = express();
// Define the upload directory inside the project folder
const uploadDir = path.join(__dirname, "uploads");

// 🔹 Ensure this line is present to serve static files correctly
app.use("/uploads", express.static(uploadDir));

// Set up multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({ storage });

const cors = require("cors");
const bodyParser = require("body-parser");
// const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
// app.use(awsServerlessExpressMiddleware.eventContext());
app.use(bodyParser.json()); // to support JSON-encoded bodies
app.use(
  bodyParser.urlencoded({
    // to support URL-encoded bodies
    extended: false,
  })
);
const corsOptions = {
  origin: process.env.UI_ORIGIN, // Replace with your UI origin
  methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
  credentials: true, // Allow cookies to be sent
  // optionsSuccessStatus: 204,
};
app.use(cors(corsOptions));

// Add request logging middleware (should be early in the middleware chain)
app.use(requestLogger);

const combiningReqParams = (req, res, next) => {
  const params = { ...req.body, ...req.query };
  req["allParams"] = params;
  next();
};
// Using the combining params middleware
app.use(combiningReqParams);

const db = require("./config/db");

//Routes
const routes = require("./routes");
const authRoutes = require("./routes/auth.routes");

(async () => {
  try {
    const result = await db.query("SELECT CURRENT_TIMESTAMP");
    logger.info("Database connected successfully", {
      timestamp: result[0].CURRENT_TIMESTAMP,
      database: process.env.APP_DB_NAME,
      host: process.env.APP_DB_HOST,
    });
  } catch (error) {
    logger.error("Database connection failed", {
      error: error.message,
      stack: error.stack,
      database: process.env.APP_DB_NAME,
      host: process.env.APP_DB_HOST,
    });
  }
})();

app.get("/", async (req, res) => {
  res.send({
    message: `Backend is running`,
    environment: process.env.APP_ENV_NAME,
    version: process.env.APP_VER_PREFIX,
  });
});

// Swagger UI setup
app.use(
  "/api-docs",
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: ".swagger-ui .topbar { display: none }",
    swaggerOptions: {
      docExpansion: "none",
      persistAuthorization: true,
    },
  })
);

app.use(`/${process.env.APP_VER_PREFIX}`, routes);
app.use("/auth", authRoutes);

// Add error logging middleware (should be at the end)
app.use(errorLogger);

// Log application startup
logger.info("Application started", {
  environment: process.env.APP_ENV_NAME,
  version: process.env.APP_VER_PREFIX,
  port: process.env.APP_PORT || 3000,
});

module.exports = app;
