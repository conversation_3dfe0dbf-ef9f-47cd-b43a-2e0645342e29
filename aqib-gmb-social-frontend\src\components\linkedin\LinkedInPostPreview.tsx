import React from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Avatar,
  Divider,
  IconButton,
} from "@mui/material";
import ThumbUpIcon from "@mui/icons-material/ThumbUp";
import CommentIcon from "@mui/icons-material/Comment";
import ShareIcon from "@mui/icons-material/Share";
import SendIcon from "@mui/icons-material/Send";
import LinkedInIcon from "@mui/icons-material/LinkedIn";

interface LinkedInPostPreviewProps {
  profileName?: string;
  profilePicture?: string;
  text: string;
  media?: any[];
  showPreview?: boolean;
}

const LinkedInPostPreview: React.FC<LinkedInPostPreviewProps> = ({
  profileName = "Your Profile",
  profilePicture,
  text,
  media,
  showPreview = true,
}) => {
  if (!showPreview || !text.trim()) {
    return null;
  }

  const formatText = (text: string) => {
    // Replace line breaks with proper spacing
    return text.split('\n').map((line, index) => (
      <React.Fragment key={index}>
        {line}
        {index < text.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  };

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="h6" sx={{ mb: 2, display: "flex", alignItems: "center" }}>
        <LinkedInIcon sx={{ mr: 1, color: "#0077B5" }} />
        LinkedIn Preview
      </Typography>
      
      <Card
        sx={{
          maxWidth: 500,
          border: "1px solid #e0e0e0",
          borderRadius: 2,
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        }}
      >
        {/* Post Header */}
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <Avatar
              src={profilePicture}
              sx={{
                width: 48,
                height: 48,
                mr: 2,
                backgroundColor: "#0077B5",
              }}
            >
              {!profilePicture && profileName.charAt(0).toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="subtitle1" fontWeight="600">
                {profileName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Just now • 🌐
              </Typography>
            </Box>
          </Box>

          {/* Post Content */}
          <Typography
            variant="body1"
            sx={{
              mb: 2,
              lineHeight: 1.5,
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
            }}
          >
            {formatText(text)}
          </Typography>

          {/* Media Preview */}
          {media && media.length > 0 && (
            <Box sx={{ mb: 2 }}>
              {media.map((mediaItem, index) => (
                <Box
                  key={index}
                  sx={{
                    border: "1px solid #e0e0e0",
                    borderRadius: 1,
                    overflow: "hidden",
                    mb: index < media.length - 1 ? 1 : 0,
                  }}
                >
                  {mediaItem.type === "image" ? (
                    <img
                      src={mediaItem.url}
                      alt={`Media ${index + 1}`}
                      style={{
                        width: "100%",
                        height: "auto",
                        maxHeight: 300,
                        objectFit: "cover",
                      }}
                    />
                  ) : mediaItem.type === "video" ? (
                    <video
                      src={mediaItem.url}
                      controls
                      style={{
                        width: "100%",
                        height: "auto",
                        maxHeight: 300,
                      }}
                    />
                  ) : (
                    <Box
                      sx={{
                        p: 2,
                        backgroundColor: "#f5f5f5",
                        textAlign: "center",
                      }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        Media Preview
                      </Typography>
                    </Box>
                  )}
                </Box>
              ))}
            </Box>
          )}
        </CardContent>

        <Divider />

        {/* Post Actions */}
        <CardContent sx={{ pt: 1, pb: 1 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-around",
              alignItems: "center",
            }}
          >
            <IconButton
              size="small"
              sx={{
                color: "#666",
                "&:hover": { backgroundColor: "#f0f0f0" },
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              <ThumbUpIcon fontSize="small" />
              <Typography variant="caption">Like</Typography>
            </IconButton>

            <IconButton
              size="small"
              sx={{
                color: "#666",
                "&:hover": { backgroundColor: "#f0f0f0" },
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              <CommentIcon fontSize="small" />
              <Typography variant="caption">Comment</Typography>
            </IconButton>

            <IconButton
              size="small"
              sx={{
                color: "#666",
                "&:hover": { backgroundColor: "#f0f0f0" },
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              <ShareIcon fontSize="small" />
              <Typography variant="caption">Repost</Typography>
            </IconButton>

            <IconButton
              size="small"
              sx={{
                color: "#666",
                "&:hover": { backgroundColor: "#f0f0f0" },
                display: "flex",
                flexDirection: "column",
                gap: 0.5,
              }}
            >
              <SendIcon fontSize="small" />
              <Typography variant="caption">Send</Typography>
            </IconButton>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LinkedInPostPreview;
