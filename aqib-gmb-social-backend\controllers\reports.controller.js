const Reports = require("../models/reports.models");

module.exports = class ReportsController {
  // Get reviews reports with aggregated data
  static async getReviewsReports(req, res) {
    try {
      console.log("Reports API called - req.user:", req.user);
      console.log("Reports API called - req.body:", req.body);

      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      const userId = req.user.userId;
      console.log("User ID:", userId);
      const { businessId, accountId, locationId, startDate, endDate } =
        req.body;

      const filters = {
        businessId,
        accountId,
        locationId,
        startDate,
        endDate,
      };

      // Fetch reviews data using the Reports model
      const reviews = await Reports.fetchReviewsData(userId, filters);
      console.log("Query results count:", reviews.length);

      // Generate aggregated data for charts
      const aggregatedData = Reports.generateAggregatedData(reviews);
      console.log("Aggregated data:", aggregatedData);

      res.status(200).json({
        success: true,
        message: "Reviews reports fetched successfully",
        data: {
          reviews: reviews,
          aggregated: aggregatedData,
        },
      });
    } catch (error) {
      console.error("Error fetching reviews reports:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch reviews reports",
        error: error.message,
      });
    }
  }

  // Export reviews report
  static async exportReviewsReport(req, res) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      const userId = req.user.userId;
      const { businessId, accountId, locationId, startDate, endDate } =
        req.body;

      const filters = {
        businessId,
        accountId,
        locationId,
        startDate,
        endDate,
      };

      // Fetch reviews data for export using the Reports model
      const reviews = await Reports.fetchReviewsForExport(userId, filters);

      // TODO: Format data for export (CSV, Excel, etc.)
      // This will be implemented based on existing export patterns in the codebase

      res.status(200).json({
        success: true,
        message: "Export functionality ready - formatting to be implemented",
        data: reviews,
      });
    } catch (error) {
      console.error("Error exporting reviews report:", error);
      res.status(500).json({
        success: false,
        message: "Failed to export reviews report",
        error: error.message,
      });
    }
  }

  // Get performance reviews reports with trend analysis
  static async getPerformanceReports(req, res) {
    try {
      console.log("Performance Reports API called - req.user:", req.user);
      console.log("Performance Reports API called - req.body:", req.body);

      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      const userId = req.user.userId;
      console.log("User ID:", userId);
      const { businessId, accountId, locationId, fromDate, toDate } = req.body;

      // Validate required fields
      if (!fromDate || !toDate) {
        return res.status(400).json({
          success: false,
          message:
            "From date and To date are required for performance analysis",
        });
      }

      // Validate date range
      const startDate = new Date(fromDate);
      const endDate = new Date(toDate);
      const today = new Date();

      if (startDate >= endDate) {
        return res.status(400).json({
          success: false,
          message: "To date must be greater than From date",
        });
      }

      if (startDate > today || endDate > today) {
        return res.status(400).json({
          success: false,
          message: "Future dates are not allowed",
        });
      }

      const filters = {
        businessId,
        accountId,
        locationId,
        fromDate,
        toDate,
      };

      // Fetch performance data using the Reports model
      const reviews = await Reports.fetchPerformanceData(userId, filters);
      console.log("Performance query results count:", reviews.length);

      // Generate performance analytics
      const performanceData = Reports.generatePerformanceData(reviews);
      console.log("Performance data generated:", performanceData);

      res.status(200).json({
        success: true,
        message: "Performance reports fetched successfully",
        data: {
          performance: performanceData,
          dateRange: { fromDate, toDate },
          totalRecords: reviews.length,
        },
      });
    } catch (error) {
      console.error("Error fetching performance reports:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch performance reports",
        error: error.message,
      });
    }
  }

  // Export performance report
  static async exportPerformanceReport(req, res) {
    try {
      if (!req.user || !req.user.userId) {
        return res.status(401).json({
          success: false,
          message: "User not authenticated",
        });
      }

      const userId = req.user.userId;
      const { businessId, accountId, locationId, fromDate, toDate } = req.body;

      // Validate required fields
      if (!fromDate || !toDate) {
        return res.status(400).json({
          success: false,
          message:
            "From date and To date are required for performance analysis",
        });
      }

      const filters = {
        businessId,
        accountId,
        locationId,
        fromDate,
        toDate,
      };

      // Fetch performance data for export using the Reports model
      const reviews = await Reports.fetchPerformanceData(userId, filters);
      const performanceData = Reports.generatePerformanceData(reviews);

      res.status(200).json({
        success: true,
        message: "Performance export data ready",
        data: {
          performance: performanceData,
          dateRange: { fromDate, toDate },
          exportTimestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("Error exporting performance report:", error);
      res.status(500).json({
        success: false,
        message: "Failed to export performance report",
        error: error.message,
      });
    }
  }
};
