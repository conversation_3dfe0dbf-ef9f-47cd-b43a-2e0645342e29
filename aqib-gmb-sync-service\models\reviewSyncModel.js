const database = require("../config/database");
const logger = require("../utils/logger");

class ReviewSyncModel {
  /**
   * Get all active OAuth tokens with user information
   */
  async getAllOAuthTokens() {
    try {
      const query = `
        SELECT
          got.id,
          got.accessToken,
          got.refreshToken,
          got.userId,
          got.gmbAccountId,
          got.statusId,
          got.createdAt,
          got.updatedAt,
          u.email as userEmail,
          u.name as userName,
          u.mobile as userMobile
        FROM gmb_oauth_tokens got
        LEFT JOIN users u ON got.userId = u.id
        WHERE got.statusId = 1
        ORDER BY got.gmbAccountId
      `;

      const tokens = await database.query(query);

      logger.logAutoReply("OAUTH_TOKENS_FETCHED", {
        tokenCount: tokens.length,
        message: "Fetched OAuth tokens from database",
      });

      return tokens;
    } catch (error) {
      logger.error("Error fetching OAuth tokens:", {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get all locations for a specific account
   */
  async getLocationsForAccount(accountId) {
    try {
      const query = `
        SELECT 
          gl.id,
          gl.gmbLocationId,
          gl.gmbLocationName,
          gl.gmbAccountId,
          gl.statusId,
          gl.createdAt,
          gl.updatedAt
        FROM gmb_locations gl
        WHERE gl.gmbAccountId = ? AND gl.statusId = 1
        ORDER BY gl.gmbLocationName
      `;

      const locations = await database.query(query, [accountId]);

      logger.logAutoReply("LOCATIONS_FETCHED", {
        accountId,
        locationCount: locations.length,
        message: "Fetched locations for account",
      });

      return locations;
    } catch (error) {
      logger.error("Error fetching locations for account:", {
        accountId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Get account information for a specific account ID
   */
  async getAccountInfo(accountId) {
    try {
      const query = `
        SELECT 
          ga.id,
          ga.accountId,
          ga.accountName,
          ga.businessId,
          ga.statusId,
          gbm.businessName,
          gbm.businessEmail
        FROM gmb_accounts ga
        LEFT JOIN gmb_businesses_master gbm ON ga.businessId = gbm.id
        WHERE ga.accountId = ? AND ga.statusId = 1
      `;

      const accounts = await database.query(query, [accountId]);

      return accounts.length > 0 ? accounts[0] : null;
    } catch (error) {
      logger.error("Error fetching account info:", {
        accountId,
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Create sync log table if it doesn't exist
   */
  async createSyncLogTable() {
    try {
      const query = `
        CREATE TABLE IF NOT EXISTS review_sync_log (
          id INT AUTO_INCREMENT PRIMARY KEY,
          account_id VARCHAR(255) NOT NULL,
          location_id VARCHAR(255) NOT NULL,
          user_id INT NOT NULL,
          sync_status ENUM('success', 'failed', 'skipped') NOT NULL,
          reviews_count INT DEFAULT 0,
          error_message TEXT,
          sync_duration_ms INT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_account_id (account_id),
          INDEX idx_location_id (location_id),
          INDEX idx_user_id (user_id),
          INDEX idx_sync_status (sync_status),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;

      await database.query(query);

      logger.logAutoReply("SYNC_LOG_TABLE_CREATED", {
        message: "Review sync log table created or verified",
      });
    } catch (error) {
      logger.error("Error creating sync log table:", {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Log a sync operation
   */
  async logSyncOperation(syncData) {
    try {
      const query = `
        INSERT INTO review_sync_log 
        (account_id, location_id, user_id, sync_status, reviews_count, error_message, sync_duration_ms)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        syncData.accountId,
        syncData.locationId,
        syncData.userId,
        syncData.status,
        syncData.reviewsCount || 0,
        syncData.errorMessage || null,
        syncData.duration || null,
      ];

      const result = await database.query(query, values);

      logger.logAutoReply("SYNC_OPERATION_LOGGED", {
        logId: result.insertId,
        accountId: syncData.accountId,
        locationId: syncData.locationId,
        status: syncData.status,
        message: "Sync operation logged to database",
      });

      return result.insertId;
    } catch (error) {
      logger.error("Error logging sync operation:", {
        syncData,
        error: error.message,
        stack: error.stack,
      });
      // Don't throw here as logging failure shouldn't stop the sync process
    }
  }

  /**
   * Get sync statistics for the last 24 hours
   */
  async getSyncStats24h() {
    try {
      const query = `
        SELECT 
          sync_status,
          COUNT(*) as count,
          SUM(reviews_count) as total_reviews,
          AVG(sync_duration_ms) as avg_duration_ms
        FROM review_sync_log 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY sync_status
      `;

      const stats = await database.query(query);

      return stats;
    } catch (error) {
      logger.error("Error fetching sync stats:", {
        error: error.message,
        stack: error.stack,
      });
      return [];
    }
  }

  /**
   * Get recent sync operations
   */
  async getRecentSyncOperations(limit = 100) {
    try {
      // Ensure limit is a valid integer and sanitize it
      const limitValue = parseInt(limit, 10);
      if (isNaN(limitValue) || limitValue <= 0 || limitValue > 1000) {
        throw new Error(`Invalid limit value: ${limit}`);
      }

      // Use string interpolation for LIMIT to avoid parameter binding issues
      const query = `
        SELECT
          rsl.*,
          gl.gmbLocationName,
          ga.accountName,
          u.email as userEmail,
          u.name as userName
        FROM review_sync_log rsl
        LEFT JOIN gmb_locations gl ON rsl.location_id = gl.gmbLocationId
        LEFT JOIN gmb_accounts ga ON rsl.account_id = ga.accountId
        LEFT JOIN users u ON rsl.user_id = u.id
        ORDER BY rsl.created_at DESC
        LIMIT ${limitValue}
      `;

      const operations = await database.query(query);

      return operations;
    } catch (error) {
      logger.error("Error fetching recent sync operations:", {
        error: error.message,
        stack: error.stack,
      });
      return [];
    }
  }

  /**
   * Clean up old sync logs (older than 30 days)
   */
  async cleanupOldLogs() {
    try {
      const query = `
        DELETE FROM review_sync_log 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
      `;

      const result = await database.query(query);

      logger.logAutoReply("SYNC_LOG_CLEANUP", {
        deletedRows: result.affectedRows,
        message: "Cleaned up old sync logs",
      });

      return result.affectedRows;
    } catch (error) {
      logger.error("Error cleaning up old sync logs:", {
        error: error.message,
        stack: error.stack,
      });
      return 0;
    }
  }

  /**
   * Get sync summary for dashboard
   */
  async getSyncSummary() {
    try {
      const queries = {
        totalTokens:
          "SELECT COUNT(*) as count FROM gmb_oauth_tokens WHERE statusId = 1",
        totalLocations: `
          SELECT COUNT(DISTINCT gl.gmbLocationId) as count 
          FROM gmb_locations gl 
          JOIN gmb_oauth_tokens got ON gl.gmbAccountId = got.gmbAccountId 
          WHERE gl.statusId = 1 AND got.statusId = 1
        `,
        lastSyncTime: `
          SELECT MAX(created_at) as last_sync 
          FROM review_sync_log
        `,
        todayStats: `
          SELECT 
            sync_status,
            COUNT(*) as count,
            SUM(reviews_count) as total_reviews
          FROM review_sync_log 
          WHERE DATE(created_at) = CURDATE()
          GROUP BY sync_status
        `,
      };

      const results = {};

      for (const [key, query] of Object.entries(queries)) {
        try {
          const result = await database.query(query);
          results[key] = result;
        } catch (error) {
          logger.error(`Error executing query for ${key}:`, error);
          results[key] = [];
        }
      }

      return results;
    } catch (error) {
      logger.error("Error fetching sync summary:", {
        error: error.message,
        stack: error.stack,
      });
      return {};
    }
  }
}

module.exports = new ReviewSyncModel();
