const logger = require("../utils/logger");
const twitterService = require("../services/twitter.service");
const Twitter = require("../models/twitter.models");
const { v4: uuidv4 } = require("uuid");

/**
 * Welcome endpoint for Twitter controller
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("twitter", "welcome", req.requestId);

    const response = {
      message: "Twitter Integration API Controller",
      endpoints: {
        authenticate: "/api/twitter/authenticate",
        callback: "/api/twitter/callback",
        accounts: "/api/twitter/accounts/:userId",
        createPost: "/api/twitter/post/:userId",
        posts: "/api/twitter/posts/:userId",
      },
    };

    logger.info("Twitter welcome endpoint accessed", {
      requestId: req.requestId,
    });

    res.send(response);
  } catch (error) {
    logger.error("Error in Twitter welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Initiate Twitter OAuth authentication
 */
const authenticate = async (req, res) => {
  try {
    const { userId } = req.body;

    logger.logControllerAction("twitter", "authenticate", req.requestId, {
      userId,
    });

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
        data: null,
      });
    }

    // Generate state parameter for security
    const state = `${userId}-${uuidv4()}`;

    // Generate Twitter OAuth URL
    const authUrl = twitterService.generateAuthUrl(state);

    logger.info("Twitter authentication initiated", {
      requestId: req.requestId,
      userId,
      state,
    });

    res.json({
      success: true,
      message: "Twitter authentication URL generated",
      data: {
        authUrl,
        state,
      },
    });
  } catch (error) {
    logger.error("Error in Twitter authentication", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Failed to initiate Twitter authentication",
      error: error.message,
    });
  }
};

/**
 * Handle Twitter OAuth callback
 */
const callback = async (req, res) => {
  try {
    const { code, state } = req.body;

    logger.logControllerAction("twitter", "callback", req.requestId, {
      hasCode: !!code,
      state,
    });

    if (!code || !state) {
      return res.status(400).json({
        success: false,
        message: "Authorization code and state are required",
        data: null,
      });
    }

    // Parse state to get userId
    const stateParts = state.split("-");
    if (stateParts.length < 2) {
      return res.status(400).json({
        success: false,
        message: "Invalid state parameter",
        data: null,
      });
    }

    const userId = parseInt(stateParts[0]);

    logger.info("Processing Twitter OAuth callback", {
      requestId: req.requestId,
      userId,
    });

    // Exchange code for access token
    const tokenData = await twitterService.exchangeCodeForToken(code, state);

    // Get user information
    const twitterUserInfo = await twitterService.getUserInfo(
      tokenData.access_token
    );

    // Save OAuth tokens
    const tokenResult = await Twitter.saveOAuthTokens({
      userId,
      twitterUserId: twitterUserInfo.id,
      twitterUsername: twitterUserInfo.username,
      twitterUserName: twitterUserInfo.name,
      twitterUserEmail: null, // Twitter API v2 doesn't provide email by default
      twitterUserPicture: twitterUserInfo.profile_image_url,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || null,
      expiresAt: tokenData.expires_in
        ? new Date(Date.now() + tokenData.expires_in * 1000)
        : null,
    });

    // Get the inserted token ID for foreign key reference
    const twitterOAuthTokenId = tokenResult.result.insertId;

    // Save the user's account as a Twitter account
    const accountsData = [
      {
        twitterOAuthTokenId,
        userId,
        accountId: twitterUserInfo.id,
        accountName: twitterUserInfo.name,
        accountUsername: twitterUserInfo.username,
        accountDescription: twitterUserInfo.description,
        accountPictureUrl: twitterUserInfo.profile_image_url,
        followersCount: twitterUserInfo.public_metrics?.followers_count || 0,
        followingCount: twitterUserInfo.public_metrics?.following_count || 0,
        tweetCount: twitterUserInfo.public_metrics?.tweet_count || 0,
        isVerified: twitterUserInfo.verified ? 1 : 0,
      },
    ];

    await Twitter.saveAccounts(accountsData);

    logger.info("Twitter authentication completed successfully", {
      requestId: req.requestId,
      userId,
      twitterUserId: twitterUserInfo.id,
      twitterUsername: twitterUserInfo.username,
    });

    // Redirect to frontend with success
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = `${frontendUrl}/post-management/create-social-post?tab=4&twitter_connected=true`;

    res.redirect(redirectUrl);
  } catch (error) {
    logger.error("Error in Twitter callback", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    // Redirect to frontend with error
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = `${frontendUrl}/post-management/create-social-post?tab=4&twitter_error=true`;

    res.redirect(redirectUrl);
  }
};

/**
 * Validate Twitter callback (for AJAX calls)
 */
const callbackValidation = async (req, res) => {
  try {
    const { code, state } = req.body;

    logger.logControllerAction("twitter", "callbackValidation", req.requestId, {
      hasCode: !!code,
      state,
    });

    if (!code || !state) {
      return res.status(400).json({
        success: false,
        message: "Authorization code and state are required",
        data: null,
      });
    }

    // Parse state to get userId
    const stateParts = state.split("-");
    if (stateParts.length < 2) {
      return res.status(400).json({
        success: false,
        message: "Invalid state parameter",
        data: null,
      });
    }

    const userId = parseInt(stateParts[0]);

    // Exchange code for access token
    const tokenData = await twitterService.exchangeCodeForToken(code, state);

    // Get user information
    const twitterUserInfo = await twitterService.getUserInfo(
      tokenData.access_token
    );

    res.json({
      success: true,
      message: "Twitter authentication successful",
      data: {
        user: twitterUserInfo,
        tokenData: {
          hasAccessToken: !!tokenData.access_token,
          expiresIn: tokenData.expires_in,
        },
      },
    });
  } catch (error) {
    logger.error("Error in Twitter callback validation", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      success: false,
      message: "Twitter authentication failed",
      error: error.message,
    });
  }
};

/**
 * Get Twitter accounts for user
 */
const getTwitterAccounts = async (req, res) => {
  try {
    const { userId } = req.params;

    logger.logControllerAction("twitter", "getTwitterAccounts", req.requestId, {
      userId,
    });

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
        data: null,
      });
    }

    const accounts = await Twitter.getAccounts(parseInt(userId));

    logger.info("Twitter accounts retrieved successfully", {
      requestId: req.requestId,
      userId,
      accountCount: accounts.length,
    });

    res.json({
      success: true,
      message: "Twitter accounts retrieved successfully",
      data: accounts,
    });
  } catch (error) {
    logger.error("Error getting Twitter accounts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
      userId: req.params.userId,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve Twitter accounts",
      error: error.message,
    });
  }
};

/**
 * Create Twitter post
 */
const createPost = async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      accountId,
      text,
      mediaUrls,
      replyTo,
      quoteTweetId,
      scheduledPublishTime,
    } = req.body;

    logger.logControllerAction("twitter", "createPost", req.requestId, {
      userId,
      accountId,
      textLength: text?.length || 0,
      mediaCount: mediaUrls?.length || 0,
    });

    if (!userId || !accountId || !text) {
      return res.status(400).json({
        success: false,
        message: "User ID, Account ID, and text are required",
        data: null,
      });
    }

    // Get Twitter account and access token
    const accounts = await Twitter.getAccounts(parseInt(userId));
    const account = accounts.find((acc) => acc.account_id === accountId);

    if (!account) {
      return res.status(404).json({
        success: false,
        message: "Twitter account not found",
        data: null,
      });
    }

    // Prepare tweet data
    const tweetData = {
      text: text,
      reply_to: replyTo,
      quote_tweet_id: quoteTweetId,
    };

    // Handle media uploads if provided
    if (mediaUrls && mediaUrls.length > 0) {
      // Note: In a real implementation, you would need to download the media
      // from the provided URLs and upload them to Twitter
      // For now, we'll store the URLs and handle upload separately
      tweetData.media_urls = mediaUrls;
    }

    // Create tweet
    const twitterPostResponse = await twitterService.createTweet(
      account.access_token,
      tweetData
    );

    // Generate Twitter URL
    const twitterUrl = twitterService.generatePostUrl(
      account.account_username,
      twitterPostResponse.id
    );

    // Save post to database
    const saveResult = await Twitter.savePost({
      userId: parseInt(userId),
      accountId: accountId,
      twitterPostId: twitterPostResponse.id,
      postContent: req.body,
      postResponse: twitterPostResponse,
      tweetText: text,
      mediaUrls: mediaUrls || [],
      hashtags: extractHashtags(text),
      mentions: extractMentions(text),
      replyToTweetId: replyTo || null,
      quoteTweetId: quoteTweetId || null,
      published: !scheduledPublishTime,
      scheduledPublishTime: scheduledPublishTime || null,
      status: scheduledPublishTime ? "scheduled" : "published",
      twitterUrl: twitterUrl,
    });

    logger.info("Twitter post created successfully", {
      requestId: req.requestId,
      userId,
      accountId,
      twitterPostId: twitterPostResponse.id,
      twitterUrl,
    });

    res.json({
      success: true,
      message: "Twitter post created successfully",
      data: {
        twitterPostId: twitterPostResponse.id,
        twitterUrl: twitterUrl,
        postResponse: twitterPostResponse,
        saveResult: saveResult,
      },
    });
  } catch (error) {
    logger.error("Error creating Twitter post", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
      userId: req.params.userId,
    });

    res.status(500).json({
      success: false,
      message: "Failed to create Twitter post",
      error: error.message,
    });
  }
};

/**
 * Get Twitter posts for user
 */
const getPosts = async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    logger.logControllerAction("twitter", "getPosts", req.requestId, {
      userId,
      limit,
      offset,
    });

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
        data: null,
      });
    }

    const posts = await Twitter.getPosts(
      parseInt(userId),
      parseInt(limit),
      parseInt(offset)
    );

    logger.info("Twitter posts retrieved successfully", {
      requestId: req.requestId,
      userId,
      postCount: posts.length,
    });

    res.json({
      success: true,
      message: "Twitter posts retrieved successfully",
      data: posts,
    });
  } catch (error) {
    logger.error("Error getting Twitter posts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
      userId: req.params.userId,
    });

    res.status(500).json({
      success: false,
      message: "Failed to retrieve Twitter posts",
      error: error.message,
    });
  }
};

/**
 * Helper function to extract hashtags from text
 */
function extractHashtags(text) {
  const hashtagRegex = /#[a-zA-Z0-9_]+/g;
  return text.match(hashtagRegex) || [];
}

/**
 * Helper function to extract mentions from text
 */
function extractMentions(text) {
  const mentionRegex = /@[a-zA-Z0-9_]+/g;
  return text.match(mentionRegex) || [];
}

module.exports = {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getTwitterAccounts,
  createPost,
  getPosts,
};
