import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import {
  FACEBOOK_AUTHENTICATE,
  FACEBOOK_GET_PAGES,
  FACEBOOK_CREATE_POST,
  FACEBOOK_GET_POSTS,
} from "../../constants/endPoints.constant";

class FacebookService {
  _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  /**
   * Initiate Facebook authentication
   */
  async authenticate(userId: number): Promise<any> {
    try {
      return await this._httpHelperService.post(FACEBOOK_AUTHENTICATE, {
        userId,
      });
    } catch (error: any) {
      console.error("Error authenticating with Facebook:", error);
      throw error;
    }
  }

  /**
   * Get Facebook pages for user
   */
  async getPages(userId: number): Promise<any> {
    try {
      return await this._httpHelperService.get(FACEBOOK_GET_PAGES(userId));
    } catch (error: any) {
      console.error("Error fetching Facebook pages:", error);
      throw error;
    }
  }

  /**
   * Create Facebook post
   */
  async createPost(userId: number, postData: any): Promise<any> {
    try {
      return await this._httpHelperService.post(
        FACEBOOK_CREATE_POST(userId),
        postData
      );
    } catch (error: any) {
      console.error("Error creating Facebook post:", error);
      throw error;
    }
  }

  /**
   * Get Facebook posts
   */
  async getPosts(
    userId: number,
    pageId?: string,
    page: number = 1,
    limit: number = 10
  ): Promise<any> {
    try {
      const params = { page, limit };
      if (pageId) {
        (params as any).pageId = pageId;
      }

      return await this._httpHelperService.get(FACEBOOK_GET_POSTS(userId), {
        params,
      });
    } catch (error: any) {
      console.error("Error fetching Facebook posts:", error);
      throw error;
    }
  }
}

export default FacebookService;
