const database = require("../config/database");
const logger = require("../utils/logger");

class AnalyticsSyncModel {
  /**
   * Create analytics tables if they don't exist
   */
  static async createAnalyticsTables() {
    try {
      logger.logAnalyticsSync("CREATE_TABLES_START", {
        message: "Creating analytics tables if they don't exist",
      });

      // Create location_analytics_daily table
      const createDailyTable = `
        CREATE TABLE IF NOT EXISTS location_analytics_daily (
          id bigint(20) NOT NULL AUTO_INCREMENT,
          gmb_location_id varchar(255) NOT NULL,
          gmb_account_id varchar(255) NOT NULL,
          metric_date date NOT NULL,
          metric_type varchar(100) NOT NULL,
          metric_value bigint(20) DEFAULT 0,
          created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY unique_location_date_metric (gmb_location_id, metric_date, metric_type),
          KEY idx_location_date (gmb_location_id, metric_date),
          KEY idx_account_date (gmb_account_id, metric_date),
          KEY idx_metric_type (metric_type),
          KEY idx_date_range (metric_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;

      await database.pool.execute(createDailyTable);

      // Create location_analytics_sync_log table
      const createSyncLogTable = `
        CREATE TABLE IF NOT EXISTS location_analytics_sync_log (
          id bigint(20) NOT NULL AUTO_INCREMENT,
          gmb_location_id varchar(255) NOT NULL,
          gmb_account_id varchar(255) NOT NULL,
          sync_date date NOT NULL,
          start_date date NOT NULL,
          end_date date NOT NULL,
          status enum('pending', 'success', 'failed', 'partial') NOT NULL DEFAULT 'pending',
          metrics_synced int(11) DEFAULT 0,
          error_message text,
          api_calls_made int(11) DEFAULT 0,
          sync_duration_seconds int(11) DEFAULT 0,
          created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          KEY idx_location_sync_date (gmb_location_id, sync_date),
          KEY idx_status (status),
          KEY idx_sync_date (sync_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;

      await database.pool.execute(createSyncLogTable);

      // Create location_analytics_config table
      const createConfigTable = `
        CREATE TABLE IF NOT EXISTS location_analytics_config (
          id bigint(20) NOT NULL AUTO_INCREMENT,
          gmb_location_id varchar(255) NOT NULL,
          gmb_account_id varchar(255) NOT NULL,
          sync_enabled tinyint(1) NOT NULL DEFAULT 1,
          last_sync_date date NULL,
          sync_frequency_days int(11) NOT NULL DEFAULT 1,
          retention_days int(11) NOT NULL DEFAULT 365,
          created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (id),
          UNIQUE KEY unique_location_config (gmb_location_id),
          KEY idx_sync_enabled (sync_enabled),
          KEY idx_last_sync (last_sync_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;

      await database.pool.execute(createConfigTable);

      logger.logAnalyticsSync("CREATE_TABLES_COMPLETE", {
        message: "Analytics tables created successfully",
      });

      return true;
    } catch (error) {
      logger.error("Error creating analytics tables:", error);
      throw error;
    }
  }

  /**
   * Initialize analytics configuration for existing locations
   */
  static async initializeAnalyticsConfig() {
    try {
      logger.logAnalyticsSync("INIT_CONFIG_START", {
        message: "Initializing analytics configuration for existing locations",
      });

      const initQuery = `
        INSERT IGNORE INTO location_analytics_config 
        (gmb_location_id, gmb_account_id, sync_enabled, sync_frequency_days, retention_days)
        SELECT 
          gmbLocationId, 
          gmbAccountId, 
          1, 
          1, 
          365
        FROM gmb_locations 
        WHERE statusId = 1
      `;

      const [result] = await database.pool.execute(initQuery);

      logger.logAnalyticsSync("INIT_CONFIG_COMPLETE", {
        affectedRows: result.affectedRows,
        message: `Initialized analytics config for ${result.affectedRows} locations`,
      });

      return result.affectedRows;
    } catch (error) {
      logger.error("Error initializing analytics config:", error);
      throw error;
    }
  }

  /**
   * Get analytics sync statistics
   */
  static async getAnalyticsStats() {
    try {
      // Get total locations with analytics data
      const [totalLocationsResult] = await database.pool.execute(`
        SELECT COUNT(DISTINCT gmb_location_id) as total_locations
        FROM location_analytics_daily
      `);

      // Get data for last 30 days
      const [last30DaysResult] = await database.pool.execute(`
        SELECT COUNT(*) as metrics_count
        FROM location_analytics_daily
        WHERE metric_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      `);

      // Get latest sync information
      const [latestSyncResult] = await database.pool.execute(`
        SELECT 
          MAX(sync_date) as latest_sync_date,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_syncs,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_syncs,
          COUNT(*) as total_syncs
        FROM location_analytics_sync_log
        WHERE sync_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      `);

      // Get locations that need sync
      const [needsSyncResult] = await database.pool.execute(`
        SELECT COUNT(DISTINCT gl.gmbLocationId) as locations_need_sync
        FROM gmb_locations gl
        LEFT JOIN location_analytics_config lac ON gl.gmbLocationId = lac.gmb_location_id
        LEFT JOIN gmb_oauth_tokens got ON gl.gmbAccountId = got.gmbAccountId
        WHERE gl.statusId = 1 
        AND got.statusId = 1
        AND got.accessToken IS NOT NULL
        AND (lac.sync_enabled IS NULL OR lac.sync_enabled = 1)
        AND (
          lac.last_sync_date IS NULL 
          OR lac.last_sync_date < DATE_SUB(CURDATE(), INTERVAL COALESCE(lac.sync_frequency_days, 1) DAY)
        )
      `);

      return {
        totalLocationsWithData: totalLocationsResult[0].total_locations || 0,
        metricsLast30Days: last30DaysResult[0].metrics_count || 0,
        latestSyncDate: latestSyncResult[0].latest_sync_date,
        successfulSyncsLast7Days: latestSyncResult[0].successful_syncs || 0,
        failedSyncsLast7Days: latestSyncResult[0].failed_syncs || 0,
        totalSyncsLast7Days: latestSyncResult[0].total_syncs || 0,
        locationsThatNeedSync: needsSyncResult[0].locations_need_sync || 0,
      };
    } catch (error) {
      logger.error("Error getting analytics stats:", error);
      throw error;
    }
  }

  /**
   * Get recent sync logs
   */
  static async getRecentSyncLogs(limit = 10) {
    try {
      const query = `
        SELECT 
          lasl.*,
          gl.gmbLocationName
        FROM location_analytics_sync_log lasl
        LEFT JOIN gmb_locations gl ON lasl.gmb_location_id = gl.gmbLocationId
        ORDER BY lasl.created_at DESC
        LIMIT ?
      `;

      const [rows] = await database.pool.execute(query, [limit]);
      return rows;
    } catch (error) {
      logger.error("Error getting recent sync logs:", error);
      throw error;
    }
  }

  /**
   * Get analytics data summary by location
   */
  static async getLocationAnalyticsSummary() {
    try {
      const query = `
        SELECT 
          lad.gmb_location_id,
          gl.gmbLocationName,
          COUNT(*) as total_metrics,
          MIN(lad.metric_date) as earliest_date,
          MAX(lad.metric_date) as latest_date,
          lac.last_sync_date,
          lac.sync_enabled
        FROM location_analytics_daily lad
        LEFT JOIN gmb_locations gl ON lad.gmb_location_id = gl.gmbLocationId
        LEFT JOIN location_analytics_config lac ON lad.gmb_location_id = lac.gmb_location_id
        GROUP BY lad.gmb_location_id, gl.gmbLocationName, lac.last_sync_date, lac.sync_enabled
        ORDER BY lac.last_sync_date DESC, total_metrics DESC
      `;

      const [rows] = await database.pool.execute(query);
      return rows;
    } catch (error) {
      logger.error("Error getting location analytics summary:", error);
      throw error;
    }
  }

  /**
   * Update analytics sync configuration for a location
   */
  static async updateSyncConfig(gmbLocationId, config) {
    try {
      const setParts = [];
      const values = [];

      if (config.syncEnabled !== undefined) {
        setParts.push("sync_enabled = ?");
        values.push(config.syncEnabled ? 1 : 0);
      }
      if (config.syncFrequencyDays !== undefined) {
        setParts.push("sync_frequency_days = ?");
        values.push(config.syncFrequencyDays);
      }
      if (config.retentionDays !== undefined) {
        setParts.push("retention_days = ?");
        values.push(config.retentionDays);
      }

      if (setParts.length === 0) {
        return { affectedRows: 0 };
      }

      setParts.push("updated_at = CURRENT_TIMESTAMP");
      values.push(gmbLocationId);

      const query = `
        UPDATE location_analytics_config 
        SET ${setParts.join(", ")} 
        WHERE gmb_location_id = ?
      `;

      const [result] = await database.pool.execute(query, values);
      return result;
    } catch (error) {
      logger.error("Error updating sync config:", error);
      throw error;
    }
  }
}

module.exports = AnalyticsSyncModel;
