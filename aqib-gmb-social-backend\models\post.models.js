const pool = require("../config/db");
const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");

module.exports = class Posts {
  static async saveSchedules(requestObj) {
    try {
      const results = await pool.query(
        "INSERT INTO access_token(userId, accessToken) VALUES (?, ?)",
        [requestObj.userId, requestObj.token]
      );
      return results;
    } catch (error) {
      console.error("Database query failed:", error);
      throw error;
    }
  }

  /**
   * Save a post to database
   */
  static async savePost(postData) {
    try {
      const {
        userId,
        businessId,
        locationId,
        accountId,
        googlePostName,
        bulkPostId,
        isBulkPost,
        postContent,
        postResponse,
        summary,
        topicType,
        languageCode,
        state,
        searchUrl,
      } = postData;

      const query = `
        INSERT INTO gmb_posts
        (user_id, business_id, location_id, account_id, google_post_name,
         bulk_post_id, is_bulk_post, post_content, post_response, summary,
         topic_type, language_code, state, search_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        userId,
        businessId,
        locationId,
        accountId,
        googlePostName,
        bulkPostId,
        isBulkPost,
        JSON.stringify(postContent),
        JSON.stringify(postResponse),
        summary,
        topicType,
        languageCode,
        state,
        searchUrl,
      ];

      const result = await pool.query(query, values);
      return { success: true, insertId: result.insertId };
    } catch (error) {
      console.error("Error saving post:", error);
      throw error;
    }
  }

  /**
   * Get post by Google post name
   */
  static async getPostByGoogleName(googlePostName) {
    try {
      const query = `
        SELECT * FROM gmb_posts
        WHERE google_post_name = ?
      `;

      const results = await pool.query(query, [googlePostName]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error("Error getting post by Google name:", error);
      throw error;
    }
  }

  /**
   * Get posts by bulk post ID
   */
  static async getPostsByBulkId(bulkPostId) {
    try {
      const query = `
        SELECT p.*, l.gmbLocationName, l.gmbLocationId
        FROM gmb_posts p
        LEFT JOIN gmb_locations l ON p.location_id = l.gmbLocationId
        WHERE p.bulk_post_id = ?
        ORDER BY p.created_at ASC
      `;

      const results = await pool.query(query, [bulkPostId]);
      return results;
    } catch (error) {
      console.error("Error getting posts by bulk ID:", error);
      throw error;
    }
  }

  /**
   * Check if a post is part of a bulk post
   */
  static async checkBulkPostStatus(googlePostName) {
    try {
      const query = `
        SELECT
          is_bulk_post,
          bulk_post_id,
          (SELECT COUNT(*) FROM gmb_posts WHERE bulk_post_id = p.bulk_post_id) as total_posts
        FROM gmb_posts p
        WHERE google_post_name = ?
      `;

      const results = await pool.query(query, [googlePostName]);

      if (results.length > 0) {
        const post = results[0];
        return {
          isBulkPost: post.is_bulk_post,
          bulkPostId: post.bulk_post_id,
          totalPosts: post.total_posts,
        };
      }

      return null;
    } catch (error) {
      console.error("Error checking bulk post status:", error);
      throw error;
    }
  }

  /**
   * Generate a new bulk post ID
   */
  static generateBulkPostId() {
    return uuidv4();
  }

  /**
   * Update a post in database
   */
  static async updatePost(
    googlePostName,
    updateData,
    userId,
    ipAddress = null,
    userAgent = null
  ) {
    try {
      // First get the current post data for logging
      const currentPost = await this.getPostByGoogleName(googlePostName);
      if (!currentPost) {
        throw new Error("Post not found");
      }

      const {
        summary,
        topicType,
        languageCode,
        postContent,
        postResponse,
        state,
        searchUrl,
        bulkPostId,
        isBulkPost,
        callToAction,
        event,
        offer,
        media,
      } = updateData;

      // Build dynamic update query
      const updateFields = [];
      const updateValues = [];

      if (summary !== undefined) {
        updateFields.push("summary = ?");
        updateValues.push(summary);
      }
      if (topicType !== undefined) {
        updateFields.push("topic_type = ?");
        updateValues.push(topicType);
      }
      if (languageCode !== undefined) {
        updateFields.push("language_code = ?");
        updateValues.push(languageCode);
      }
      if (postContent !== undefined) {
        updateFields.push("post_content = ?");
        updateValues.push(JSON.stringify(postContent));
      }
      if (postResponse !== undefined) {
        updateFields.push("post_response = ?");
        updateValues.push(JSON.stringify(postResponse));
      }
      if (state !== undefined) {
        updateFields.push("state = ?");
        updateValues.push(state);
      }
      if (searchUrl !== undefined) {
        updateFields.push("search_url = ?");
        updateValues.push(searchUrl);
      }
      if (bulkPostId !== undefined) {
        updateFields.push("bulk_post_id = ?");
        updateValues.push(bulkPostId);
      }
      if (isBulkPost !== undefined) {
        updateFields.push("is_bulk_post = ?");
        updateValues.push(isBulkPost);
      }

      // Add updated timestamp
      updateFields.push("updated_at = CURRENT_TIMESTAMP");

      if (updateFields.length === 1) {
        // Only timestamp update
        throw new Error("No fields to update");
      }

      const query = `
        UPDATE gmb_posts
        SET ${updateFields.join(", ")}
        WHERE google_post_name = ?
      `;

      updateValues.push(googlePostName);

      const result = await pool.query(query, updateValues);

      if (result.affectedRows === 0) {
        throw new Error("No post was updated");
      }

      // Log the update
      await this.logPostAction({
        postId: currentPost.id,
        googlePostName: googlePostName,
        userId: userId,
        businessId: currentPost.business_id,
        locationId: currentPost.location_id,
        accountId: currentPost.account_id,
        actionType: "UPDATE",
        oldContent: currentPost.post_content,
        newContent: postContent
          ? JSON.stringify(postContent)
          : currentPost.post_content,
        oldSummary: currentPost.summary,
        newSummary: summary || currentPost.summary,
        oldTopicType: currentPost.topic_type,
        newTopicType: topicType || currentPost.topic_type,
        oldState: currentPost.state,
        newState: state || currentPost.state,
        oldBulkPostId: currentPost.bulk_post_id,
        newBulkPostId:
          bulkPostId !== undefined ? bulkPostId : currentPost.bulk_post_id,
        oldIsBulkPost: currentPost.is_bulk_post,
        newIsBulkPost:
          isBulkPost !== undefined ? isBulkPost : currentPost.is_bulk_post,
        changesMade: updateData,
        ipAddress: ipAddress,
        userAgent: userAgent,
      });

      return { success: true, affectedRows: result.affectedRows };
    } catch (error) {
      console.error("Error updating post:", error);
      throw error;
    }
  }

  /**
   * Log post actions for audit trail
   */
  static async logPostAction(logData) {
    try {
      const {
        postId,
        googlePostName,
        userId,
        businessId,
        locationId,
        accountId,
        actionType,
        oldContent,
        newContent,
        oldSummary,
        newSummary,
        oldTopicType,
        newTopicType,
        oldState,
        newState,
        oldBulkPostId,
        newBulkPostId,
        oldIsBulkPost,
        newIsBulkPost,
        changesMade,
        ipAddress,
        userAgent,
        notes,
      } = logData;

      const query = `
        INSERT INTO gmb_posts_log
        (post_id, google_post_name, user_id, business_id, location_id, account_id,
         action_type, old_content, new_content, old_summary, new_summary,
         old_topic_type, new_topic_type, old_state, new_state,
         old_bulk_post_id, new_bulk_post_id, old_is_bulk_post, new_is_bulk_post,
         changes_made, ip_address, user_agent, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        postId,
        googlePostName,
        userId,
        businessId,
        locationId,
        accountId,
        actionType,
        oldContent,
        newContent,
        oldSummary,
        newSummary,
        oldTopicType,
        newTopicType,
        oldState,
        newState,
        oldBulkPostId,
        newBulkPostId,
        oldIsBulkPost,
        newIsBulkPost,
        JSON.stringify(changesMade),
        ipAddress,
        userAgent,
        notes,
      ];

      const result = await pool.query(query, values);
      return { success: true, insertId: result.insertId };
    } catch (error) {
      console.error("Error logging post action:", error);
      throw error;
    }
  }

  /**
   * Get post logs by post ID or Google post name
   */
  static async getPostLogs(identifier, type = "google_post_name", limit = 50) {
    try {
      const whereClause =
        type === "post_id" ? "post_id = ?" : "google_post_name = ?";

      const query = `
        SELECT
          l.*,
          u.firstName,
          u.lastName,
          u.email as user_email,
          b.businessName
        FROM gmb_posts_log l
        LEFT JOIN users u ON l.user_id = u.id
        LEFT JOIN business b ON l.business_id = b.id
        WHERE ${whereClause}
        ORDER BY l.created_at DESC
        LIMIT ?
      `;

      const results = await pool.query(query, [identifier, limit]);
      return results;
    } catch (error) {
      console.error("Error getting post logs:", error);
      throw error;
    }
  }

  /**
   * Update multiple posts by bulk post ID
   */
  static async updateBulkPosts(
    bulkPostId,
    updateData,
    userId,
    ipAddress = null,
    userAgent = null
  ) {
    try {
      // First get all posts in the bulk group
      const bulkPosts = await this.getPostsByBulkId(bulkPostId);

      if (bulkPosts.length === 0) {
        throw new Error("No posts found in bulk group");
      }

      const updateResults = [];
      const errors = [];

      // Update each post in the bulk group
      for (const post of bulkPosts) {
        try {
          const result = await this.updatePost(
            post.google_post_name,
            updateData,
            userId,
            ipAddress,
            userAgent
          );
          updateResults.push({
            googlePostName: post.google_post_name,
            locationName: post.gmbLocationName,
            success: result.success,
            affectedRows: result.affectedRows,
          });
        } catch (error) {
          console.error(`Error updating post ${post.google_post_name}:`, error);
          errors.push({
            googlePostName: post.google_post_name,
            locationName: post.gmbLocationName,
            error: error.message,
          });
        }
      }

      return {
        success: true,
        totalPosts: bulkPosts.length,
        successfulUpdates: updateResults.length,
        failedUpdates: errors.length,
        results: updateResults,
        errors: errors,
      };
    } catch (error) {
      console.error("Error updating bulk posts:", error);
      throw error;
    }
  }

  /**
   * Get post by Google post name with location info
   */
  static async getPostWithLocationInfo(googlePostName) {
    try {
      const query = `
        SELECT p.*, l.gmbLocationName, l.gmbLocationId, a.gmbAccountName
        FROM gmb_posts p
        LEFT JOIN gmb_locations l ON p.location_id = l.gmbLocationId
        LEFT JOIN gmb_accounts a ON p.account_id = a.gmbAccountId
        WHERE p.google_post_name = ?
      `;

      const results = await pool.query(query, [googlePostName]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error("Error getting post with location info:", error);
      throw error;
    }
  }
};
