import { Component, Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import {
  GMB_CALLBACK,
  GOOGLE_AUTHENTICATION,
  LIST_OF_BUSINESS,
  FACEBOOK_CALLBACK,
} from "../../constants/endPoints.constant";
import { Action } from "redux";

class AuthService {
  _httpHelperService;
  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  googleAuthenticate = async (formData: any) => {
    return await this._httpHelperService.post(GOOGLE_AUTHENTICATION, formData);
  };

  gmbcallback = async (code: any, state: any, userId: number) => {
    try {
      const result = await this._httpHelperService.post(GMB_CALLBACK, {
        code,
        state,
        userId,
      });
      return result;
    } catch (error) {
      return error;
    }
  };

  facebookCallback = async (code: any, state: any, userId: number) => {
    try {
      const result = await this._httpHelperService.post(FACEBOOK_CALLBACK, {
        code,
        state,
        userId,
      });
      return result;
    } catch (error) {
      return error;
    }
  };
}

export default AuthService;
