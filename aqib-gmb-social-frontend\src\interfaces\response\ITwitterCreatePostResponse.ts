export interface ITwitterAccountData {
  id: number;
  account_id: string;
  account_name: string;
  account_username: string;
  account_description?: string;
  account_picture_url?: string;
  followers_count: number;
  following_count: number;
  tweet_count: number;
  is_verified: boolean;
  is_active: boolean;
  twitter_username: string;
  twitter_user_name: string;
  access_token: string;
  created_at: string;
  updated_at: string;
}

export interface ITwitterCreatePostResponse {
  success: boolean;
  message: string;
  data: {
    twitterPostId: string;
    twitterUrl: string;
    postResponse: {
      id: string;
      text: string;
      edit_history_tweet_ids: string[];
    };
    saveResult: {
      success: boolean;
      result: {
        insertId: number;
        affectedRows: number;
      };
    };
  };
}

export interface ITwitterPostData {
  id: number;
  user_id: number;
  business_id: number;
  account_id: string;
  twitter_post_id: string;
  post_content: any;
  post_response: any;
  tweet_text: string;
  media_urls: string[];
  hashtags: string[];
  mentions: string[];
  reply_to_tweet_id?: string;
  quote_tweet_id?: string;
  published: boolean;
  scheduled_publish_time?: string;
  status: "draft" | "scheduled" | "published" | "failed";
  twitter_url: string;
  retweet_count: number;
  like_count: number;
  reply_count: number;
  quote_count: number;
  impression_count: number;
  created_at: string;
  updated_at: string;
  account_name: string;
  account_username: string;
}

export interface ITwitterAuthResponse {
  success: boolean;
  message: string;
  data: {
    authUrl: string;
    state: string;
  };
}

export interface ITwitterCallbackResponse {
  success: boolean;
  message: string;
  data: {
    user: {
      id: string;
      name: string;
      username: string;
      description?: string;
      profile_image_url?: string;
      public_metrics: {
        followers_count: number;
        following_count: number;
        tweet_count: number;
        listed_count: number;
      };
      verified: boolean;
    };
    tokenData: {
      hasAccessToken: boolean;
      expiresIn: number;
    };
  };
}
