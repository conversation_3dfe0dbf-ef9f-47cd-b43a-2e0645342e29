import React, { useContext, useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import { ToastContext } from "../../context/toast.context";
import { Grid2 } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import Grid from "@mui/material/Grid";
import Chip from "@mui/material/Chip";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { IDatabaseOperationResponseModel } from "../../interfaces/response/IDatabaseOperationResponseModel";
import ReviewService from "../../services/review/review.service";
import {
  ITags,
  ITagsResponseModel,
} from "../../interfaces/response/ITagsResponseModel";
import { IReviewsResponse } from "../../interfaces/response/IReviewsListResponseModel";
import { IUpdateTagToReviewRequestModel } from "../../interfaces/request/IUpdateTagToReviewRequestModel";
import { LoadingContext } from "../../context/loading.context";
import { MessageConstants } from "../../constants/message.constant";
import { theme } from "../../theme";

const CreateTagsComponent = (props: {
  review: IReviewsResponse;
  closeDrawer: () => null | void | undefined;
}) => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const _reviewService = new ReviewService(dispatch);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const [tagName, setTagName] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [tags, setTags] = useState<ITags[]>([]);
  const { setLoading } = useContext(LoadingContext);
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    // Allow only letters, numbers, and spaces
    const sanitizedValue = inputValue.replace(/[^a-zA-Z0-9 ]/g, "");
    if (sanitizedValue.length <= 20) {
      setTagName(sanitizedValue);

      // Set error messages based on input length
      if (sanitizedValue.length < 4) {
        setError("Minimum 4 characters required");
      } else {
        setError(""); // Clear error when valid
      }
    } else {
      setError("Maximum 20 characters allowed");
    }
  };

  useEffect(() => {
    getAllTags();
  }, []);

  const createTag = async () => {
    try {
      if (
        !tags.some((tag) => tag.tagName.toLowerCase() === tagName.toLowerCase())
      ) {
        const createTag: IDatabaseOperationResponseModel =
          await _reviewService.createReviewTags(tagName, userInfo.id);
        setTagName("");
        getAllTags();
      } else {
        setToastConfig(ToastSeverity.Error, "Tag already exists.", true);
      }
    } catch (error) {}
  };

  const getAllTags = async () => {
    try {
      // setLoading(true);
      const allTagsResponse: ITagsResponseModel =
        await _reviewService.getAllTags();

      if (props.review.reviewTags) {
        const selectedReviewTags = props.review.reviewTags
          .toLowerCase()
          .split(",");
        const filteredTags = allTagsResponse.filter(
          (x) => Boolean(x.isActive) == true
        );
        const selectedFilteredTags = filteredTags.map((x) => {
          return selectedReviewTags.includes(x.tagName.toLowerCase())
            ? { ...x, selected: true }
            : { ...x };
        });
        setTags(selectedFilteredTags);
      } else {
        setTags(allTagsResponse.filter((x) => Boolean(x.isActive) == true));
      }
    } catch (error) {
    } finally {
      // setLoading(false);
    }
  };

  const updateTagsToReview = async () => {
    try {
      setLoading(true);
      const request: IUpdateTagToReviewRequestModel = {
        tagNames: tags
          .filter((x) => x.selected)
          .map((x) => x.tagName)
          .join(","),
        reviewId: props.review.id,
      };

      const updateTagsToReviewResponse: IDatabaseOperationResponseModel =
        await _reviewService.updateTagsToReview(request);
      setToastConfig(ToastSeverity.Success, "Tags Updated Successfully", true);
      props.closeDrawer();
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box className="commonModal">
      <Typography
        id="modal-modal-title"
        variant="h6"
        component="h2"
        className="modal-modal-title"
      >
        Add Review Tags
      </Typography>

      <Box id="modal-modal-description" className="modal-modal-description">
        <Box>
          <Grid container>
            <Grid item xs={12}>
              <Box className="commonInput">
                <TextField
                  id="outlined-basic"
                  label="Type Custom Services"
                  variant="outlined"
                  fullWidth
                  value={tagName}
                  onChange={handleInputChange}
                  helperText={error}
                />

                {tagName && (
                  <Button
                    onClick={createTag}
                    fullWidth
                    variant="contained"
                    color="primary"
                    style={{ marginTop: 10, textTransform: "none" }}
                    disabled={tags.some(
                      (tag) =>
                        tag.tagName.toLowerCase() === tagName.toLowerCase()
                    )}
                  >
                    + Add {tagName}
                  </Button>
                )}
              </Box>
            </Grid>
            <Grid item xs={12}>
              <h3>Select Tags</h3>
              <Box>
                <Stack direction="row">
                  <Grid2
                    container
                    rowSpacing={1} // Adds spacing between the items
                    sx={{
                      flexWrap: "wrap", // Ensures items wrap to the next row
                    }}
                  >
                    {tags.length > 0 &&
                      tags.map((tag: ITags) => (
                        <Grid2 key={tag.id}>
                          <Chip
                            label={tag.tagName}
                            onClick={() => {
                              const updatedItems = tags.map((item) =>
                                item.id === tag.id
                                  ? { ...item, selected: !tag.selected }
                                  : item
                              );

                              setTags(updatedItems);
                            }}
                            className="tagChips"
                            color={tag.selected ? "primary" : "default"} // Change color when selected
                            sx={{
                              border: tag.selected
                                ? "1px solid " + theme.palette.primary?.main
                                : "1px solid #ccc", // Optional border
                              fontWeight: tag.selected ? "bold" : "normal", // Change style when selected
                            }}
                          />
                        </Grid2>
                      ))}
                  </Grid2>
                </Stack>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box className="">
        <Stack direction="row" className="commonFooter">
          <Button
            onClick={props.closeDrawer}
            variant="outlined"
            className="secondaryOutlineBtn"
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            className="primaryFillBtn"
            onClick={() => updateTagsToReview()}
            disabled={tags.filter((x) => x.selected).length === 0}
          >
            Add{" "}
            <span className="chipTagCount">
              {tags.filter((x) => x.selected).length}
            </span>{" "}
            Tag
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default CreateTagsComponent;
