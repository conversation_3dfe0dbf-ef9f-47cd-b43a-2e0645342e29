# GMB Auto Reply Service

A Windows service for automated Google My Business review replies based on configured templates and business rules.

## Overview

This service automatically processes Google My Business reviews and sends appropriate replies based on:
- Star rating filters
- Business hours restrictions
- Configurable delay settings
- Pre-defined reply templates
- Business-specific configurations

## Features

- **Windows Service Integration**: Runs as a background Windows service
- **Scheduled Processing**: Configurable cron-based scheduling (default: every 5 minutes)
- **Database Integration**: Uses existing MySQL database from the main application
- **Auto Reply Logic**: Respects business hours, delay settings, and star rating filters
- **Template System**: Uses pre-configured reply templates for different star ratings
- **Comprehensive Logging**: Detailed logging with rotation and different log levels
- **Error Handling**: Robust error handling with retry mechanisms
- **Health Monitoring**: Built-in health checks and status reporting

## Prerequisites

- Windows operating system
- Node.js 16.0.0 or higher
- Administrator privileges (for service installation)
- Access to the MySQL database used by the main GMB application

## Installation

1. **Clone or copy the service files** to your desired directory
2. **Install dependencies**:
   ```bash
   npm install
   ```
3. **Configure environment variables** in `.env` file (see Configuration section)
4. **Install as Windows service** (run as Administrator):
   ```bash
   npm run install-service
   ```

## Configuration

### Environment Variables (.env)

```env
# Database Configuration
DB_HOST=your-database-host
DB_USER=your-database-user
DB_PASSWORD=your-database-password
DB_NAME=your-database-name
DB_PORT=3306

# Service Configuration
SERVICE_NAME=GMB-AutoReply-Service
SERVICE_DESCRIPTION=Automated Google My Business Review Reply Service
LOG_LEVEL=info

# Scheduling Configuration
CRON_SCHEDULE=*/5 * * * *
# Check for new reviews every 5 minutes

# Auto Reply Configuration
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_MINUTES=10
BATCH_SIZE=50
```

### Database Requirements

The service requires the following database tables (created automatically if missing):

- `auto_reply_settings` - Business auto-reply configurations
- `reply_templates` - Reply templates for different star ratings
- `business_reply_templates` - Template-to-business mappings
- `gmb_reviews` - Google My Business reviews
- `auto_reply_log` - Auto-reply processing log (created by service)

## Usage

### Service Management

**Start the service**:
```bash
sc start "GMB-AutoReply-Service"
```

**Stop the service**:
```bash
sc stop "GMB-AutoReply-Service"
```

**Check service status**:
```bash
sc query "GMB-AutoReply-Service"
```

**Uninstall the service**:
```bash
npm run uninstall-service
```

### Development Mode

For development and testing:
```bash
npm run dev
```

### Manual Execution

To run without installing as a service:
```bash
npm start
```

## How It Works

1. **Scheduled Execution**: The service runs on a configurable schedule (default: every 5 minutes)

2. **Business Processing**: For each business with auto-reply enabled:
   - Checks if current time is within business hours (if configured)
   - Retrieves pending reviews based on star rating filters and delay settings

3. **Review Processing**: For each pending review:
   - Finds appropriate reply template based on star rating
   - Generates personalized reply content
   - Posts reply to Google My Business (via API)
   - Logs the attempt and result

4. **Error Handling**: Failed attempts are logged and can be retried based on configuration

## Logging

Logs are stored in the `./logs` directory:

- `combined.log` - All log entries
- `error.log` - Error-level entries only
- Console output (in development mode)

Log levels: `error`, `warn`, `info`, `debug`

## Auto Reply Configuration

Auto-reply settings are managed through the main application's web interface and stored in the `auto_reply_settings` table:

- `is_enabled` - Enable/disable auto-reply for the business
- `enabled_star_ratings` - Array of star ratings to process (1-5)
- `delay_minutes` - Minimum delay before replying to a review
- `only_business_hours` - Restrict replies to business hours only
- `business_hours_start` - Business hours start time (HH:mm:ss)
- `business_hours_end` - Business hours end time (HH:mm:ss)

## Reply Templates

Reply templates are configured through the main application and stored in the `reply_templates` table:

- Templates are associated with specific star ratings (1-5)
- Templates can include placeholders like `{reviewer_name}`, `{business_name}`, etc.
- Templates are mapped to businesses via the `business_reply_templates` table

## Monitoring

The service provides health check information including:
- Service status and uptime
- Processing statistics
- Memory usage
- Database connection status
- Last processing results

## Troubleshooting

### Common Issues

1. **Service won't start**:
   - Check if running as Administrator
   - Verify database connection settings
   - Check logs for specific error messages

2. **Database connection errors**:
   - Verify database credentials in `.env` file
   - Ensure database server is accessible
   - Check firewall settings

3. **No reviews being processed**:
   - Verify auto-reply settings are enabled for businesses
   - Check if reviews meet the delay and star rating criteria
   - Verify reply templates exist for the star ratings

### Log Analysis

Check the logs in `./logs/` directory for detailed information about:
- Service startup and shutdown
- Database operations
- Review processing attempts
- Error messages and stack traces

## Security Considerations

- Store database credentials securely
- Run the service with minimal required privileges
- Regularly monitor logs for suspicious activity
- Keep Node.js and dependencies updated

## Support

For issues and questions:
1. Check the logs for error messages
2. Verify configuration settings
3. Ensure database connectivity
4. Review the troubleshooting section above
