# My Loco Biz Frontend

This project has been migrated from Create React App to [Vite](https://vitejs.dev/) for improved performance and faster development experience.

## Available Scripts

In the project directory, you can run:

### `npm start` or `npm run dev`

Runs the app in development mode using Vite.\
Open [http://localhost:5173](http://localhost:5173) to view it in the browser.

The page will reload instantly when you make edits thanks to Vite's HMR (Hot Module Replacement).\
You will also see any lint errors in the console.

### `npm test`

Launches the test runner using Vitest.\
Tests run in watch mode by default for a great development experience.

### `npm run build`

Builds the app for production to the `build` folder using Vite.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

### `npm run preview`

Serves the production build locally for testing before deployment.

## Migration from Create React App to Vite

This project has been successfully migrated from Create React App to Vite. Key changes include:

- **Build Tool**: Replaced react-scripts with Vite for faster builds and development
- **Configuration**: Added `vite.config.ts` and `vitest.config.ts`
- **Environment Variables**: Still uses `REACT_APP_` prefix (configured in Vite)
- **Testing**: Migrated from Jest to Vitest
- **TypeScript**: Updated configuration for better Vite compatibility
- **HTML Template**: Moved from `public/index.html` to root `index.html`

### Benefits of the Migration

- ⚡ **Faster Development**: Instant server start and lightning-fast HMR
- 🚀 **Faster Builds**: Significantly reduced build times
- 📦 **Better Tree Shaking**: More efficient bundle optimization
- 🔧 **Modern Tooling**: Built on top of Rollup and esbuild
- 🎯 **Better TypeScript Support**: Native TypeScript support without additional configuration

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).
