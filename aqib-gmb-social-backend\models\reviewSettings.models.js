const pool = require("../config/db");
const RoleType = require("../constants/dbConstants");

module.exports = class ReviewSettings {
  // Get all reply templates for a user with location-based filtering
  // LocationId is unique and sufficient for filtering
  static async getReplyTemplates(
    userId,
    businessId = null,
    accountId = null,
    locationId = null
  ) {
    try {
      let query, params;

      if (locationId) {
        // If locationId is provided, use it as the primary filter (locationId is unique)
        // Get location-specific templates + fallback to account/business level templates
        query = `
          SELECT
            rt.*,
            brt.business_id,
            brt.account_id,
            brt.location_id,
            brt.is_active as business_template_active,
            gl.gmbLocationName,
            ga.accountName,
            b.businessName
          FROM reply_templates rt
          LEFT JOIN business_reply_templates brt ON rt.id = brt.template_id
          LEFT JOIN gmb_locations gl ON brt.location_id = gl.gmbLocationId
          LEFT JOIN gmb_accounts ga ON brt.account_id = ga.accountId
          LEFT JOIN gmb_businesses_master b ON brt.business_id = b.id
          WHERE rt.created_by = ?
            AND (
              brt.location_id = ? OR
              (brt.location_id IS NULL AND brt.account_id = (
                SELECT gmbAccountId FROM gmb_locations WHERE gmbLocationId = ?
              )) OR
              (brt.location_id IS NULL AND brt.account_id IS NULL AND brt.business_id = (
                SELECT b2.id FROM gmb_businesses_master b2
                JOIN gmb_accounts ga2 ON b2.id = ga2.businessId
                JOIN gmb_locations gl2 ON ga2.accountId = gl2.gmbAccountId
                WHERE gl2.gmbLocationId = ?
              ))
            )
          ORDER BY
            CASE
              WHEN brt.location_id IS NOT NULL THEN 1
              WHEN brt.account_id IS NOT NULL THEN 2
              WHEN brt.business_id IS NOT NULL THEN 3
              ELSE 4
            END,
            rt.star_rating,
            rt.created_at DESC
        `;
        params = [userId, locationId, locationId, locationId];
      } else if (accountId) {
        // If only accountId is provided
        query = `
          SELECT
            rt.*,
            brt.business_id,
            brt.account_id,
            brt.location_id,
            brt.is_active as business_template_active,
            ga.accountName,
            b.businessName
          FROM reply_templates rt
          LEFT JOIN business_reply_templates brt ON rt.id = brt.template_id
          LEFT JOIN gmb_accounts ga ON brt.account_id = ga.accountId
          LEFT JOIN gmb_businesses_master b ON brt.business_id = b.id
          WHERE rt.created_by = ?
            AND (brt.account_id = ? OR (brt.account_id IS NULL AND brt.business_id = (
              SELECT businessId FROM gmb_accounts WHERE accountId = ?
            )))
          ORDER BY brt.account_id DESC, brt.business_id DESC, rt.star_rating, rt.created_at DESC
        `;
        params = [userId, accountId, accountId];
      } else if (businessId) {
        // If only businessId is provided
        query = `
          SELECT
            rt.*,
            brt.business_id,
            brt.account_id,
            brt.location_id,
            brt.is_active as business_template_active,
            b.businessName
          FROM reply_templates rt
          LEFT JOIN business_reply_templates brt ON rt.id = brt.template_id
          LEFT JOIN gmb_businesses_master b ON brt.business_id = b.id
          WHERE rt.created_by = ?
            AND (brt.business_id = ? OR brt.business_id IS NULL)
          ORDER BY brt.business_id DESC, rt.star_rating, rt.created_at DESC
        `;
        params = [userId, businessId];
      } else {
        // No filters - return all templates for the user
        query = `
          SELECT
            rt.*,
            brt.business_id,
            brt.account_id,
            brt.location_id,
            brt.is_active as business_template_active
          FROM reply_templates rt
          LEFT JOIN business_reply_templates brt ON rt.id = brt.template_id
          WHERE rt.created_by = ?
          ORDER BY rt.star_rating, rt.created_at DESC
        `;
        params = [userId];
      }

      console.log("=== GET TEMPLATES QUERY DEBUG ===");
      console.log("LocationId:", locationId);
      console.log("AccountId:", accountId);
      console.log("BusinessId:", businessId);
      console.log("Query:", query);
      console.log("Params:", params);

      const results = await pool.query(query, params);

      console.log("Templates found:", results.length);

      // Convert tinyint fields to boolean
      const processedResults = results.map((template) => ({
        ...template,
        is_default: Boolean(template.is_default),
        business_template_active: Boolean(template.business_template_active),
      }));

      return processedResults;
    } catch (error) {
      console.error("Error fetching reply templates:", error);
      throw error;
    }
  }

  // Create a new reply template
  static async createReplyTemplate(
    templateData,
    accountId = null,
    locationId = null
  ) {
    try {
      const {
        userId,
        starRating,
        templateName,
        templateContent,
        isDefault,
        businessId,
      } = templateData;

      console.log("=== MODEL CREATE TEMPLATE DEBUG ===");
      console.log("Template Data:", templateData);
      console.log("Account ID:", accountId);
      console.log("Location ID:", locationId);

      // If this is set as default, unset other defaults for the same star rating
      if (isDefault) {
        await pool.query(
          `UPDATE reply_templates SET is_default = 0
           WHERE created_by = ? AND star_rating = ?`,
          [userId, starRating]
        );
      }

      const result = await pool.query(
        `INSERT INTO reply_templates
         (created_by, star_rating, template_name, template_content, is_default, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
        [userId, starRating, templateName, templateContent, isDefault ? 1 : 0]
      );

      console.log("Template insert result:", result);

      // Create business-template mapping with account/location
      if (result.insertId) {
        let finalBusinessId = businessId;
        let finalAccountId = accountId;

        // If locationId is provided but businessId/accountId are not, get them from location
        if (locationId && (!businessId || !accountId)) {
          console.log("Getting business and account info from locationId...");
          const locationInfo = await pool.query(
            `SELECT gl.gmbAccountId, ga.businessId
             FROM gmb_locations gl
             JOIN gmb_accounts ga ON gl.gmbAccountId = ga.accountId
             WHERE gl.gmbLocationId = ?`,
            [locationId]
          );

          if (locationInfo.length > 0) {
            finalBusinessId = finalBusinessId || locationInfo[0].businessId;
            finalAccountId = finalAccountId || locationInfo[0].gmbAccountId;
            console.log(
              "Retrieved from location - BusinessId:",
              finalBusinessId,
              "AccountId:",
              finalAccountId
            );
          }
        }

        if (finalBusinessId) {
          console.log("Mapping template to business/account/location...");
          await pool.query(
            `INSERT INTO business_reply_templates (business_id, account_id, location_id, template_id, is_active, created_at)
             VALUES (?, ?, ?, ?, 1, NOW())`,
            [finalBusinessId, finalAccountId, locationId, result.insertId]
          );
          console.log("Template mapping completed");
        }
      }

      return result;
    } catch (error) {
      console.error("Error creating reply template:", error);
      throw error;
    }
  }

  // Update reply template
  static async updateReplyTemplate(templateId, templateData, userId) {
    try {
      const { starRating, templateName, templateContent, isDefault } =
        templateData;

      // If this is set as default, unset other defaults for the same star rating
      if (isDefault) {
        await pool.query(
          `UPDATE reply_templates SET is_default = 0 
           WHERE created_by = ? AND star_rating = ? AND id != ?`,
          [userId, starRating, templateId]
        );
      }

      const result = await pool.query(
        `UPDATE reply_templates 
         SET star_rating = ?, template_name = ?, template_content = ?, 
             is_default = ?, updated_at = NOW()
         WHERE id = ? AND created_by = ?`,
        [
          starRating,
          templateName,
          templateContent,
          isDefault ? 1 : 0,
          templateId,
          userId,
        ]
      );

      return result;
    } catch (error) {
      console.error("Error updating reply template:", error);
      throw error;
    }
  }

  // Delete reply template
  static async deleteReplyTemplate(templateId, userId) {
    try {
      // First delete business mappings
      await pool.query(
        `DELETE FROM business_reply_templates WHERE template_id = ?`,
        [templateId]
      );

      // Then delete the template
      const result = await pool.query(
        `DELETE FROM reply_templates WHERE id = ? AND created_by = ?`,
        [templateId, userId]
      );

      return result;
    } catch (error) {
      console.error("Error deleting reply template:", error);
      throw error;
    }
  }

  // Get auto-reply settings for a business/account/location
  static async getAutoReplySettings(
    businessId,
    accountId = null,
    locationId = null
  ) {
    try {
      let query = `SELECT * FROM auto_reply_settings WHERE business_id = ?`;
      let params = [businessId];

      // Priority: Location-specific > Account-specific > Business-wide
      if (locationId) {
        query += ` AND (location_id = ? OR location_id IS NULL)`;
        params.push(locationId);
        query += ` ORDER BY location_id DESC LIMIT 1`;
      } else if (accountId) {
        query += ` AND (account_id = ? OR account_id IS NULL) AND location_id IS NULL`;
        params.push(accountId);
        query += ` ORDER BY account_id DESC LIMIT 1`;
      } else {
        query += ` AND account_id IS NULL AND location_id IS NULL`;
      }

      const result = await pool.query(query, params);

      if (result.length > 0) {
        const settings = result[0];

        // Handle enabled_star_ratings - it might be a string or already parsed array
        if (settings.enabled_star_ratings) {
          if (typeof settings.enabled_star_ratings === "string") {
            try {
              settings.enabled_star_ratings = JSON.parse(
                settings.enabled_star_ratings
              );
            } catch (parseError) {
              console.error("Error parsing enabled_star_ratings:", parseError);
              settings.enabled_star_ratings = [];
            }
          } else if (Array.isArray(settings.enabled_star_ratings)) {
            // Already an array, no need to parse
          } else {
            // Unknown format, set to empty array
            settings.enabled_star_ratings = [];
          }
        } else {
          settings.enabled_star_ratings = [];
        }

        // Convert boolean fields from tinyint to boolean
        settings.is_enabled = Boolean(settings.is_enabled);
        settings.only_business_hours = Boolean(settings.only_business_hours);
        settings.enable_ai_auto_reply = Boolean(settings.enable_ai_auto_reply);

        return settings;
      }

      // Return default settings when no settings exist
      return {
        business_id: businessId,
        account_id: accountId,
        location_id: locationId,
        is_enabled: false,
        enabled_star_ratings: [],
        delay_minutes: 0,
        only_business_hours: false,
        enable_ai_auto_reply: false,
        business_hours_start: "09:00:00",
        business_hours_end: "17:00:00",
      };
    } catch (error) {
      console.error("Error fetching auto-reply settings:", error);
      throw error;
    }
  }

  // Update auto-reply settings
  static async updateAutoReplySettings(
    businessId,
    settings,
    accountId = null,
    locationId = null
  ) {
    try {
      const {
        isEnabled,
        enabledStarRatings,
        delayMinutes,
        onlyBusinessHours,
        enableAiAutoReply,
        businessHoursStart,
        businessHoursEnd,
      } = settings;

      const result = await pool.query(
        `INSERT INTO auto_reply_settings
         (business_id, account_id, location_id, is_enabled, enabled_star_ratings, delay_minutes,
          only_business_hours, enable_ai_auto_reply, business_hours_start, business_hours_end, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
         ON DUPLICATE KEY UPDATE
         is_enabled = VALUES(is_enabled),
         enabled_star_ratings = VALUES(enabled_star_ratings),
         delay_minutes = VALUES(delay_minutes),
         only_business_hours = VALUES(only_business_hours),
         enable_ai_auto_reply = VALUES(enable_ai_auto_reply),
         business_hours_start = VALUES(business_hours_start),
         business_hours_end = VALUES(business_hours_end),
         updated_at = NOW()`,
        [
          businessId,
          accountId,
          locationId,
          isEnabled ? 1 : 0,
          JSON.stringify(enabledStarRatings),
          delayMinutes,
          onlyBusinessHours ? 1 : 0,
          enableAiAutoReply ? 1 : 0,
          businessHoursStart,
          businessHoursEnd,
        ]
      );

      return result;
    } catch (error) {
      console.error("Error updating auto-reply settings:", error);
      throw error;
    }
  }

  // Map template to business/account/location
  static async mapTemplateToBusinesses(
    templateId,
    businessIds,
    userId,
    accountId = null,
    locationId = null
  ) {
    try {
      // First, remove existing mappings for this template at the same level
      let deleteQuery = `DELETE FROM business_reply_templates WHERE template_id = ?`;
      let deleteParams = [templateId];

      if (locationId) {
        deleteQuery += ` AND location_id = ?`;
        deleteParams.push(locationId);
      } else if (accountId) {
        deleteQuery += ` AND account_id = ? AND location_id IS NULL`;
        deleteParams.push(accountId);
      } else {
        deleteQuery += ` AND account_id IS NULL AND location_id IS NULL`;
      }

      await pool.query(deleteQuery, deleteParams);

      // Add new mappings
      if (businessIds && businessIds.length > 0) {
        const values = businessIds.map((businessId) => [
          businessId,
          accountId,
          locationId,
          templateId,
          1,
        ]);
        await pool.query(
          `INSERT INTO business_reply_templates (business_id, account_id, location_id, template_id, is_active, created_at)
           VALUES ?`,
          [values.map((v) => [...v, new Date()])]
        );
      }

      return { success: true };
    } catch (error) {
      console.error("Error mapping template to businesses:", error);
      throw error;
    }
  }

  // Get template by star rating for auto-reply
  static async getTemplateForAutoReply(businessId, starRating) {
    try {
      const result = await pool.query(
        `SELECT rt.* FROM reply_templates rt
         JOIN business_reply_templates brt ON rt.id = brt.template_id
         WHERE brt.business_id = ? AND rt.star_rating = ? AND brt.is_active = 1
         AND rt.is_default = 1
         LIMIT 1`,
        [businessId, starRating]
      );

      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error("Error fetching template for auto-reply:", error);
      throw error;
    }
  }
};
