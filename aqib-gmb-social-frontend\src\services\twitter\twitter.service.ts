import { Dispatch } from "react";
import HttpHelperService from "../httpHelper.service";
import { Action } from "redux";
import {
  TWITTER_AUTHENTICATE,
  TWITTER_CALLBACK_VALIDATION,
  TWITTER_GET_ACCOUNTS,
  TWITTER_CREATE_POST,
  TWITTER_GET_POSTS,
} from "../../constants/endPoints.constant";

export interface ITwitterAuthResponse {
  success: boolean;
  message: string;
  data: {
    authUrl: string;
    state: string;
  };
}

export interface ITwitterAccount {
  id: number;
  account_id: string;
  account_name: string;
  account_username: string;
  account_description?: string;
  account_picture_url?: string;
  followers_count: number;
  following_count: number;
  tweet_count: number;
  is_verified: boolean;
  is_active: boolean;
  twitter_username: string;
  twitter_user_name: string;
  access_token: string;
}

export interface ITwitterCreatePostRequest {
  accountId: string;
  text: string;
  mediaUrls?: string[];
  replyTo?: string;
  quoteTweetId?: string;
  scheduledPublishTime?: string;
}

export interface ITwitterCreatePostResponse {
  success: boolean;
  message: string;
  data: {
    twitterPostId: string;
    twitterUrl: string;
    postResponse: any;
    saveResult: any;
  };
}

export interface ITwitterPost {
  id: number;
  user_id: number;
  business_id: number;
  account_id: string;
  twitter_post_id: string;
  post_content: any;
  post_response: any;
  tweet_text: string;
  media_urls: string[];
  hashtags: string[];
  mentions: string[];
  reply_to_tweet_id?: string;
  quote_tweet_id?: string;
  published: boolean;
  scheduled_publish_time?: string;
  status: "draft" | "scheduled" | "published" | "failed";
  twitter_url: string;
  retweet_count: number;
  like_count: number;
  reply_count: number;
  quote_count: number;
  impression_count: number;
  created_at: string;
  updated_at: string;
  account_name: string;
  account_username: string;
}

class TwitterService {
  _httpHelperService: HttpHelperService;

  constructor(dispatch: Dispatch<Action>) {
    this._httpHelperService = new HttpHelperService(dispatch);
  }

  /**
   * Initiate Twitter authentication
   */
  async authenticate(userId: number): Promise<any> {
    try {
      console.log("TwitterService.authenticate called with:", { userId });
      const response = await this._httpHelperService.post(
        TWITTER_AUTHENTICATE,
        {
          userId,
        }
      );
      console.log("TwitterService.authenticate response:", response);
      return response;
    } catch (error: any) {
      console.error("Error authenticating with Twitter:", error);
      throw error;
    }
  }

  /**
   * Validate Twitter callback
   */
  async validateCallback(code: string, state: string): Promise<any> {
    try {
      return await this._httpHelperService.post(TWITTER_CALLBACK_VALIDATION, {
        code,
        state,
      });
    } catch (error: any) {
      console.error("Error validating Twitter callback:", error);
      throw error;
    }
  }

  /**
   * Get Twitter accounts for user
   */
  async getAccounts(userId: number): Promise<any> {
    try {
      return await this._httpHelperService.get(TWITTER_GET_ACCOUNTS(userId));
    } catch (error: any) {
      console.error("Error getting Twitter accounts:", error);
      throw error;
    }
  }

  /**
   * Create Twitter post
   */
  async createPost(userId: number, postData: any): Promise<any> {
    try {
      return await this._httpHelperService.post(
        TWITTER_CREATE_POST(userId),
        postData
      );
    } catch (error: any) {
      console.error("Error creating Twitter post:", error);
      throw error;
    }
  }

  /**
   * Get Twitter posts for user
   */
  async getPosts(
    userId: number,
    limit: number = 50,
    offset: number = 0
  ): Promise<any> {
    try {
      return await this._httpHelperService.get(
        `${TWITTER_GET_POSTS(userId)}?limit=${limit}&offset=${offset}`
      );
    } catch (error: any) {
      console.error("Error getting Twitter posts:", error);
      throw error;
    }
  }

  /**
   * Check if user has Twitter connection
   */
  async hasConnection(userId: number): Promise<boolean> {
    try {
      const accounts = await this.getAccounts(userId);
      return accounts.length > 0;
    } catch (error) {
      console.error("Error checking Twitter connection:", error);
      return false;
    }
  }

  /**
   * Open Twitter authentication popup
   */
  openAuthPopup(authUrl: string): Promise<{ code: string; state: string }> {
    return new Promise((resolve, reject) => {
      const popup = window.open(
        authUrl,
        "twitter-auth",
        "width=600,height=600,scrollbars=yes,resizable=yes"
      );

      if (!popup) {
        reject(new Error("Failed to open popup window"));
        return;
      }

      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          reject(new Error("Authentication cancelled"));
        }
      }, 1000);

      // Listen for messages from the popup
      const messageListener = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) {
          return;
        }

        if (event.data.type === "TWITTER_AUTH_SUCCESS") {
          clearInterval(checkClosed);
          window.removeEventListener("message", messageListener);
          popup.close();
          resolve({
            code: event.data.code,
            state: event.data.state,
          });
        } else if (event.data.type === "TWITTER_AUTH_ERROR") {
          clearInterval(checkClosed);
          window.removeEventListener("message", messageListener);
          popup.close();
          reject(new Error(event.data.error || "Authentication failed"));
        }
      };

      window.addEventListener("message", messageListener);
    });
  }
}

export default TwitterService;
