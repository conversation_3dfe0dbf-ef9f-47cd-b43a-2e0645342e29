const cron = require("node-cron");
const autoReplyService = require("./autoReplyService");
const reviewSyncService = require("./reviewSyncService");
const analyticsSyncService = require("./analyticsSyncService");
const logger = require("../utils/logger");

class SchedulerService {
  constructor() {
    this.jobs = new Map();
    this.isRunning = false;
  }

  /**
   * Start the scheduler service
   */
  start() {
    try {
      if (this.isRunning) {
        logger.logScheduler("ALREADY_RUNNING", {
          message: "Scheduler is already running",
        });
        return;
      }

      const cronSchedule = process.env.CRON_SCHEDULE || "*/5 * * * *"; // Default: every 5 minutes

      logger.logScheduler("START", {
        cronSchedule,
        message: "Starting scheduler service",
      });

      // Validate cron expression
      if (!cron.validate(cronSchedule)) {
        throw new Error(`Invalid cron schedule: ${cronSchedule}`);
      }

      // Schedule the auto-reply processing job
      const autoReplyJob = cron.schedule(
        cronSchedule,
        async () => {
          try {
            logger.logScheduler("JOB_START", {
              jobName: "auto-reply-processing",
              timestamp: new Date().toISOString(),
            });

            await autoReplyService.processAutoReplies();

            logger.logScheduler("JOB_COMPLETE", {
              jobName: "auto-reply-processing",
              timestamp: new Date().toISOString(),
              stats: autoReplyService.getStats(),
            });
          } catch (error) {
            logger.error("Error in scheduled auto-reply job:", error);
            logger.logScheduler("JOB_ERROR", {
              jobName: "auto-reply-processing",
              error: error.message,
              timestamp: new Date().toISOString(),
            });
          }
        },
        {
          scheduled: true, // Don't start immediately
          timezone: "UTC",
        }
      );

      // Store the job reference
      this.jobs.set("auto-reply", autoReplyJob);

      // Start the job
      autoReplyJob.start();

      // Schedule the review sync job (daily at 11:00 PM UTC)
      const reviewSyncSchedule =
        process.env.REVIEW_SYNC_SCHEDULE || "0 23 * * *";
      const reviewSyncJob = cron.schedule(
        reviewSyncSchedule, // 11:00 PM UTC daily by default
        async () => {
          try {
            logger.logScheduler("JOB_START", {
              jobName: "review-sync",
              timestamp: new Date().toISOString(),
            });

            await reviewSyncService.syncAllReviews();

            logger.logScheduler("JOB_COMPLETE", {
              jobName: "review-sync",
              timestamp: new Date().toISOString(),
              stats: reviewSyncService.getStats(),
            });
          } catch (error) {
            logger.error("Error in scheduled review sync job:", error);
            logger.logScheduler("JOB_ERROR", {
              jobName: "review-sync",
              error: error.message,
              timestamp: new Date().toISOString(),
            });
          }
        },
        {
          scheduled: true,
          timezone: "UTC",
        }
      );

      // Store the review sync job reference
      this.jobs.set("review-sync", reviewSyncJob);

      // Start the review sync job
      reviewSyncJob.start();

      // Schedule the analytics sync job (daily at 1:00 AM UTC)
      const analyticsSyncSchedule =
        process.env.ANALYTICS_SYNC_SCHEDULE || "0 0 1 * * *";
      const analyticsSyncJob = cron.schedule(
        analyticsSyncSchedule, // 1:00 AM UTC daily by default
        async () => {
          try {
            logger.logScheduler("JOB_START", {
              jobName: "analytics-sync",
              timestamp: new Date().toISOString(),
            });

            await analyticsSyncService.syncAllAnalytics();

            logger.logScheduler("JOB_COMPLETE", {
              jobName: "analytics-sync",
              timestamp: new Date().toISOString(),
              stats: analyticsSyncService.getStats(),
            });
          } catch (error) {
            logger.error("Error in scheduled analytics sync job:", error);
            logger.logScheduler("JOB_ERROR", {
              jobName: "analytics-sync",
              error: error.message,
              timestamp: new Date().toISOString(),
            });
          }
        },
        {
          scheduled: true,
          timezone: "UTC",
        }
      );

      // Store the analytics sync job reference
      this.jobs.set("analytics-sync", analyticsSyncJob);

      // Start the analytics sync job
      analyticsSyncJob.start();

      this.isRunning = true;

      logger.logScheduler("STARTED", {
        cronSchedule,
        jobCount: this.jobs.size,
        message: "Scheduler service started successfully",
      });

      // Schedule a health check job (every hour)
      this.scheduleHealthCheck();
    } catch (error) {
      logger.error("Error starting scheduler service:", error);
      throw error;
    }
  }

  /**
   * Stop the scheduler service
   */
  stop() {
    try {
      if (!this.isRunning) {
        logger.logScheduler("NOT_RUNNING", {
          message: "Scheduler is not running",
        });
        return;
      }

      logger.logScheduler("STOP", { message: "Stopping scheduler service" });

      // Stop all jobs
      for (const [jobName, job] of this.jobs) {
        job.stop();
        logger.logScheduler("JOB_STOPPED", { jobName });
      }

      // Clear jobs map
      this.jobs.clear();
      this.isRunning = false;

      logger.logScheduler("STOPPED", {
        message: "Scheduler service stopped successfully",
      });
    } catch (error) {
      logger.error("Error stopping scheduler service:", error);
      throw error;
    }
  }

  /**
   * Schedule health check job
   */
  scheduleHealthCheck() {
    try {
      const healthCheckJob = cron.schedule(
        "0 * * * *",
        () => {
          // Every hour
          try {
            logger.logScheduler("HEALTH_CHECK", {
              timestamp: new Date().toISOString(),
              isRunning: this.isRunning,
              activeJobs: this.jobs.size,
              autoReplyStats: autoReplyService.getStats(),
              memoryUsage: process.memoryUsage(),
              uptime: process.uptime(),
            });
          } catch (error) {
            logger.error("Error in health check:", error);
          }
        },
        {
          scheduled: false,
          timezone: "UTC",
        }
      );

      this.jobs.set("health-check", healthCheckJob);
      healthCheckJob.start();

      logger.logScheduler("HEALTH_CHECK_SCHEDULED", {
        message: "Health check job scheduled successfully",
      });
    } catch (error) {
      logger.error("Error scheduling health check:", error);
    }
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeJobs: Array.from(this.jobs.keys()),
      jobCount: this.jobs.size,
      autoReplyStats: autoReplyService.getStats(),
      reviewSyncStats: reviewSyncService.getStats(),
      analyticsSyncStats: analyticsSyncService.getStats(),
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    };
  }

  /**
   * Manually trigger auto-reply processing
   */
  async triggerAutoReplyProcessing() {
    try {
      logger.logScheduler("MANUAL_TRIGGER", {
        message: "Manually triggering auto-reply processing",
      });

      await autoReplyService.processAutoReplies();

      logger.logScheduler("MANUAL_TRIGGER_COMPLETE", {
        stats: autoReplyService.getStats(),
      });

      return autoReplyService.getStats();
    } catch (error) {
      logger.error("Error in manual trigger:", error);
      throw error;
    }
  }

  /**
   * Manually trigger review sync processing
   */
  async triggerReviewSync() {
    try {
      logger.logScheduler("MANUAL_TRIGGER", {
        message: "Manually triggering review sync processing",
      });

      await reviewSyncService.syncAllReviews();

      logger.logScheduler("MANUAL_TRIGGER_COMPLETE", {
        stats: reviewSyncService.getStats(),
      });

      return reviewSyncService.getStats();
    } catch (error) {
      logger.error("Error in manual review sync trigger:", error);
      throw error;
    }
  }

  /**
   * Manually trigger analytics sync processing
   */
  async triggerAnalyticsSync() {
    try {
      logger.logScheduler("MANUAL_TRIGGER", {
        message: "Manually triggering analytics sync processing",
      });

      await analyticsSyncService.syncAllAnalytics();

      logger.logScheduler("MANUAL_TRIGGER_COMPLETE", {
        stats: analyticsSyncService.getStats(),
      });

      return analyticsSyncService.getStats();
    } catch (error) {
      logger.error("Error in manual analytics sync trigger:", error);
      throw error;
    }
  }

  /**
   * Update cron schedule (requires restart)
   */
  updateSchedule(newCronSchedule) {
    try {
      if (!cron.validate(newCronSchedule)) {
        throw new Error(`Invalid cron schedule: ${newCronSchedule}`);
      }

      logger.logScheduler("SCHEDULE_UPDATE", {
        oldSchedule: process.env.CRON_SCHEDULE,
        newSchedule: newCronSchedule,
      });

      // Update environment variable
      process.env.CRON_SCHEDULE = newCronSchedule;

      // Restart scheduler to apply new schedule
      this.stop();
      this.start();

      logger.logScheduler("SCHEDULE_UPDATED", {
        newSchedule: newCronSchedule,
        message: "Schedule updated and scheduler restarted",
      });
    } catch (error) {
      logger.error("Error updating schedule:", error);
      throw error;
    }
  }
}

module.exports = new SchedulerService();
