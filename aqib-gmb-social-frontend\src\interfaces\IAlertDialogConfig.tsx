import { ToastSeverity } from "../constants/toastSeverity.constant";

export interface IAlertDialogConfig {
  title?: string;
  description?: string;
  toastSeverity?: ToastSeverity;
  buttonTitle?: string;
  isShow: boolean;
  data?: any;
  callBack: () => void | undefined;
}

export interface IAlertDialogConfigV1 {
  title?: string;
  description?: string;
  toastSeverity?: ToastSeverity;
  buttonTitle?: string;
  isShow: boolean;
}
