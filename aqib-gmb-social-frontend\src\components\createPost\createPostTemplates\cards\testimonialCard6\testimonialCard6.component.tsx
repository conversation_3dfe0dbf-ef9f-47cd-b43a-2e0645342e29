import React from "react";
import {
  Box,
  Typography,
  Avatar,
  Rating,
  Container,
  CssBaseline,
} from "@mui/material";
import { IPostTemplateConfig } from "../../../../../types/IPostTemplateConfig";
import { ref } from "yup";
import StarIcon from "@mui/icons-material/Star";
import UserAvatar from "../../../../userAvatar/userAvatar.component";
import RatingsStar from "../../../../ratingsStar/ratingsStar.component";

//Css Import
import "../testimonialCard6/testimonialCard6.component.style.css";

// Image import
import backgroundImage from "../../../../../assets/feedbackBackgrouns/6.png";

const TestimonialCard6 = (props: {
  templateConfig: IPostTemplateConfig;
  divRef: any;
}) => {
  return (
    <Box
      ref={props.divRef}
      sx={{
        background: `${props.templateConfig.backgroundColor} url(${backgroundImage}) no-repeat center/cover`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        color: "#fff",
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        border: 1,
        borderColor: "black",
      }}
    >
      <Container>
        <CssBaseline />
        <Box>
          {/* Main Content */}
          {/* Avatar and Name */}
          <Box>
            {/* Rating */}
            <Box className="marB20">
              {/* Rating */}
              <Box className="marB20">
                {props.templateConfig.showRating && (
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      mb: 2,
                    }}
                  >
                    <RatingsStar starRating={props.templateConfig.starRating} />
                  </Box>
                )}
              </Box>
            </Box>
            {/* Review Text */}
            <Typography variant="body2" color="text.secondary" mb={2}>
              {props.templateConfig.comment}
            </Typography>
            <Box className="testmonialUserInfo">
              <Box>
                {props.templateConfig.showAvatar && (
                  <UserAvatar
                    profileImage={props.templateConfig.reviewerImage}
                    fullname={props.templateConfig.reviewerName}
                    style={{
                      width: 60,
                      height: 60,
                      margin: "0 auto 10px",
                      background: "linear-gradient(45deg, #42A5F5, #64B5F6)",
                    }}
                  />
                )}
              </Box>
              <Box>
                <Typography className="testmonialUserName">
                  {props.templateConfig.reviewerName}
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default TestimonialCard6;
