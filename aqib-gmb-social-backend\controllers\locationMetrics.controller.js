const GMB_ACTIONS = require("../constants/gmb-actions");
const logger = require("../utils/logger");
const { reqGMBApi } = require("../services/gmb.service");
const LocationAnalytics = require("../models/locationMetrics.models");

const refreshLocationMetrics = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  const { startDate, endDate } = req.body;

  try {
    logger.info("Fetching location metrics from database", {
      locationId,
      accountId,
      startDate,
      endDate,
    });

    // First try to get data from database
    const dbData = await LocationAnalytics.getFormattedAnalyticsData(
      [locationId],
      startDate,
      endDate
    );

    // Check if we have data for this location
    if (
      dbData[locationId] &&
      dbData[locationId].multiDailyMetricTimeSeries[0].dailyMetricTimeSeries
        .length > 0
    ) {
      logger.info("Analytics data found in database", {
        locationId,
        metricsCount:
          dbData[locationId].multiDailyMetricTimeSeries[0].dailyMetricTimeSeries
            .length,
      });

      res.status(200).json({
        message: "Locations Metric fetched from database",
        data: dbData[locationId],
        source: "database",
      });
      return;
    }

    // If no data in database, fall back to Google API
    logger.info("No data found in database, falling back to Google API", {
      locationId,
    });

    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_locationMetrics,
      reqBodyData: {
        locationId: locationId,
        accountId: accountId,
        startDateRange: {
          year: startDate.split("-")[0],
          month: startDate.split("-")[1],
          day: startDate.split("-")[2],
        },
        endDateRange: {
          year: endDate.split("-")[0],
          month: endDate.split("-")[1],
          day: endDate.split("-")[2],
        },
      },
    });

    if (result.success) {
      logger.info("Analytics data fetched from Google API", { locationId });

      res.status(200).json({
        message: "Locations Metric fetched from Google API",
        data: { ...result.data },
        source: "google_api",
      });
    } else {
      logger.error("Failed to fetch from Google API", {
        locationId,
        status: result.status,
        error: result.data,
      });

      res.status(result.status).json({
        message: "Failed to fetch Locations Metric",
        data: result.data,
      });
    }
  } catch (error) {
    logger.error("Error in refreshLocationMetrics", {
      locationId,
      accountId,
      error: error.message,
      stack: error.stack,
    });

    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

const getSearchkeywords = async (req, res) => {
  const accountId = req.headers["x-gmb-account-id"];
  const locationId = req.headers["x-gmb-location-id"];
  const { startDate, endDate, pageToken } = req.body;

  try {
    const result = await reqGMBApi({
      req,
      action: GMB_ACTIONS.get_searchkeywords,
      reqBodyData: {
        locationId: locationId,
        accountId: accountId,
        startDateRange: {
          year: startDate.split("-")[0],
          month: startDate.split("-")[1],
          day: startDate.split("-")[2],
        },
        endDateRange: {
          year: endDate.split("-")[0],
          month: endDate.split("-")[1],
          day: endDate.split("-")[2],
        },
        pageToken: pageToken,
      },
    });
    if (result.success) {
      res.status(200).json({
        message: "Search Keywords fetched",
        data: { ...result.data },
      });
    } else {
      res.status(result.status).json({
        message: "Failed to fetch Locations Metric",
        data: result.data,
      });
    }
  } catch (error) {
    res
      .status(500)
      .json({ message: "Internal Server Error", error: error.message });
  }
};

/**
 * Get analytics data for multiple locations from database
 * This endpoint is optimized for the analytics dashboard that needs data for multiple locations
 */
const getMultiLocationAnalytics = async (req, res) => {
  const { locationIds, startDate, endDate } = req.body;

  try {
    // Validate input
    if (
      !locationIds ||
      !Array.isArray(locationIds) ||
      locationIds.length === 0
    ) {
      return res.status(400).json({
        message: "locationIds array is required and cannot be empty",
      });
    }

    if (!startDate || !endDate) {
      return res.status(400).json({
        message: "startDate and endDate are required",
      });
    }

    logger.info("Fetching multi-location analytics from database", {
      locationIds,
      startDate,
      endDate,
      locationCount: locationIds.length,
    });

    // Get data from database for all locations
    const dbData = await LocationAnalytics.getFormattedAnalyticsData(
      locationIds,
      startDate,
      endDate
    );

    // Check which locations have data and which don't
    const locationsWithData = Object.keys(dbData);
    const locationsWithoutData = locationIds.filter(
      (id) => !locationsWithData.includes(id)
    );

    logger.info("Multi-location analytics query results", {
      totalRequested: locationIds.length,
      foundInDatabase: locationsWithData.length,
      missingFromDatabase: locationsWithoutData.length,
      locationsWithoutData,
    });

    // Transform the data to match the expected frontend format
    const responseData = {
      multiDailyMetricTimeSeries: [],
      totalLocations: locationIds.length,
      processedLocations: locationsWithData.length,
      locationsWithData: locationsWithData,
      locationsWithoutData: locationsWithoutData,
      source: "database",
    };

    // If we have data for at least one location, aggregate it
    if (locationsWithData.length > 0) {
      // Aggregate all metrics from all locations
      const aggregatedMetrics = {};

      Object.values(dbData).forEach((locationData) => {
        if (
          locationData.multiDailyMetricTimeSeries &&
          locationData.multiDailyMetricTimeSeries[0]
        ) {
          locationData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries.forEach(
            (metricSeries) => {
              const metricType = metricSeries.dailyMetric;

              if (!aggregatedMetrics[metricType]) {
                aggregatedMetrics[metricType] = {
                  dailyMetric: metricType,
                  timeSeries: {
                    datedValues: [],
                  },
                };
              }

              // Merge dated values
              metricSeries.timeSeries.datedValues.forEach((datedValue) => {
                const existingValue = aggregatedMetrics[
                  metricType
                ].timeSeries.datedValues.find(
                  (existing) =>
                    existing.date.year === datedValue.date.year &&
                    existing.date.month === datedValue.date.month &&
                    existing.date.day === datedValue.date.day
                );

                if (existingValue) {
                  // Aggregate the values for the same date
                  const currentValue = parseInt(existingValue.value || "0", 10);
                  const newValue = parseInt(datedValue.value || "0", 10);
                  existingValue.value = (currentValue + newValue).toString();
                } else {
                  // Add new date entry
                  aggregatedMetrics[metricType].timeSeries.datedValues.push({
                    date: { ...datedValue.date },
                    value: datedValue.value || "0",
                  });
                }
              });
            }
          );
        }
      });

      // Convert aggregated metrics to array and sort dated values
      responseData.multiDailyMetricTimeSeries = [
        {
          dailyMetricTimeSeries: Object.values(aggregatedMetrics).map(
            (metric) => {
              // Sort dated values by date
              metric.timeSeries.datedValues.sort((a, b) => {
                const dateA = new Date(
                  a.date.year,
                  a.date.month - 1,
                  a.date.day
                );
                const dateB = new Date(
                  b.date.year,
                  b.date.month - 1,
                  b.date.day
                );
                return dateA - dateB;
              });
              return metric;
            }
          ),
        },
      ];
    }

    res.status(200).json({
      message: `Analytics data retrieved for ${locationsWithData.length} of ${locationIds.length} locations`,
      data: responseData,
    });
  } catch (error) {
    logger.error("Error in getMultiLocationAnalytics", {
      locationIds,
      startDate,
      endDate,
      error: error.message,
      stack: error.stack,
    });

    res.status(500).json({
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

module.exports = {
  refreshLocationMetrics,
  getSearchkeywords,
  getMultiLocationAnalytics,
};
