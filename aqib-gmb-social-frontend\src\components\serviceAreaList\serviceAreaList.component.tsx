import React from "react";
import { Box, Chip, Typography } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

interface PlaceInfo {
  placeName: string;
  placeId: string;
}

interface ServiceAreaProps {
  placeInfos: PlaceInfo[];
  onRemove?: (placeId: string) => void;
  editable?: boolean;
}

const ServiceAreaList: React.FC<ServiceAreaProps> = ({
  placeInfos,
  onRemove,
  editable = false,
}) => {
  const handleRemove = (placeId: string) => {
    if (onRemove) {
      onRemove(placeId);
    }
  };

  if (!placeInfos || placeInfos.length === 0) {
    return (
      <Typography
        color="text.secondary"
        sx={{ fontStyle: "italic", fontSize: "0.85rem", p: 1 }}
      >
        No service areas added
      </Typography>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        flexWrap: "wrap",
        gap: 1,
        p: 1,
      }}
    >
      {placeInfos.map((place, index) => (
        <Chip
          key={place.placeId || index}
          label={place.placeName}
          onDelete={editable ? () => handleRemove(place.placeId) : undefined}
          deleteIcon={editable ? <CloseIcon /> : undefined}
          sx={{
            bgcolor: "#309898",
            color: "#ffffff",
            borderRadius: "16px",
            fontWeight: 500,
            fontSize: "0.875rem",
            "& .MuiChip-deleteIcon": {
              color: "#ffffff",
              "&:hover": {
                color: "#f0f0f0",
              },
            },
            "&:hover": {
              bgcolor: "#267373",
            },
          }}
        />
      ))}
    </Box>
  );
};

export default ServiceAreaList;
