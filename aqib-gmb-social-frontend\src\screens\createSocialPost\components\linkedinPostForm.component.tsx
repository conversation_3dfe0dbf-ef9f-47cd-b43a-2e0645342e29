import React, { useState, useEffect } from "react";
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Chip,
  Avatar,
  SelectChangeEvent,
  IconButton,
  Button,
} from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import {
  ILinkedInPageData,
  ILinkedInSelectedProfile,
} from "../../../services/linkedin/linkedin.service";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";

interface ILinkedInCreatePost {
  profileId: string;
  text: string;
  media?: any[];
  published?: boolean;
  scheduledPublishTime?: string;
}

interface LinkedInPostFormProps {
  profiles: ILinkedInPageData[];
  selectedProfiles: ILinkedInSelectedProfile[];
  onSelectedProfilesChange: (profiles: ILinkedInSelectedProfile[]) => void;
  formData: ILinkedInCreatePost;
  onFormDataChange: (data: ILinkedInCreatePost) => void;
  uploadedImages: any[];
  scheduleForLater: boolean;
  onScheduleChange: (schedule: boolean) => void;
  scheduledDate: Dayjs | null;
  onScheduledDateChange: (date: Dayjs | null) => void;
  onImageUpload: () => void;
  onGalleryOpen: () => void;
  onSubmit?: () => void;
  isLinkedInConnected?: boolean;
}

const LinkedInPostForm: React.FC<LinkedInPostFormProps> = ({
  profiles,
  selectedProfiles,
  onSelectedProfilesChange,
  formData,
  onFormDataChange,
  uploadedImages,
  scheduleForLater,
  onScheduleChange,
  scheduledDate,
  onScheduledDateChange,
  onImageUpload,
  onGalleryOpen,
  onSubmit,
  isLinkedInConnected = true,
}) => {
  // Update form data when uploaded images change
  useEffect(() => {
    if (uploadedImages && uploadedImages.length > 0) {
      const mediaData = uploadedImages.map((file: any) => ({
        type: file.type?.startsWith("video/") ? "video" : "image",
        url: file.s3Url || URL.createObjectURL(file),
        title: file.name || "Media",
        description: "",
      }));

      onFormDataChange({
        ...formData,
        media: mediaData,
      });
    } else {
      onFormDataChange({
        ...formData,
        media: [],
      });
    }
  }, [uploadedImages]);

  // Update scheduled publish time when date changes
  useEffect(() => {
    if (scheduleForLater && scheduledDate) {
      onFormDataChange({
        ...formData,
        published: false,
        scheduledPublishTime: scheduledDate.toISOString(),
      });
    } else {
      onFormDataChange({
        ...formData,
        published: true,
        scheduledPublishTime: undefined,
      });
    }
  }, [scheduleForLater, scheduledDate]);

  const handleProfileSelection = (event: SelectChangeEvent<string[]>) => {
    const selectedIds = event.target.value as string[];

    // Update selected profiles based on the selection
    const updatedProfiles = selectedProfiles.map((p) => ({
      ...p,
      selected: selectedIds.includes(p.id),
    }));

    onSelectedProfilesChange(updatedProfiles);

    // Update form data with first selected profile for compatibility
    const firstSelectedId = selectedIds[0];
    if (firstSelectedId) {
      onFormDataChange({
        ...formData,
        profileId: firstSelectedId,
      });
    }
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const text = event.target.value;
    onFormDataChange({
      ...formData,
      text: text,
    });
  };

  const getSelectedProfile = () => {
    return selectedProfiles.find((p) => p.selected);
  };

  const getSelectedProfileIds = () => {
    return selectedProfiles.filter((p) => p.selected).map((p) => p.id);
  };

  return (
    <Box>
      {/* Form Section */}
      <Box>
        {/* Profile Selection */}
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Select LinkedIn Profiles</InputLabel>
          <Select
            multiple
            value={getSelectedProfileIds()}
            label="Select LinkedIn Profiles"
            onChange={handleProfileSelection}
            disabled={profiles.length === 0}
            renderValue={(selected) => (
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                {(selected as string[]).map((profileId) => {
                  const profile = profiles.find((p) => p.id === profileId);
                  return (
                    <Chip
                      key={profileId}
                      label={profile?.name || profileId}
                      size="small"
                      avatar={
                        <Avatar
                          src={profile?.picture}
                          sx={{ width: 20, height: 20 }}
                        >
                          {profile?.name.charAt(0).toUpperCase()}
                        </Avatar>
                      }
                    />
                  );
                })}
              </Box>
            )}
          >
            {profiles.map((profile) => (
              <MenuItem key={profile.id} value={profile.id}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Avatar src={profile.picture} sx={{ width: 24, height: 24 }}>
                    {profile.name.charAt(0).toUpperCase()}
                  </Avatar>
                  {profile.name}
                  {selectedProfiles.find((p) => p.id === profile.id)
                    ?.selected && (
                    <Chip
                      size="small"
                      label="✓"
                      color="primary"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Placeholder Info */}
        {selectedProfiles.filter((p) => p.selected).length > 1 && (
          <Box
            sx={{ mb: 2, p: 2, backgroundColor: "#f5f5f5", borderRadius: 1 }}
          >
            <Typography variant="caption" color="text.secondary">
              💡 Tip: Use {"{Profile Name}"} in your text to automatically
              replace with each profile's name when posting.
            </Typography>
          </Box>
        )}

        {/* Media Upload Section */}
        <Box sx={{ mb: 2 }}>
          <Box
            onClick={onGalleryOpen}
            sx={{
              border: "2px dashed #e0e0e0",
              borderRadius: 2,
              p: 4,
              textAlign: "center",
              cursor: "pointer",
              backgroundColor: "#fafafa",
              transition: "all 0.3s ease",
              "&:hover": {
                borderColor: "#1976d2",
                backgroundColor: "#f5f5f5",
              },
            }}
          >
            <CloudUploadIcon
              sx={{
                fontSize: 48,
                color: "#9e9e9e",
                mb: 1,
              }}
            />
            <Typography variant="h6" sx={{ mb: 0.5, color: "#666" }}>
              Add/Edit Post Media
            </Typography>
            <Typography variant="caption" sx={{ color: "#999" }}>
              Supported: Images (JPG, PNG, GIF, WebP) and Videos (MP4, AVI, MOV,
              WMV, FLV, WebM)
            </Typography>
          </Box>
        </Box>

        {/* Post Text */}
        <TextField
          fullWidth
          multiline
          rows={6}
          label="Post Text"
          placeholder="What do you want to share on LinkedIn?"
          value={formData.text}
          onChange={handleTextChange}
          sx={{ mb: 2 }}
          helperText={`${formData.text.length}/3000 characters`}
          inputProps={{ maxLength: 3000 }}
        />

        {/* Schedule Toggle */}
        <FormControlLabel
          control={
            <Switch
              checked={scheduleForLater}
              onChange={(e) => onScheduleChange(e.target.checked)}
            />
          }
          label="Schedule for later"
          sx={{ mb: 2 }}
        />

        {/* Schedule Date/Time */}
        {scheduleForLater && (
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateTimePicker
              label="Schedule Date & Time"
              value={scheduledDate}
              onChange={onScheduledDateChange}
              minDateTime={dayjs()}
              sx={{ width: "100%", mb: 2 }}
            />
          </LocalizationProvider>
        )}

        {/* Submit Button */}
        {onSubmit && isLinkedInConnected && (
          <Box sx={{ mt: 3 }}>
            <Button
              className="updatesShapeBtn"
              onClick={onSubmit}
              variant="contained"
              style={{ textTransform: "capitalize" }}
              fullWidth
              disabled={selectedProfiles.length === 0}
            >
              {selectedProfiles.length > 1
                ? `Create Posts for ${selectedProfiles.length} Profiles`
                : "Create LinkedIn Post"}
            </Button>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default LinkedInPostForm;
